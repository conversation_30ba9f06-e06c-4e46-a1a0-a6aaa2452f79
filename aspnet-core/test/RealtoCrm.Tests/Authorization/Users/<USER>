using System;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Runtime.Session;
using Abp.Timing;
using Abp.UI;
using RealtoCrm.Authorization.Delegation;
using RealtoCrm.Authorization.Users;
using RealtoCrm.Authorization.Users.Delegation;
using RealtoCrm.Authorization.Users.Delegation.Dto;
using Shouldly;
using Xunit;

namespace RealtoCrm.Tests.Authorization.Users;

public class UserDelegationAppServiceTests : UserDelegationTestBase
{
    private readonly IUserDelegationAppService userDelegationAppService;
    private readonly IUserDelegationConfiguration userDelegationConfiguration;

    // Existing User Delegations
    // admin -> alex
    // george -> admin

    public UserDelegationAppServiceTests()
    {
            this.userDelegationAppService = this.Resolve<IUserDelegationAppService>();
            this.userDelegationConfiguration = this.Resolve<IUserDelegationConfiguration>();
        }

    [Fact]
    public async Task GetActiveUserDelegations_For_Tenant_Tests()
    {
            this.LoginAsDefaultTenantAdmin();

            var delegations = await this.userDelegationAppService.GetActiveUserDelegations();
            delegations.Count.ShouldBe(1);
        }
        
    [MultiTenantFact]
    public async Task GetActiveUserDelegations_For_Host_Tests()
    {
            this.LoginAsHostAdmin();
            var delegations = await this.userDelegationAppService.GetActiveUserDelegations();
            delegations.Count.ShouldBe(0);
        }

    [Fact]
    public async Task DelegateNewUser_Tests()
    {
            if (!this.userDelegationConfiguration.IsEnabled)
            {
                return;
            }

            this.LoginAsDefaultTenantAdmin();

            var delegations = await this.userDelegationAppService.GetDelegatedUsers(new GetUserDelegationsInput
            {
                MaxResultCount = 10
            });

            delegations.TotalCount.ShouldBe(1);

            var george = this.UsingDbContext(context => { return context.Users.FirstOrDefault(u => u.UserName == "george"); });
            await this.userDelegationAppService.DelegateNewUser(new CreateUserDelegationDto
            {
                TargetUserId = george.Id,
                StartTime = Clock.Now,
                EndTime = Clock.Now.AddDays(7)
            });

            delegations = await this.userDelegationAppService.GetDelegatedUsers(new GetUserDelegationsInput
            {
                MaxResultCount = 10
            });

            delegations.TotalCount.ShouldBe(2);
        }

    [Fact]
    public async Task SelfDelegation_Tests()
    {
            if (!this.userDelegationConfiguration.IsEnabled)
            {
                return;
            }

            this.LoginAsDefaultTenantAdmin();

            var exception = await Assert.ThrowsAsync<UserFriendlyException>(async () => await this.userDelegationAppService.DelegateNewUser(new CreateUserDelegationDto
            {
                TargetUserId = this.AbpSession.GetUserId(),
                StartTime = Clock.Now,
                EndTime = Clock.Now.AddDays(7)
            }));

            exception.Message.ShouldBe("You can't delegate authorization to yourself !");
        }

    [Fact]
    public async Task RemoveDelegation_Test()
    {
            if (!this.userDelegationConfiguration.IsEnabled)
            {
                return;
            }

            this.LoginAsDefaultTenantAdmin();

            var delegations = await this.userDelegationAppService.GetDelegatedUsers(new GetUserDelegationsInput
            {
                MaxResultCount = 10
            });

            delegations.TotalCount.ShouldBe(1);

            var delegationId = delegations.Items[0].Id;
            await this.userDelegationAppService.RemoveDelegation(new EntityDto<long>(delegationId));

            delegations = await this.userDelegationAppService.GetDelegatedUsers(new GetUserDelegationsInput
            {
                MaxResultCount = 10
            });

            delegations.TotalCount.ShouldBe(0);
        }

    [Fact]
    public async Task Remove_Different_Users_Delegation_Test()
    {
            if (!this.userDelegationConfiguration.IsEnabled)
            {
                return;
            }

            this.LoginAsDefaultTenantAdmin();

            var differentUsersDelegation = this.UsingDbContext(context =>
            {
                var george = context.Users.FirstOrDefault(e => e.UserName == "george");
                return context.UserDelegations.FirstOrDefault(e => e.SourceUserId == george.Id);
            });
            
            var exception = await Assert.ThrowsAsync<Exception>(async () => await this.userDelegationAppService.RemoveDelegation(new EntityDto<long>(differentUsersDelegation.Id)));
            exception.Message.ShouldBe("Only source user can delete a user delegation !");
        }
}

public abstract class UserDelegationTestBase : AppTestBase
{
    protected UserDelegationTestBase()
    {
            this.CreateTestUsers();
        }

    protected void CreateTestUsers()
    {
            //Note: There is a default "admin" user also

            this.UsingDbContext(
                context =>
                {
                    context.Users.Add(this.CreateUserEntity("alex", "Alex", "Nash", "<EMAIL>"));
                    context.Users.Add(this.CreateUserEntity("george", "George", "Adams", "<EMAIL>"));

                    context.SaveChanges();

                    var george = context.Users.FirstOrDefault(u => u.TenantId == this.AbpSession.TenantId && u.UserName == "george");
                    var alex = context.Users.FirstOrDefault(u => u.TenantId == this.AbpSession.TenantId && u.UserName == "alex");
                    var admin = context.Users.FirstOrDefault(u => u.TenantId == this.AbpSession.TenantId && u.UserName == "admin");

                    context.UserDelegations.Add(new UserDelegation
                    {
                        TenantId = this.AbpSession.TenantId,
                        SourceUserId = admin.Id,
                        TargetUserId = alex.Id,
                        StartTime = Clock.Now,
                        EndTime = Clock.Now.AddDays(7)
                    });

                    context.UserDelegations.Add(new UserDelegation
                    {
                        TenantId = this.AbpSession.TenantId,
                        SourceUserId = george.Id,
                        TargetUserId = admin.Id,
                        StartTime = Clock.Now,
                        EndTime = Clock.Now.AddDays(7)
                    });

                    context.SaveChanges();
                });
        }

    protected User CreateUserEntity(string userName, string name, string surname, string emailAddress)
    {
            var user = new User
            {
                EmailAddress = emailAddress,
                IsEmailConfirmed = true,
                Name = name,
                Surname = surname,
                UserName = userName,
                Password = "AM4OLBpptxBYmM79lGOX9egzZk3vIQU3d/gFCJzaBjAPXzYIK3tQ2N7X4fcrHtElTw==", //123qwe
                TenantId = this.AbpSession.TenantId,
            };

            user.SetNormalizedNames();

            return user;
        }
}