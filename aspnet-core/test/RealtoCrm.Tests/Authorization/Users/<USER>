using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.MultiTenancy;
using RealtoCrm.Authorization;
using RealtoCrm.Authorization.Users;
using Shouldly;
using Xunit;

namespace RealtoCrm.Tests.Authorization.Users;

// ReSharper disable once InconsistentNaming
public class UserAppService_Unlock_Tests : UserAppServiceTestBase
{
    private readonly UserManager userManager;
    private readonly LogInManager loginManager;

    public UserAppService_Unlock_Tests()
    {
        this.userManager = this.Resolve<UserManager>();
        this.loginManager = this.Resolve<LogInManager>();

        this.CreateTestUsers();
    }

    [Fact]
    public async Task Should_Unlock_User()
    {
        //Arrange

        await this.userManager.InitializeOptionsAsync(this.AbpSession.TenantId);
        var user = await this.GetUserByUserNameAsync("jnash");

        //Pre conditions
        (await this.userManager.IsLockedOutAsync(user)).ShouldBeFalse();
        user.IsLockoutEnabled.ShouldBeTrue();

        //Try wrong password until lockout
        AbpLoginResultType loginResultType;
        do
        {
            loginResultType = (await this.loginManager.LoginAsync(user.UserName, "wrong-password", AbpTenantBase.DefaultTenantName)).Result;
        } while (loginResultType != AbpLoginResultType.LockedOut);

        (await this.userManager.IsLockedOutAsync(await this.GetUserByUserNameAsync("jnash"))).ShouldBeTrue();

        //Act

        await this.UserAppService.UnlockUser(user.Id);

        //Assert

        (await this.loginManager.LoginAsync(user.UserName, "wrong-password", AbpTenantBase.DefaultTenantName)).Result.ShouldBe(AbpLoginResultType.InvalidPassword);
    }
}