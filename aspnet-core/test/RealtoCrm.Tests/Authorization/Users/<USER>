using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Shouldly;
using Xunit;

namespace RealtoCrm.Tests.Authorization.Users;

// ReSharper disable once InconsistentNaming
public class UserAppService_Delete_Tests : UserAppServiceTestBase
{
    [Fact]
    public async Task Should_Delete_User()
    {
        //Arrange
        this.CreateTestUsers();

        var user = await this.GetUserByUserNameOrNullAsync("artdent");
        user.ShouldNotBe(null);

        //Act
        await this.UserAppService.DeleteAsync(user.Id);

        //Assert
        user = await this.GetUserByUserNameOrNullAsync("artdent");
        user.IsDeleted.ShouldBe(true);
    }
}