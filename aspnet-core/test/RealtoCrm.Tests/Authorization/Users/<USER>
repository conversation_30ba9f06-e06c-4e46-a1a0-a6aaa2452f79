using System.Collections.Generic;
using Abp.Authorization.Users;
using RealtoCrm.Authorization.Users;
using RealtoCrm.Test.Base;

namespace RealtoCrm.Tests.Authorization.Users;

public abstract class UserAppServiceTestBase : AppTestBase
{
    protected readonly IUserAppService UserAppService;
    protected readonly UserManager UserManager;
        
    protected UserAppServiceTestBase()
    {
        this.UserAppService = this.Resolve<IUserAppService>();
        this.UserManager = this.Resolve<UserManager>();
    }

    protected void CreateTestUsers()
    {
        //Note: There is a default "admin" user also

        this.UsingDbContext(
            context =>
            {
                context.Users.Add(this.CreateUserEntity("jnash", "<PERSON>", "<PERSON>", "<EMAIL>"));
                context.Users.Add(this.CreateUserEntity("adams_d", "<PERSON>", "<PERSON>", "<EMAIL>"));
                context.Users.Add(this.CreateUserEntity("art<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><EMAIL>"));
            });
    }

    protected User CreateUserEntity(string userName, string name, string surname, string emailAddress)
    {
        var user = new User
        {
            EmailAddress = emailAddress,
            IsEmailConfirmed = true,
            Name = name,
            Surname = surname,
            UserName = userName,
            Password = "AM4OLBpptxBYmM79lGOX9egzZk3vIQU3d/gFCJzaBjAPXzYIK3tQ2N7X4fcrHtElTw==", //123qwe
            TenantId = this.AbpSession.TenantId,
            Permissions = new List<UserPermissionSetting>
            {
                new UserPermissionSetting {Name = "test.permission1", IsGranted = true, TenantId = this.AbpSession.TenantId},
                new UserPermissionSetting {Name = "test.permission2", IsGranted = true, TenantId = this.AbpSession.TenantId},
                new UserPermissionSetting {Name = "test.permission3", IsGranted = false, TenantId = this.AbpSession.TenantId},
                new UserPermissionSetting {Name = "test.permission4", IsGranted = false, TenantId = this.AbpSession.TenantId}
            }
        };

        user.SetNormalizedNames();

        return user;
    }
}