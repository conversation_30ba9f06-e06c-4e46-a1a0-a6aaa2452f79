using System.Linq;
using System.Threading.Tasks;
using System.Web;
using Abp;
using Abp.Runtime.Security;
using Castle.MicroKernel.Registration;
using RealtoCrm.Authorization.Accounts;
using RealtoCrm.Authorization.Accounts.Dto;
using RealtoCrm.Authorization.Users;
using RealtoCrm.Url;
using NSubstitute;
using Shouldly;
using Xunit;

namespace RealtoCrm.Tests.Authorization.Accounts;

// ReSharper disable once InconsistentNaming
public class Email_Change_Tests : AppTestBase
{
    [Fact]
    public async Task Should_Change_Email()
    {
        //Arrange

        this.UsingDbContext(context =>
        {
            //Set IsEmailConfirmed to false to provide initial test case
            var currentUser = context.Users.Single(u => u.Id == this.AbpSession.UserId.Value);
            currentUser.IsEmailConfirmed = false;
        });

        var user = await this.GetCurrentUserAsync();
        user.IsEmailConfirmed.ShouldBeFalse();
        
        const string newEmailAddress = "<EMAIL>";
        
        var accountAppService = this.Resolve<IAccountAppService>();
        var userEmailer = this.Resolve<IUserEmailer>();
        var appUrlService = this.Resolve<IAppUrlService>();

        //Act

        await userEmailer.SendEmailChangeRequestLinkAsync(
            user,
            newEmailAddress,
            appUrlService.CreateEmailChangeRequestUrlFormat(this.AbpSession.TenantId)
        );

        await accountAppService.ChangeEmail(
            new ChangeEmailInput()
            {
                UserId = user.Id,
                EmailAddress = newEmailAddress,
                OldEmailAddress = user.EmailAddress
            }
        );

        //Assert

        user = await this.GetCurrentUserAsync();
        user.EmailAddress.ShouldBe(newEmailAddress);
    }
    
    [Fact]
    public async Task Should_Throw_Exception_Wrong_Old_Email()
    {
        //Arrange

        this.UsingDbContext(context =>
        {
            //Set IsEmailConfirmed to false to provide initial test case
            var currentUser = context.Users.Single(u => u.Id == this.AbpSession.UserId.Value);
            currentUser.IsEmailConfirmed = false;
        });

        var user = await this.GetCurrentUserAsync();
        user.IsEmailConfirmed.ShouldBeFalse();
        
        const string newEmailAddress = "<EMAIL>";
        
        var accountAppService = this.Resolve<IAccountAppService>();
        
        //Act & Assert

        await Should.ThrowAsync<AbpException>( async () =>
        {
            await accountAppService.ChangeEmail(
                new ChangeEmailInput()
                {
                    UserId = user.Id,
                    EmailAddress = newEmailAddress,
                    OldEmailAddress = "<EMAIL>"
                }
            );
        });
    }
}