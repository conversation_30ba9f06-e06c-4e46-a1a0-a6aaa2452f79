using System.Linq;
using System.Threading.Tasks;
using Castle.MicroKernel.Registration;
using RealtoCrm.Authorization.Accounts;
using RealtoCrm.Authorization.Accounts.Dto;
using RealtoCrm.Authorization.Users;
using RealtoCrm.Test.Base;
using NSubstitute;
using Shouldly;
using Xunit;

namespace RealtoCrm.Tests.Authorization.Accounts;

// ReSharper disable once InconsistentNaming
public class Email_Activation_Tests : AppTestBase
{
    [Fact]
    public async Task Should_Activate_Email()
    {
        //Arrange

        this.UsingDbContext(context =>
        {
            //Set IsEmailConfirmed to false to provide initial test case
            var currentUser = context.Users.Single(u => u.Id == this.AbpSession.UserId.Value);
            currentUser.IsEmailConfirmed = false;
        });

        var user = await this.GetCurrentUserAsync();
        user.IsEmailConfirmed.ShouldBeFalse();

        string confirmationCode = null;

        var fakeUserEmailer = Substitute.For<IUserEmailer>();
        var localUser = user;
        fakeUserEmailer.SendEmailActivationLinkAsync(Arg.Any<User>(), Arg.Any<string>()).Returns(callInfo =>
        {
            var calledUser = callInfo.Arg<User>();
            calledUser.EmailAddress.ShouldBe(localUser.EmailAddress);
            confirmationCode = calledUser.EmailConfirmationCode; //Getting the confirmation code sent to the email address
            return Task.CompletedTask;
        });

        this.LocalIocManager.IocContainer.Register(Component.For<IUserEmailer>().Instance(fakeUserEmailer).IsDefault());

        var accountAppService = this.Resolve<IAccountAppService>();

        //Act

        await accountAppService.SendEmailActivationLink(
            new SendEmailActivationLinkInput
            {
                EmailAddress = user.EmailAddress
            }
        );

        await accountAppService.ActivateEmail(
            new ActivateEmailInput
            {
                UserId = user.Id,
                ConfirmationCode = confirmationCode
            }
        );

        //Assert

        user = await this.GetCurrentUserAsync();
        user.IsEmailConfirmed.ShouldBeTrue();
    }
}