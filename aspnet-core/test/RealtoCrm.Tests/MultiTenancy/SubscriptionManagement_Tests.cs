using System.Linq;
using System.Threading.Tasks;
using RealtoCrm.Editions;
using RealtoCrm.MultiTenancy;
using RealtoCrm.MultiTenancy.Payments;
using RealtoCrm.MultiTenancy.Payments.Dto;
using Shouldly;
using Xunit;

namespace RealtoCrm.Tests.MultiTenancy;

// ReSharper disable once InconsistentNaming
public class SubscriptionManagement_Tests : AppTestBase
{
    private readonly TenantManager tenantManager;
    private readonly EditionManager editionManager;
    private readonly IPaymentAppService paymentAppService;

    public SubscriptionManagement_Tests()
    {
        this.LoginAsHostAdmin();

        this.tenantManager = this.Resolve<TenantManager>();
        this.editionManager = this.Resolve<EditionManager>();
        this.paymentAppService = this.Resolve<IPaymentAppService>();
    }

    [Theory]
    [InlineData(PaymentPeriodType.Daily, 99.99, 199.99, 1, 4.17)]
    [InlineData(PaymentPeriodType.Daily, 99.99, 199.99, 23, 95.83)]
    [InlineData(PaymentPeriodType.Daily, 99.99, 199.99, 25, 104.17)]
    [InlineData(PaymentPeriodType.Daily, 99.99, 199.99, 47, 195.83)]
    [InlineData(PaymentPeriodType.Daily, 99.99, 199.99, 12, 50.00)]

    [InlineData(PaymentPeriodType.Weekly, 99.99, 199.99, 1, 0.60)]
    [InlineData(PaymentPeriodType.Weekly, 99.99, 199.99, 167, 99.40)]
    [InlineData(PaymentPeriodType.Weekly, 99.99, 199.99, 169, 100.60)]
    [InlineData(PaymentPeriodType.Weekly, 99.99, 199.99, 335, 199.40)]
    [InlineData(PaymentPeriodType.Weekly, 99.99, 199.99, 84, 50.00)]

    [InlineData(PaymentPeriodType.Monthly, 99.99, 199.99, 1, 0.14)]
    [InlineData(PaymentPeriodType.Monthly, 99.99, 199.99, 719, 99.86)]
    [InlineData(PaymentPeriodType.Monthly, 99.99, 199.99, 721, 100.14)]
    [InlineData(PaymentPeriodType.Monthly, 99.99, 199.99, 1439, 199.86)]
    [InlineData(PaymentPeriodType.Monthly, 99.99, 199.99, 360, 50.00)]

    [InlineData(PaymentPeriodType.Annual, 99.99, 199.99, 1, 0.01)]
    [InlineData(PaymentPeriodType.Annual, 99.99, 199.99, 8759, 99.99)]
    [InlineData(PaymentPeriodType.Annual, 99.99, 199.99, 8761, 100.01)]
    [InlineData(PaymentPeriodType.Annual, 99.99, 199.99, 17519, 199.99)]
    [InlineData(PaymentPeriodType.Annual, 99.99, 199.99, 4380, 50.00)]
    public void Calculate_Upgrade_To_Edition_Price(PaymentPeriodType paymentPeriodType, decimal currentEditionPrice, decimal targetEditionPrice, int remainingHoursCount, decimal upgradePrice)
    {
        // Used same price for easily testing upgrade price calculation.
        var currentEdition = new SubscribableEdition
        {
            DisplayName = "Standard",
            Name = "Standard",
            DailyPrice = currentEditionPrice,
            WeeklyPrice = currentEditionPrice,
            MonthlyPrice = currentEditionPrice,
            AnnualPrice = currentEditionPrice
        };

        var targetEdition = new SubscribableEdition
        {
            DisplayName = "Premium",
            Name = "Premium",
            DailyPrice = targetEditionPrice,
            WeeklyPrice = targetEditionPrice,
            MonthlyPrice = targetEditionPrice,
            AnnualPrice = targetEditionPrice
        };

        var price = this.tenantManager.GetUpgradePrice(currentEdition, targetEdition, remainingHoursCount, paymentPeriodType);

        price.ToString("N2").ShouldBe(upgradePrice.ToString("N2"));
    }

    [MultiTenantTheory]
    [InlineData(PaymentPeriodType.Annual, EditionPaymentType.Upgrade)]
    [InlineData(PaymentPeriodType.Monthly, EditionPaymentType.Upgrade)]
    [InlineData(PaymentPeriodType.Weekly, EditionPaymentType.Upgrade)]
    [InlineData(PaymentPeriodType.Daily, EditionPaymentType.Upgrade)]
    public async Task Upgrade_Tenant_Edition(PaymentPeriodType paymentPeriodType,
        EditionPaymentType editionPaymentType)
    {
        await this.CreateUpdateTenant(paymentPeriodType, editionPaymentType);
    }

    [MultiTenantTheory]
    [InlineData(PaymentPeriodType.Annual, EditionPaymentType.BuyNow)]
    [InlineData(PaymentPeriodType.Monthly, EditionPaymentType.BuyNow)]
    [InlineData(PaymentPeriodType.Weekly, EditionPaymentType.BuyNow)]
    [InlineData(PaymentPeriodType.Daily, EditionPaymentType.BuyNow)]
    public async Task BuyNow_Tenant_Edition(PaymentPeriodType paymentPeriodType,
        EditionPaymentType editionPaymentType)
    {
        await this.CreateUpdateTenant(paymentPeriodType, editionPaymentType);
    }

    [MultiTenantTheory]
    [InlineData(PaymentPeriodType.Annual, EditionPaymentType.Extend)]
    [InlineData(PaymentPeriodType.Monthly, EditionPaymentType.Extend)]
    [InlineData(PaymentPeriodType.Weekly, EditionPaymentType.Extend)]
    [InlineData(PaymentPeriodType.Daily, EditionPaymentType.Extend)]
    public async Task Extend_Tenant_Edition(PaymentPeriodType paymentPeriodType,
        EditionPaymentType editionPaymentType)
    {
        await this.CreateUpdateTenant(paymentPeriodType, editionPaymentType);
    }

    [Fact]
    public async Task Mark_Tenant_As_Passive_When_Subscription_Expires()
    {
        //Act
        var freeEdition = new SubscribableEdition
        {
            DisplayName = "Free Edition"
        };
        var standard = new SubscribableEdition
        {
            DisplayName = "Standard Edition"
        };

        await this.UsingDbContextAsync(async context =>
        {
            context.SubscribableEditions.Add(freeEdition);
            context.SubscribableEditions.Add(standard);
            await context.SaveChangesAsync();
        });

        var tenant = new Tenant("AbpProjectName", "AbpProjectName")
        {
            EditionId = standard.Id,
        };

        await this.UsingDbContextAsync(async context =>
        {
            context.Tenants.Add(tenant);
            await context.SaveChangesAsync();
        });

        this.UsingDbContext(context =>
        {
            var updatedTenant = context.Tenants.FirstOrDefault(t => t.Id == tenant.Id);
            updatedTenant.ShouldNotBe(null);
            updatedTenant.IsActive.ShouldBe(false);
        });
    }

    [Fact]
    public async Task GetPaymentHistory_Test()
    {
        this.LoginAsDefaultTenantAdmin();

        var result = await this.paymentAppService.GetPaymentHistory(new GetPaymentHistoryInput());
        result.Items.Count.ShouldBe(2);
        result.Items[0].DayCount.ShouldBe(1);
        result.Items[1].DayCount.ShouldBe(30);
    }

    private async Task CreateUpdateTenant(PaymentPeriodType paymentPeriodType, EditionPaymentType editionPaymentType)
    {
        await this.editionManager.CreateAsync(new SubscribableEdition
        {
            Name = "CurrentEdition",
            DisplayName = "Current Edition"
        });

        await this.editionManager.CreateAsync(new SubscribableEdition
        {
            Name = "TargetEdition",
            DisplayName = "Target Edition"
        });

        var currentEditionId = (await this.editionManager.FindByNameAsync("CurrentEdition")).Id;
        var targetEditionId = (await this.editionManager.FindByNameAsync("TargetEdition")).Id;

        // Create tenant
        // await this.tenantAppService.CreateTenant(
        //     new CreateTenantInput
        //     {
        //         TenancyName = "testTenant",
        //         Name = "Tenant for test purpose",
        //         AdminEmailAddress = "<EMAIL>",
        //         AdminPassword = "123qwe",
        //         IsActive = true,
        //         EditionId = currentEditionId,
        //         SubscriptionEndDateUtc = subscriptionEndDate
        //     });

        //Assert
        var tenant = await this.GetTenantOrNullAsync("testTenant");
        tenant.ShouldNotBe(null);
        tenant.Name.ShouldBe("Tenant for test purpose");
        tenant.IsActive.ShouldBe(true);
        tenant.EditionId.ShouldBe(currentEditionId);

        // Update Tenant -----------------------------

        var tenantForUpdate = this.tenantManager.UpdateTenantAsync(
            tenant.Id,
            true,
            false,
            paymentPeriodType,
            targetEditionId,
            editionPaymentType
        );

        //Assert
        tenant = await tenantForUpdate;
        tenant.IsActive.ShouldBe(true);
        tenant.EditionId.ShouldBe(targetEditionId);
    }
}
