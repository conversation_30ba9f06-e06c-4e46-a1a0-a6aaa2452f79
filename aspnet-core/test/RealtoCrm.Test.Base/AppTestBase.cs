using System;
using System.Linq;
using System.Threading.Tasks;
using Abp;
using Abp.Authorization.Users;
using Abp.EntityFrameworkCore.Extensions;
using Abp.Events.Bus;
using Abp.Events.Bus.Entities;
using Abp.Modules;
using Abp.MultiTenancy;
using Abp.Runtime.Session;
using Abp.TestBase;
using Microsoft.EntityFrameworkCore;
using RealtoCrm.Authorization.Roles;
using RealtoCrm.Authorization.Users;
using RealtoCrm.EntityFrameworkCore;
using RealtoCrm.MultiTenancy;
using RealtoCrm.Test.Base.TestData;

namespace RealtoCrm.Test.Base;

/// <summary>
/// This is base class for all our test classes.
/// It prepares ABP system, modules and a fake, in-memory database.
/// Seeds database with initial data.
/// Provides methods to easily work with <see cref="RealtoCrmDbContext"/>.
/// </summary>
public abstract class AppTestBase<T> : AbpIntegratedTestBase<T> where T : AbpModule
{
    protected AppTestBase()
    {
        this.SeedTestData();
        this.LoginAsDefaultTenantAdmin();
    }

    private void SeedTestData()
    {
        void NormalizeDbContext(RealtoCrmDbContext context)
        {
            context.EntityChangeEventHelper = NullEntityChangeEventHelper.Instance;
            context.EventBus = NullEventBus.Instance;
            context.SuppressAutoSetTenantId = true;
        }

        //Seed initial data for default tenant
        this.AbpSession.TenantId = 1;

        this.UsingDbContext(context =>
        {
            NormalizeDbContext(context);
            new TestDataBuilder(context, 1).Create();
        });
    }

    protected IDisposable UsingTenantId(int? tenantId)
    {
        var previousTenantId = this.AbpSession.TenantId;
        this.AbpSession.TenantId = tenantId;
        return new DisposeAction(() => this.AbpSession.TenantId = previousTenantId);
    }

    protected void UsingDbContext(Action<RealtoCrmDbContext> action)
    {
        this.UsingDbContext(this.AbpSession.TenantId, action);
    }

    protected Task UsingDbContextAsync(Func<RealtoCrmDbContext, Task> action)
    {
        return this.UsingDbContextAsync(this.AbpSession.TenantId, action);
    }

    protected TResult UsingDbContext<TResult>(Func<RealtoCrmDbContext, TResult> func)
    {
        return this.UsingDbContext(this.AbpSession.TenantId, func);
    }

    protected Task<TResult> UsingDbContextAsync<TResult>(Func<RealtoCrmDbContext, Task<TResult>> func)
    {
        return this.UsingDbContextAsync(this.AbpSession.TenantId, func);
    }

    protected void UsingDbContext(int? tenantId, Action<RealtoCrmDbContext> action)
    {
        using (this.UsingTenantId(tenantId))
        {
            using (var context = this.LocalIocManager.Resolve<RealtoCrmDbContext>())
            {
                action(context);
                context.SaveChanges();
            }
        }
    }

    protected async Task UsingDbContextAsync(int? tenantId, Func<RealtoCrmDbContext, Task> action)
    {
        using (this.UsingTenantId(tenantId))
        {
            await using (var context = this.LocalIocManager.Resolve<RealtoCrmDbContext>())
            {
                await action(context);
                await context.SaveChangesAsync();
            }
        }
    }

    protected TResult UsingDbContext<TResult>(int? tenantId, Func<RealtoCrmDbContext, TResult> func)
    {
        TResult result;

        using (this.UsingTenantId(tenantId))
        {
            using (var context = this.LocalIocManager.Resolve<RealtoCrmDbContext>())
            {
                result = func(context);
                context.SaveChanges();
            }
        }

        return result;
    }

    protected async Task<TResult> UsingDbContextAsync<TResult>(int? tenantId, Func<RealtoCrmDbContext, Task<TResult>> func)
    {
        TResult result;

        using (this.UsingTenantId(tenantId))
        {
            await using (var context = this.LocalIocManager.Resolve<RealtoCrmDbContext>())
            {
                result = await func(context);
                await context.SaveChangesAsync();
            }
        }

        return result;
    }

    #region Login

    protected void LoginAsHostAdmin()
    {
        this.LoginAsHost(AbpUserBase.AdminUserName);
    }

    protected void LoginAsDefaultTenantAdmin()
    {
        this.LoginAsTenant(AbpTenantBase.DefaultTenantName, AbpUserBase.AdminUserName);
    }

    protected void LoginAsHost(string userName)
    {
        this.AbpSession.TenantId = null;

        var user = this.UsingDbContext(context => context.Users.FirstOrDefault(u => u.TenantId == this.AbpSession.TenantId && u.UserName == userName));
        if (user == null)
        {
            throw new Exception("There is no user: " + userName + " for host.");
        }

        this.AbpSession.UserId = user.Id;
    }

    protected void LoginAsTenant(string tenancyName, string userName)
    {
        this.AbpSession.TenantId = null;

        var tenant = this.UsingDbContext(context => context.Tenants.FirstOrDefault(t => t.TenancyName == tenancyName));
        if (tenant == null)
        {
            throw new Exception("There is no tenant: " + tenancyName);
        }

        this.AbpSession.TenantId = tenant.Id;

        var user = this.UsingDbContext(context => context.Users.FirstOrDefault(u => u.TenantId == this.AbpSession.TenantId && u.UserName == userName));
        if (user == null)
        {
            throw new Exception("There is no user: " + userName + " for tenant: " + tenancyName);
        }

        this.AbpSession.UserId = user.Id;
    }

    #endregion

    #region GetCurrentUser

    /// <summary>
    /// Gets current user if <see cref="IAbpSession.UserId"/> is not null.
    /// Throws exception if it's null.
    /// </summary>
    protected User GetCurrentUser()
    {
        var userId = this.AbpSession.GetUserId();
        return this.UsingDbContext(context => context.Users.Single(u => u.Id == userId));
    }

    /// <summary>
    /// Gets current user if <see cref="IAbpSession.UserId"/> is not null.
    /// Throws exception if it's null.
    /// </summary>
    protected async Task<User> GetCurrentUserAsync()
    {
        var userId = this.AbpSession.GetUserId();
        return await this.UsingDbContext(context => context.Users.SingleAsync(u => u.Id == userId));
    }

    #endregion

    #region GetCurrentTenant

    /// <summary>
    /// Gets current tenant if <see cref="IAbpSession.TenantId"/> is not null.
    /// Throws exception if there is no current tenant.
    /// </summary>
    protected Tenant GetCurrentTenant()
    {
        var tenantId = this.AbpSession.GetTenantId();
        return this.UsingDbContext(null, context => context.Tenants.Single(t => t.Id == tenantId));
    }

    /// <summary>
    /// Gets current tenant if <see cref="IAbpSession.TenantId"/> is not null.
    /// Throws exception if there is no current tenant.
    /// </summary>
    protected async Task<Tenant> GetCurrentTenantAsync()
    {
        var tenantId = this.AbpSession.GetTenantId();
        return await this.UsingDbContext(null, context => context.Tenants.SingleAsync(t => t.Id == tenantId));
    }

    #endregion

    #region GetTenant / GetTenantOrNull

    protected Tenant GetTenant(string tenancyName)
    {
        return this.UsingDbContext(null, context => context.Tenants.Single(t => t.TenancyName == tenancyName));
    }

    protected async Task<Tenant> GetTenantAsync(string tenancyName)
    {
        return await this.UsingDbContext(null, async context => await context.Tenants.SingleAsync(t => t.TenancyName == tenancyName));
    }

    protected Tenant GetTenantOrNull(string tenancyName)
    {
        return this.UsingDbContext(null, context => context.Tenants.FirstOrDefault(t => t.TenancyName == tenancyName));
    }

    protected async Task<Tenant> GetTenantOrNullAsync(string tenancyName)
    {
        return await this.UsingDbContext(null, async context => await context.Tenants.FirstOrDefaultAsync(t => t.TenancyName == tenancyName));
    }

    #endregion

    #region GetRole

    protected Role GetRole(string roleName)
    {
        return this.UsingDbContext(context => context.Roles.Single(r => r.Name == roleName && r.TenantId == this.AbpSession.TenantId));
    }

    protected async Task<Role> GetRoleAsync(string roleName)
    {
        return await this.UsingDbContext(async context => await context.Roles.SingleAsync(r => r.Name == roleName && r.TenantId == this.AbpSession.TenantId));
    }

    #endregion

    #region GetUserByUserName

    protected User GetUserByUserName(string userName)
    {
        var user = this.GetUserByUserNameOrNull(userName);
        if (user == null)
        {
            throw new Exception("Can not find a user with username: " + userName);
        }

        return user;
    }

    protected async Task<User> GetUserByUserNameAsync(string userName)
    {
        var user = await this.GetUserByUserNameOrNullAsync(userName);
        if (user == null)
        {
            throw new Exception("Can not find a user with username: " + userName);
        }

        return user;
    }

    protected User GetUserByUserNameOrNull(string userName)
    {
        return this.UsingDbContext(context =>
            context.Users.FirstOrDefault(u =>
                u.UserName == userName &&
                u.TenantId == this.AbpSession.TenantId
            ));
    }

    protected async Task<User> GetUserByUserNameOrNullAsync(string userName, bool includeRoles = false)
    {
        return await this.UsingDbContextAsync(async context =>
            await context.Users
                .IncludeIf(includeRoles, u => u.Roles)
                .FirstOrDefaultAsync(u =>
                    u.UserName == userName &&
                    u.TenantId == this.AbpSession.TenantId
                ));
    }

    #endregion
}