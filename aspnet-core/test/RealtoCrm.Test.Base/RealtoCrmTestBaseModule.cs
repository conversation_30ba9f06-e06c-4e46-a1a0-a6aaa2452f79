using System;
using System.IO;
using Abp;
using Abp.AspNetZeroCore;
using Abp.AutoMapper;
using Abp.Configuration.Startup;
using Abp.Dependency;
using Abp.Modules;
using Abp.Net.Mail;
using Abp.TestBase;
using Abp.Zero.Configuration;
using Castle.MicroKernel.Registration;
using Microsoft.Extensions.Configuration;
using RealtoCrm.Authorization.Users;
using RealtoCrm.Configuration;
using RealtoCrm.EntityFrameworkCore;
using RealtoCrm.MultiTenancy;
using RealtoCrm.Security.Recaptcha;
using RealtoCrm.Test.Base.DependencyInjection;
using RealtoCrm.Test.Base.UiCustomization;
using RealtoCrm.Test.Base.Url;
using RealtoCrm.Test.Base.Web;
using RealtoCrm.UiCustomization;
using RealtoCrm.Url;
using NSubstitute;

namespace RealtoCrm.Test.Base;

[DependsOn(
    typeof(RealtoCrmApplicationModule),
    typeof(RealtoCrmEntityFrameworkCoreModule),
    typeof(AbpTestBaseModule))]
public class RealtoCrmTestBaseModule : AbpModule
{
    public RealtoCrmTestBaseModule(RealtoCrmEntityFrameworkCoreModule abpZeroTemplateEntityFrameworkCoreModule)
    {
        abpZeroTemplateEntityFrameworkCoreModule.SkipDbContextRegistration = true;
    }

    public override void PreInitialize()
    {
        var configuration = GetConfiguration();

        this.Configuration.BackgroundJobs.IsJobExecutionEnabled = false;

        this.Configuration.UnitOfWork.Timeout = TimeSpan.FromMinutes(30);
        this.Configuration.UnitOfWork.IsTransactional = false;

        //Disable static mapper usage since it breaks unit tests (see https://github.com/aspnetboilerplate/aspnetboilerplate/issues/2052)
        this.Configuration.Modules.AbpAutoMapper().UseStaticMapper = false;

        //Use database for language management
        this.Configuration.Modules.Zero().LanguageManagement.EnableDbLocalization();

        this.RegisterFakeService<AbpZeroDbMigrator>();

        this.IocManager.Register<IAppUrlService, FakeAppUrlService>();
        this.IocManager.Register<IWebUrlService, FakeWebUrlService>();
        this.IocManager.Register<IRecaptchaValidator, FakeRecaptchaValidator>();

        this.Configuration.ReplaceService<IAppConfigurationAccessor, TestAppConfigurationAccessor>();
        this.Configuration.ReplaceService<IEmailSender, NullEmailSender>(DependencyLifeStyle.Transient);
        this.Configuration.ReplaceService<IUiThemeCustomizerFactory, NullUiThemeCustomizerFactory>();

        this.Configuration.Modules.AspNetZero().LicenseCode = configuration["AbpZeroLicenseCode"];

        //Uncomment below line to write change logs for the entities below:
        this.Configuration.EntityHistory.IsEnabled = true;
        this.Configuration.EntityHistory.Selectors.Add("RealtoCrmEntities", typeof(User), typeof(Tenant));
    }

    public override void Initialize()
    {
        ServiceCollectionRegistrar.Register(this.IocManager);
    }

    private void RegisterFakeService<TService>()
        where TService : class
    {
        this.IocManager.IocContainer.Register(
            Component.For<TService>()
                .UsingFactoryMethod(() => Substitute.For<TService>())
                .LifestyleSingleton()
        );
    }

    private static IConfigurationRoot GetConfiguration()
    {
        return AppConfigurations.Get(Directory.GetCurrentDirectory(), addUserSecrets: true);
    }
}