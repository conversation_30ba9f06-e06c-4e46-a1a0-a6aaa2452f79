using System.Linq;
using RealtoCrm.Editions;
using RealtoCrm.EntityFrameworkCore;
using RealtoCrm.MultiTenancy.Payments;

namespace RealtoCrm.Test.Base.TestData;

public class TestSubscriptionPaymentBuilder(RealtoCrmDbContext context, int tenantId)
{
    public void Create()
    {
        this.CreatePayments();
    }

    private void CreatePayments()
    {
        var defaultEdition = context.Editions.First(e => e.Name == EditionManager.DefaultEditionName);

        this.CreatePayment(1, defaultEdition.Id, tenantId, 1, "147741");
        this.CreatePayment(19, defaultEdition.Id, tenantId, 30, "1477419");
    }

    private void CreatePayment(decimal amount, int editionId, int tenantId, int dayCount, string paymentId)
    {
        context.SubscriptionPayments.Add(new SubscriptionPayment
        {
            Amount = amount,
            EditionId = editionId,
            TenantId = tenantId,
            DayCount = dayCount,
            ExternalPaymentId = paymentId
        });
    }
}