using RealtoCrm.Editions;
using RealtoCrm.EntityFrameworkCore;

namespace RealtoCrm.Test.Base.TestData;

public class TestEditionsBuilder(RealtoCrmDbContext context)
{
    public void Create()
    {
        this.CreateEditions();
    }

    private void CreateEditions()
    {
        this.CreateEdition("Free Edition 1", "FreeEdition1", null, null);
        this.CreateEdition("Free Edition 2", "FreeEdition2", null, null);
        this.CreateEdition("Free Edition 3", "FreeEdition3", null, null);
        this.CreateEdition("Paid Edition 1", "PaidEdition1", 10, 100);
        this.CreateEdition("Paid Edition 2", "PaidEdition2", 20, 200);
        this.CreateEdition("Paid Edition 3", "PaidEdition3", 30, 300);
    }

    private void CreateEdition(string displayName, string name, decimal? monthlyPrice, decimal? annualPrice)
    {
        var edition = new SubscribableEdition
        {
            DisplayName = displayName,
            Name = name,
            MonthlyPrice = monthlyPrice,
            AnnualPrice = annualPrice
        };

        context.SubscribableEditions.Add(edition);
    }
}