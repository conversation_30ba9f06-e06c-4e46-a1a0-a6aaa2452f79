using Abp.Dependency;
using Abp.Reflection.Extensions;
using Microsoft.Extensions.Configuration;
using RealtoCrm.Configuration;

namespace RealtoCrm.Test.Base.Configuration;

public class TestAppConfigurationAccessor : IAppConfigurationAccessor, ISingletonDependency
{
    public IConfigurationRoot Configuration { get; } = AppConfigurations.Get(
        typeof(RealtoCrmTestBaseModule).GetAssembly().GetDirectoryPathOrNull()
    );
}