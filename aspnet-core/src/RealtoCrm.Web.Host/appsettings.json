{"ConnectionStrings": {"Default": "Host=localhost;Port=5432;Database=realto_crm;Username=realto_user;Password=********;Pooling=true;"}, "AbpZeroLicenseCode": "00w9vBHdSqRRozBjw8/JSRXA==822cf80c3d08f251dec635a741724456", "Abp": {"RedisCache": {"ConnectionString": "localhost", "DatabaseId": -1}}, "App": {"ServerRootAddress": "http://localhost:8001", "ClientRootAddress": "http://localhost:8002/", "CorsOrigins": "http://crm-realto-demo.com:81,http://localhost:8002,http://localhost:9876", "SwaggerEndPoint": "/swagger/v1/swagger.json", "AllowAnonymousSignalRConnection": "true", "HomePageUrl": "/index.html", "AuditLog": {"AutoDeleteExpiredLogs": {"IsEnabled": false, "ExcelBackup": {"IsEnabled": false, "FilePath": "App_Data/AuditLogsBackups/"}}}, "Smtp": {"Host": "app.settings.com", "Port": 465, "UserName": "appsettings", "Password": "@ppsettings_password", "EnableSsl": false, "UseDefaultCredentials": true, "DefaultFromAddress": "<EMAIL>", "DefaultFromDisplayName": "appsettings"}}, "Authentication": {"AllowSocialLoginSettingsPerTenant": false, "Facebook": {"IsEnabled": "false", "AppId": "", "AppSecret": ""}, "Twitter": {"IsEnabled": "false", "ApiKey": "", "ApiKeySecret": ""}, "Google": {"IsEnabled": "false", "ClientId": "", "ClientSecret": "", "UserInfoEndpoint": "https://www.googleapis.com/oauth2/v2/userinfo"}, "Microsoft": {"IsEnabled": "false", "ConsumerKey": "", "ConsumerSecret": ""}, "OpenId": {"IsEnabled": "false", "ClientId": "", "Authority": "", "LoginUrl": "", "ValidateIssuer": "false", "ResponseType": "id_token", "ClaimsMapping": [{"claim": "unique_name", "key": "preferred_username"}]}, "WsFederation": {"IsEnabled": "false", "Authority": "", "ClientId": "", "Tenant": "", "MetaDataAddress": ""}, "JwtBearer": {"IsEnabled": "true", "SecurityKey": "9E59D1A99E844D8B955995EB6F0DA5EE", "Issuer": "RealtoCrm", "Audience": "RealtoCrm"}}, "Storage": {"AzureBlobStorage": {"ConnectionString": "DefaultEndpointsProtocol=https;AccountName=cosher;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"}}, "Configuration": {"AzureKeyVault": {"IsEnabled": "false", "KeyVaultName": "", "TenantId": "", "ClientId": "", "ClientSecret": ""}}, "Twilio": {"AccountSid": "", "AuthToken": "", "SenderNumber": ""}, "Recaptcha": {"SiteKey": "6Le8FUgqAAAAAFj_kbpPhrmaGfAaScgDPxecrber", "SecretKey": "6Le8FUgqAAAAABKvRbtqQGmi7TFsP1DCMHOQozLv"}, "OpenIddict": {"IsEnabled": "false", "Applications": [{"ClientId": "client", "ClientSecret": "def2edf7-5d42-4edc-a84a-30136c340e13", "DisplayName": "RealtoCrm_App", "ConsentType": "Explicit", "RedirectUris": ["https://oauthdebugger.com/debug"], "PostLogoutRedirectUris": [], "Scopes": ["default-api", "profile"], "Permissions": ["ept:token", "ept:authorization", "gt:password", "gt:client_credentials", "gt:authorization_code", "rst:code", "rst:code id_token"]}]}, "Payment": {"PayPal": {"IsActive": "true", "Environment": "sandbox", "BaseUrl": "https://api.sandbox.paypal.com/v1", "ClientId": "", "ClientSecret": "", "DemoUsername": "", "DemoPassword": "", "DisabledFundings": []}, "Stripe": {"IsActive": "true", "BaseUrl": "https://api.stripe.com/v1", "SecretKey": "", "PublishableKey": "", "WebhookSecret": "", "PaymentMethodTypes": ["card"]}}, "HealthChecks": {"HealthChecksEnabled": false, "HealthChecksUI": {"HealthChecksUIEnabled": false, "HealthChecks": [{"Name": "RealtoCrm.Web.Host", "Uri": "http://localhost:8001/health"}], "EvaluationTimeOnSeconds": 10, "MinimumSecondsBetweenFailureNotifications": 60}}, "KestrelServer": {"IsEnabled": false}, "Swagger": {"ShowSummaries": false}, "Sentry": {"DSN": ""}, "HttpPorts": 8001, "HTTP_PORTS": 8001}