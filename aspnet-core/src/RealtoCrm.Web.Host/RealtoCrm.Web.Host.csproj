<Project Sdk="Microsoft.NET.Sdk.Web">
  <Import Project="..\..\common.props" />
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <AssetTargetFallback>$(AssetTargetFallback);portable-net45+win8+wp8+wpa81;</AssetTargetFallback>
    <PreserveCompilationContext>true</PreserveCompilationContext>
    <AssemblyName>RealtoCrm.Web.Host</AssemblyName>
    <OutputType>Exe</OutputType>
    <PackageId>RealtoCrm.Web.Host</PackageId>
    <UserSecretsId>MyCompanyName-RealtoCrm-894FDFC1-6482-4A56-926A-3C46C9FE0329</UserSecretsId>
    <RootNamespace>RealtoCrm.Web</RootNamespace>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <GenerateBindingRedirectsOutputType>true</GenerateBindingRedirectsOutputType>
    <GenerateDocumentationFile>False</GenerateDocumentationFile>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  <ItemGroup>
    <Compile Remove="App_Data\**" />
    <Content Remove="App_Data\**" />
    <EmbeddedResource Remove="App_Data\**" />
    <None Remove="App_Data\**" />
    <None Update="Templates\Договор за Гаранция.docx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Content Remove="wwwroot\swagger\ui\index.html" />
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="wwwroot\Plugins\.gitkeep" />
    <None Update="Dockerfile">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="log4net.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </None>
    <None Update="wwwroot\**\*">
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\RealtoCrm.Web.Core\RealtoCrm.Web.Core.csproj" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Abp.Castle.Log4Net" Version="9.0.0" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.19.6" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.1">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Sentry.AspNetCore" Version="4.1.2" />
    <PackageReference Include="DotSwashbuckle.AspNetCore" Version="3.0.11" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="wwwroot\swagger\ui\index.html" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Templates\" />
    <Folder Include="wwwroot\Plugins\" />
  </ItemGroup>
</Project>