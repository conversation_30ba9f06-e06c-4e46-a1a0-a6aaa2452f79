using System.IO;
using Abp;
using Abp.Dependency;
using Abp.Reflection.Extensions;
using Microsoft.AspNetCore;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.DependencyInjection;
using RealtoCrm.Web.Startup;

namespace RealtoCrm.Web;

public class SwaggerWebHostFactory
{
    public static IWebHost CreateWebHost()
    {
        var builder = WebHost.CreateDefaultBuilder()
            .UseContentRoot(Directory.GetCurrentDirectory())
            .UseStartup<Startup.Startup>();

        var app = builder.Build();

        var ioc = app.Services.GetRequiredService<IIocManager>();

        ioc.RegisterAssemblyByConvention(typeof(RealtoCrmWebHostModule).GetAssembly());

        AbpBootstrapper
            .Create<RealtoCrmWebHostModule>(options => options.IocManager = ioc)
            .Initialize();

        return app;
    }
}