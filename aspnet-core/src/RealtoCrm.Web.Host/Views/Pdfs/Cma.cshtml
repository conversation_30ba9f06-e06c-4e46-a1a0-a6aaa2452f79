@using RealtoCrm.CMA.Models
@using RealtoCrm.Companies
@using RealtoCrm.Expressions
@using static RealtoCrm.EnvironmentConsts;
@model RealtoCrm.Application.CMA.CmaWrapper
@inject ICompaniesAppService CompaniesAppService;
@{
    ViewData["Title"] = "Сравнителен Пазарен Анализ";
    var baseOffer = Model.BaseOffer;
    string WithCdn(string path) => Environment.GetEnvironmentVariable(CdnBaseUrlName) + path;
    var company = await CompaniesAppService.GetCurrentCompanyDetails<CompanyForCmaResponseModel>();
    var wrapperClassName = $"cma-report {company.Name.ToLower().Hyphenize()}";
}

<div class="@wrapperClassName">
    <!-- Header -->
    <div class="report-header">
        <div class="logo-container">
            <img src="@WithCdn(company.LogoUrl)" alt="@company.Name" class="logo"/>
        </div>
        <h1 class="report-title"> СРАВНИТЕЛЕН ПАЗАРЕН АНАЛИЗ</h1>
    </div>

    <!-- Introduction -->
    <div class="report-section">
        <p class="introduction">
            Целта на настоящия анализ е да Ви предложи пълна информация за текущото пазарно състояние
            и най-коректно да определи правилната офертна цена на Вашия имот. Изключително важно е при
            първоначалното обявяване на имота цената да бъде максимално адекватна на пазарните
            очаквания. Това ще помогне за бързата му реализация.
            Анализът представя сравнение на Вашия имот с други оферти на пазара със сходни параметри.
            Разглежданите предложения включват активните към момента имоти, такива, които не са били
            реализирани и такива, които са продадени.
        </p>
    </div>

    <!-- Table of Contents -->
    <div class="report-section toc">
        <h2>СЪДЪРЖАНИЕ:</h2>
        <ol>
            <li>Въведение</li>
            <li>Основна информация за имота</li>
            <li>Обобщена карта на имотите</li>
            <li>Сравнение на параметрите на имотите</li>
            <li>Сравнение на показаните имоти по статуси; Сравнение по цена и дни на пазара</li>
            <li>Анализ на пазара в района</li>
            <li>Обща информация за пазара</li>
            <li>Оценка на имота</li>
            <li>Представяне на брокера и на компанията</li>
        </ol>
    </div>

    <!-- Main Property Information -->
    @if (baseOffer != null)
    {
        <div class="report-section property-info">
            <h2>Обща Информация за Имота</h2>

            <div class="property-images">
                @foreach (var image in baseOffer.Images.Take(3))
                {
                    <div class="property-image">
                        <img src="@image.Source" alt="Property Image"/>
                    </div>
                }
            </div>

            <div class="property-details">
                <div class="property-details-left">
                    <div class="detail-item">
                        <span class="detail-label">Ref.ID:</span>
                        <span class="detail-value">@baseOffer.Id</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Тип имот:</span>
                        <span class="detail-value">@baseOffer.EstateTypeName</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Адрес на имота:</span>
                        <span class="detail-value">
                            @baseOffer.EstateAddress.PopulatedPlaceName, @baseOffer.EstateAddress.DistrictName,
                            @baseOffer.EstateAddress.StreetName № @baseOffer.EstateAddress.StreetNumber
                            @if (!string.IsNullOrEmpty(baseOffer.EstateAddress.EntranceNumber))
                            {
                                <text>
                                    ет. @baseOffer.EstateAddress.FloorNumber ап. @baseOffer.EstateAddress.ApartmentNumber</text>
                            }
                        </span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Вид:</span>
                        <span class="detail-value">с обзавеждане</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Етаж:</span>
                        <span class="detail-value">@baseOffer.EstateAddress.FloorNumber</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">РЗП:</span>
                        <span class="detail-value">@baseOffer.EstateArea кв.м.</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Брой спални:</span>
                        <span class="detail-value">@baseOffer.EstateBedroomsCount спални</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Брой бани:</span>
                        <span class="detail-value">@baseOffer.EstateBathroomsCount бани</span>
                    </div>
                </div>
                <div class="property-details-right">
                    <div class="detail-item">
                        <span class="detail-label">Състояние:</span>
                        <span class="detail-value">Луксозно</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Изложение:</span>
                        <span class="detail-value">Изток</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Паркинг:</span>
                        <span class="detail-value">Подземно паркомясто - 30 000 евро</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Асансьор:</span>
                        <span class="detail-value">да</span>
                    </div>
                </div>
            </div>

            <div class="property-description">
                <h3>Описание:</h3>
                <p>
                    @company.Name има удоволствието да Ви предложи забележителен пентхаус в една от най-луксозните
                    сгради в София в кв. Изгрев, отличаваща се с невероятни общи части, прекрасно аранжирано
                    фоайе, денонощна охрана. В непосредствена близост са разположени много посолства. Имотът се
                    характеризира с...
                </p>

                <h3>Разпределение:</h3>
                <p>
                    Първо ниво: просторен хол с трапезария и напълно оборудвана кухня, излаз на уникална тераса-двор,
                    която може да се преобрази в истински оазис, родителска спалня с прилежащи дрешник и баня,
                    мокро помещение, тоалетна за гости
                </p>
                <p>
                    Второ ниво - коридор с библиотека, гардероби, две спални с прилежащи бани, излаз на тераса
                </p>

                <h3>Предимства:</h3>
                <ul>
                    <li>Отлична сграда с перфектна локация</li>
                    <li>Стилно завършено и обзаведено жилище</li>
                    <li>Феноменални тераси с разкошни гледки</li>
                    <li>Подземно паркомясто - 30 000 евро</li>
                </ul>
            </div>
        </div>
    }

    <!-- Market Properties Summary -->
    <div class="report-section properties-summary">
        <h2>Обобщена карта на имотите</h2>

        <table class="properties-table">
            <thead>
            <tr>
                <th></th>
                <th>Разглеждан имот</th>
                <th>Офертна цена</th>
                <th>Площ</th>
                <th>Цена на кв.м.</th>
                <th>Дни на пазара</th>
                <th>Спални</th>
                <th>Бани</th>
            </tr>
            </thead>
            <tbody>
            @if (baseOffer != null)
            {
                <tr class="featured-property">
                    <td class="property-icon"><span class="star-icon">★</span></td>
                    <td>
                        @baseOffer.EstateTypeName - Ref.ID: @baseOffer.Id<br/>
                        @baseOffer.EstateAddress.PopulatedPlaceName, @baseOffer.EstateAddress.DistrictName
                    </td>
                    <td>€ @baseOffer.Price.Amount.ToString("N0")</td>
                    <td>@baseOffer.EstateArea кв.м.</td>
                    <td>
                        € @(Math.Round((decimal)baseOffer.Price.Amount / (decimal)baseOffer.EstateArea, 0)).ToString("N0")
                    </td>
                    <td>30</td>
                    <td>@baseOffer.EstateBedroomsCount</td>
                    <td>@baseOffer.EstateBathroomsCount</td>
                </tr>
            }

            <tr class="section-header">
                <td class="property-icon"><span class="active-icon">●</span></td>
                <td colspan="7">Активни имоти на пазара</td>
            </tr>

            @foreach (var offer in Model.ActiveOffers)
            {
                <tr>
                    <td style="min-width: 100px; width: 100px;">
                        <img src="@WithCdn(offer.Images.FirstOrDefault()?.Source)" alt="Property"
                             style="width: 100%; height: 100px; object-fit: cover; border-radius: 4px;"/>
                    </td>
                    <td>
                        @offer.EstateTypeName - Ref.ID: @offer.Id<br/>
                        @offer.EstateAddress.PopulatedPlaceName, @offer.EstateAddress.DistrictName
                    </td>
                    <td>€ @offer.Price.Amount.ToString("N0")</td>
                    <td>@offer.EstateArea кв.м.</td>
                    @* <td> @if(offer.EstateArea.HasValue) { *@
                    @*             Math.Round((decimal)offer.Price?.Amount / (decimal)offer.EstateArea, 0) : 0)€ @(Math.Round((decimal)offer.Price?.Amount / (decimal)offer.EstateArea, 0)).ToString("N0") *@
                    @*                  *@
                    @* :} None *@
                    @* </td> *@
                    <td>@((DateTime.Now - offer.CreationTime).Days)</td>
                    <td>@offer.EstateBedroomsCount</td>
                    <td>@offer.EstateBathroomsCount</td>
                </tr>
            }

            <tr class="section-header">
                <td class="property-icon"><span class="unfulfilled-icon">●</span></td>
                <td colspan="7">Нереализирани имоти на пазара</td>
            </tr>

            @foreach (var offer in Model.UnfulfilledOffers)
            {
                <tr>
                    <td style="min-width: 100px; width: 100px;">
                        <img src="@WithCdn(offer.Images.FirstOrDefault()?.Source)" alt="Property"
                             style="width: 100%; height: 100px; object-fit: cover; border-radius: 4px;"/>
                    </td>
                    <td>
                        @offer.EstateTypeName - Ref.ID: @offer.Id<br/>
                        @offer.EstateAddress.PopulatedPlaceName, @offer.EstateAddress.DistrictName
                    </td>
                    <td>€ @offer.Price.Amount.ToString("N0")</td>
                    <td>@offer.EstateArea кв.м.</td>
                    @* <td>€ @(Math.Round((decimal)offer.Price.Amount / (decimal)offer.EstateArea, 0)).ToString("N0")</td> *@
                    <td>@((DateTime.Now - offer.CreationTime).Days)</td>
                    <td>@offer.EstateBedroomsCount</td>
                    <td>@offer.EstateBathroomsCount</td>
                </tr>
            }

            <tr class="section-header">
                <td class="property-icon"><span class="sold-icon">●</span></td>
                <td colspan="7">Продадени имоти</td>
            </tr>

            @foreach (var offer in Model.OffersWithDeals)
            {
                <tr>
                    <td style="min-width: 100px; width: 100px;">
                        <img src="@WithCdn(offer.Images.FirstOrDefault()?.Source)" alt="Property"
                             style="width: 100%; height: 100px; object-fit: cover; border-radius: 4px;"/>
                    </td>
                    <td>
                        @offer.EstateTypeName - Ref.ID: @offer.Id<br/>
                        @offer.EstateAddress.PopulatedPlaceName, @offer.EstateAddress.DistrictName
                    </td>
                    <td>€ @offer.Price.Amount.ToString("N0")</td>
                    <td>@offer.EstateArea кв.м.</td>
                    <td>@((offer.LastModificationTime.HasValue ? offer.LastModificationTime.Value : DateTime.Now) - offer.CreationTime).Days</td>
                    <td>@offer.EstateBedroomsCount</td>
                    <td>@offer.EstateBathroomsCount</td>
                </tr>
            }
            </tbody>
        </table>
    </div>

    <!-- Comparative Analysis -->
    <div class="report-section comparative-analysis">
        <h2>Сравнителен Пазарен Анализ</h2>

        <table class="comparison-table">
            <thead>
            <tr>
                <th></th>
                <th>Разглеждан имот</th>
                <th>Имот 1</th>
                <th>Имот 2</th>
                <th>Имот 3</th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td class="property-photo">
                    @if (Model.ActiveOffers.ElementAtOrDefault(0)?.Images.Any() == true)
                    {
                        @* <img src="@Model.ActiveOffers.ElementAt(0).Images.First().Url" alt="Property" /> *@
                    }
                </td>
                <td class="property-photo">
                    @if (Model.ActiveOffers.ElementAtOrDefault(1)?.Images.Any() == true)
                    {
                        @* <img src="@Model.ActiveOffers.ElementAt(1).Images.First().Url" alt="Property" /> *@
                    }
                </td>
                <td class="property-photo">
                    @if (Model.ActiveOffers.ElementAtOrDefault(2)?.Images.Any() == true)
                    {
                        @* <img src="@Model.ActiveOffers.ElementAt(2).Images.First().Url" alt="Property" /> *@
                    }
                </td>
            </tr>
            <tr>
                <td>Цена</td>
                <td>€ @(baseOffer?.Price.Amount.ToString("N0"))</td>
                <td>€ @(Model.ActiveOffers.ElementAtOrDefault(0)?.Price.Amount.ToString("N0"))</td>
                <td>€ @(Model.ActiveOffers.ElementAtOrDefault(1)?.Price.Amount.ToString("N0"))</td>
                <td>€ @(Model.ActiveOffers.ElementAtOrDefault(2)?.Price.Amount.ToString("N0"))</td>
            </tr>
            <!-- Additional rows for other property comparisons -->
            <tr>
                <td>Тип имот</td>
                <td>@(baseOffer?.EstateTypeName)<br/>Ref.ID: @(baseOffer?.Id)</td>
                <td>@(Model.ActiveOffers.ElementAtOrDefault(0)?.EstateTypeName)<br/>Ref.ID: @(Model.ActiveOffers.ElementAtOrDefault(0)?.Id)
                </td>
                <td>@(Model.ActiveOffers.ElementAtOrDefault(1)?.EstateTypeName)<br/>Ref.ID: @(Model.ActiveOffers.ElementAtOrDefault(1)?.Id)
                </td>
                <td>@(Model.ActiveOffers.ElementAtOrDefault(2)?.EstateTypeName)<br/>Ref.ID: @(Model.ActiveOffers.ElementAtOrDefault(2)?.Id)
                </td>
            </tr>
            <tr>
                <td>Вид</td>
                <td>с обзавеждане</td>
                <td>с обзавеждане</td>
                <td>на шпакловка и замазка</td>
                <td>на шпакловка и замазка</td>
            </tr>
            <tr>
                <td>Адрес на имота</td>
                <td>@(baseOffer?.EstateAddress.PopulatedPlaceName), @(baseOffer?.EstateAddress.DistrictName)</td>
                <td>@(Model.ActiveOffers.ElementAtOrDefault(0)?.EstateAddress.PopulatedPlaceName), @(Model.ActiveOffers.ElementAtOrDefault(0)?.EstateAddress.DistrictName)</td>
                <td>@(Model.ActiveOffers.ElementAtOrDefault(1)?.EstateAddress.PopulatedPlaceName), @(Model.ActiveOffers.ElementAtOrDefault(1)?.EstateAddress.DistrictName)</td>
                <td>@(Model.ActiveOffers.ElementAtOrDefault(2)?.EstateAddress.PopulatedPlaceName), @(Model.ActiveOffers.ElementAtOrDefault(2)?.EstateAddress.DistrictName)</td>
            </tr>
            <tr>
                <td>РЗП</td>
                <td>@(baseOffer?.EstateArea)</td>
                <td>@(Model.ActiveOffers.ElementAtOrDefault(0)?.EstateArea)</td>
                <td>@(Model.ActiveOffers.ElementAtOrDefault(1)?.EstateArea)</td>
                <td>@(Model.ActiveOffers.ElementAtOrDefault(2)?.EstateArea)</td>
            </tr>
            <tr>
                <td>Брой спални</td>
                <td>@(baseOffer?.EstateBedroomsCount)</td>
                <td>@(Model.ActiveOffers.ElementAtOrDefault(0)?.EstateBedroomsCount)</td>
                <td>@(Model.ActiveOffers.ElementAtOrDefault(1)?.EstateBedroomsCount)</td>
                <td>@(Model.ActiveOffers.ElementAtOrDefault(2)?.EstateBedroomsCount)</td>
            </tr>
            <tr>
                <td>Брой бани</td>
                <td>@(baseOffer?.EstateBathroomsCount)</td>
                <td>@(Model.ActiveOffers.ElementAtOrDefault(0)?.EstateBathroomsCount)</td>
                <td>@(Model.ActiveOffers.ElementAtOrDefault(1)?.EstateBathroomsCount)</td>
                <td>@(Model.ActiveOffers.ElementAtOrDefault(2)?.EstateBathroomsCount)</td>
            </tr>
            <tr>
                <td>Асансьор</td>
                <td>да</td>
                <td>да</td>
                <td>да</td>
                <td>да</td>
            </tr>
            </tbody>
        </table>
    </div>

    <!-- Property Valuation -->
    <div class="report-section property-valuation">
        <h2>Обобщена оценка на имота</h2>

        @if (baseOffer != null && baseOffer.Images.Any())
        {
            <div class="property-main-image">
                @* <img src="@baseOffer.Images.First().Url" alt="Property Main Image" /> *@
            </div>
        }

        <div class="property-summary">
            <h3>Общо описание на имота:</h3>
            <p>
                @company.Name има удоволствието да Ви предложи @baseOffer.EstateDescription
            </p>
        </div>

        <div class="property-strengths-weaknesses">
            <div class="strengths">
                <h3>Силни страни:</h3>
                <ul>
                    <li>Площ - 380 кв.м.</li>
                    <li>Спални - 3 бр.</li>
                    <li>Етап на завършеност - Акт 16</li>
                    <li>Обзавеждане - с обзавеждане</li>
                    <li>Отопление - ТЕЦ</li>
                    <li>Паркинг - Друго / Подземно паркомясто - 30 000 евро</li>
                </ul>
            </div>
            <div class="weaknesses">
                <h3>Слаби страни:</h3>
                <ul>
                    <li>Едно паркомясто</li>
                </ul>
            </div>
        </div>

        <div class="property-pricing">
            <p>Оптимистична цена: € 1 700 000</p>
            <p>Песимистична цена: € 1 400 000</p>
            <p>Пазарна цена: € 1 550 000</p>
        </div>
    </div>

    <!-- Market Information -->
    <div class="report-section market-info">
        <h2>Обща информация за пазара</h2>

        <ul class="market-points">
            <li>
                Пазарът от началото на 2024 е много активен. Продължава тенденцията на нарастващи запитвания от основния
                имотен портал, което е обичайният индикатор за интереса на купувачи към един или наемател имот.
            </li>
            <li>
                Началото на годината започна с намаляващо предлагане на оферти за продажба в последните три месеца
                тенденцията е за плавно нарастване на предлагането. Основно това се дължи на нови проекти, докато
                вторичния
                пазар бележи задържане.
            </li>
            <li>
                Наблюдава трайно нарастване на цените за последните 6-9 месеца, предимно на вторичния пазара.
                Първоначално
                обявените цени често са до 20% по-високи от средните прогнозни. Отстъпки се правят в рамките около 5%, а
                повече
                от 50% от продажбите стават на офертна цена. В същото време наблюдаваме все по-бавно вземане на решение
                за
                покупка от страна на купувачите и нужда от повече среди.
            </li>
            <li>
                Инвеститорите са все по-склонни да се доверяват на посредници с доказани позиции и голяма мрежа от
                консултанти.
            </li>
        </ul>
    </div>

    <!-- About Us -->
    <div class="report-section about-us">
        <h2>Кои сме ние</h2>

        <div class="company-info">
            <p>
                Unique Estates е създадена през 2006 г. като специализирана консултантска компания за продажба
                и отдаване под наем на имоти в две направления - luxury и executive. Обслужва клиенти в луксозния и
                корпоративния сегмент, дипломати и чужди граждани.
            </p>

            <p>
                Портфолиото от имоти е прецизно подбрано и разделено в селекции, които представят различните
                типове имоти: уникални градски и извънградски имоти, прекрасни имения, имоти на морето, в планината,
                голф
                дестинации, емблематични имоти в стара София и имоти по света.
            </p>

            <p>
                Екипът на Unique Estates е изграждан с внимание към опита и компетентността. Нашите професионално
                обучени консултанти са на ваше разположение 7 дни в седмицата. Като опитни посредници те ще ви
                съдействат да закупите или продадете своя имот възможно най-бързо, при най-добрите условия.
            </p>
        </div>
    </div>

    <!-- Footer -->
    <div class="report-footer">
        <div class="broker-info">
            <h3>КАЛИНА БОЯДЖИЕВА</h3>
            <p>+ *********** 660</p>
        </div>

        <div class="company-contact">
            <p>+*********** 600 / <EMAIL> / www.UES.bg</p>
            <p>гр. София, ул. Оборище 10 / бул. Патриарх Евтимий 17</p>
        </div>
    </div>
</div>

<style>
    /* General Styles */
    .cma-report {
        font-family: 'Segoe UI', Arial, sans-serif;
        color: #333;
        max-width: 1200px;
        margin: 0 auto;
        padding: 15px;
    }

    .cma-report.address {
        h1, h2 {
            color: #22205f;
        }
    }

    .report-header {
        display: flex;
        flex-direction: column;
        justify-content: stretch;
        align-items: center;
        border-bottom: 1px solid #ddd;
        padding: 20px;
        background-color: #ccc;
    }

    .logo-container {
        max-height: 40px;
    }

    .logo {
        height: 100%;
        max-height: 100%;
    }

    .report-title {
        font-size: 36px;
        font-weight: normal;
        letter-spacing: 2px;
    }

    .report-section {
        margin-bottom: 40px;
        padding-bottom: 20px;
        border-bottom: 1px solid #ddd;
    }

    h2 {
        font-size: 24px;
        margin-bottom: 20px;
        color: #000;
        font-weight: 500;
    }

    h3 {
        font-size: 18px;
        margin-bottom: 10px;
        font-weight: 500;
    }

    /* Introduction Section */
    .introduction {
        line-height: 1.6;
        font-size: 16px;
    }

    /* Table of Contents */
    .toc ol {
        padding-left: 20px;
    }

    .toc li {
        margin-bottom: 8px;
        font-size: 16px;
    }

    /* Property Info */
    .property-images {
        display: flex;
        gap: 15px;
        margin-bottom: 20px;
    }

    .property-image {
        flex: 1;
        max-width: 33%;
    }

    .property-image img {
        width: 100%;
        height: auto;
        border-radius: 4px;
    }

    .property-details {
        display: flex;
        margin-bottom: 20px;
    }

    .property-details-left, .property-details-right {
        flex: 1;
    }

    .detail-item {
        margin-bottom: 8px;
    }

    .detail-label {
        font-weight: 500;
        margin-right: 5px;
    }

    .property-description {
        margin-top: 20px;
    }

    .property-description ul {
        padding-left: 20px;
        list-style-type: disc;
    }

    .property-description p {
        margin-bottom: 10px;
        line-height: 1.5;
    }

    /* Properties Table */
    .properties-table {
        width: 100%;
        border-collapse: collapse;
        font-size: 14px;
        margin-top: 20px;
    }

    .properties-table th, .properties-table td {
        border: 1px solid #ddd;
        padding: 8px;
        text-align: left;
    }

    .properties-table th {
        background-color: #f2f2f2;
        font-weight: 500;
    }

    .featured-property {
        background-color: #f5f5f5;
    }

    .section-header {
        background-color: #eee;
        font-weight: 500;
    }

    .property-icon {
        text-align: center;
        width: 30px;
    }

    .star-icon {
        color: gold;
        font-size: 18px;
    }

    .active-icon {
        color: green;
        font-size: 18px;
    }

    .unfulfilled-icon {
        color: orange;
        font-size: 18px;
    }

    .sold-icon {
        color: red;
        font-size: 18px;
    }

    /* Comparison Table */
    .comparison-table {
        width: 100%;
        border-collapse: collapse;
        font-size: 14px;
    }

    .comparison-table th, .comparison-table td {
        border: 1px solid #ddd;
        padding: 8px;
        text-align: left;
    }

    .comparison-table th {
        background-color: #f2f2f2;
        font-weight: 500;
    }

    .property-photo {
        width: 20%;
    }

    .property-photo img {
        width: 100%;
        height: auto;
        border-radius: 4px;
    }

    /* Property Valuation */
    .property-main-image {
        margin-bottom: 20px;
    }

    .property-main-image img {
        width: 100%;
        max-height: 400px;
        object-fit: contain;
        border-radius: 4px;
    }

    .property-summary {
        margin-bottom: 20px;
    }

    .property-strengths-weaknesses {
        display: flex;
        margin-bottom: 20px;
    }

    .strengths, .weaknesses {
        flex: 1;
        padding: 15px;
    }

    .strengths {
        border-right: 1px solid #ddd;
    }

    .strengths ul, .weaknesses ul {
        padding-left: 20px;
        list-style-type: none;
    }

    .strengths li, .weaknesses li {
        margin-bottom: 5px;
        position: relative;
        padding-left: 5px;
    }

    .property-pricing {
        margin-top: 30px;
        text-align: center;
        font-size: 18px;
    }

    .property-pricing p {
        margin-bottom: 10px;
    }

    /* Market Information */
    .market-points {
        list-style-type: square;
        padding-left: 20px;
    }

    .market-points li {
        margin-bottom: 15px;
        line-height: 1.5;
    }

    /* About Us */
    .company-info p {
        margin-bottom: 15px;
        line-height: 1.5;
    }

    /* Footer */
    .report-footer {
        display: flex;
        justify-content: space-between;
        padding-top: 20px;
        border-top: 1px solid #ddd;
        font-size: 14px;
    }

    .broker-info {
        flex: 1;
    }

    .company-contact {
        flex: 2;
        text-align: right;
    }

    .broker-info h3 {
        font-size: 16px;
        margin-bottom: 5px;
    }

    /* Special styling for the company logo */
    .logo-container {
        display: flex;
        justify-content: center;
        margin-bottom: 20px;
    }

    /* Gold accent color for luxury branding */
    .logo, h2 {
        color: #b8a369;
    }

</style>