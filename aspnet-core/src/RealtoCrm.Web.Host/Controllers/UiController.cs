using System.Threading.Tasks;
using Abp.Auditing;
using Abp.Authorization;
using Abp.Authorization.Users;
using Abp.Configuration.Startup;
using Abp.UI;
using Microsoft.AspNetCore.Mvc;
using RealtoCrm.Authorization;
using RealtoCrm.Authorization.Accounts;
using RealtoCrm.Authorization.Accounts.Dto;
using RealtoCrm.Authorization.Users;
using RealtoCrm.Identity;
using RealtoCrm.MultiTenancy;
using RealtoCrm.Web.Models.Ui;
using RealtoCrm.Web.Session;

namespace RealtoCrm.Web.Controllers;

public class UiController(
    IPerRequestSessionCache sessionCache,
    IMultiTenancyConfig multiTenancyConfig,
    IAccountAppService accountAppService,
    LogInManager logInManager,
    SignInManager signInManager,
    AbpLoginResultTypeHelper abpLoginResultTypeHelper)
    : RealtoCrmControllerBase
{
    [DisableAuditing]
    public async Task<IActionResult> Index()
    {
        var model = new HomePageModel
        {
            LoginInformation = await sessionCache.GetCurrentLoginInformationsAsync(),
            IsMultiTenancyEnabled = multiTenancyConfig.IsEnabled
        };

        if (model.LoginInformation?.User == null)
        {
            return this.RedirectToAction("Login");
        }

        return this.View(model);
    }

    [HttpGet]
    public async Task<IActionResult> Login(string returnUrl = "")
    {
        if (!string.IsNullOrEmpty(returnUrl))
        {
            this.ViewBag.ReturnUrl = returnUrl;
        }

        await signInManager.SignOutAsync();
            
        return this.View();
    }

    [HttpPost]
    public async Task<IActionResult> Login(LoginModel model, string returnUrl = "")
    {
        if (model.TenancyName != null)
        {
            var isTenantAvailable = await accountAppService.IsTenantAvailable(new IsTenantAvailableInput
            {
                TenancyName = model.TenancyName
            });

            switch (isTenantAvailable.State)
            {
                case TenantAvailabilityState.InActive:
                    throw new UserFriendlyException(this.L("TenantIsNotActive", model.TenancyName));
                case TenantAvailabilityState.NotFound:
                    throw new UserFriendlyException(this.L("ThereIsNoTenantDefinedWithName{0}", model.TenancyName));
            }
        }

        var loginResult = await this.GetLoginResultAsync(model.UserNameOrEmailAddress, model.Password, model.TenancyName);

        if (loginResult.User.ShouldChangePasswordOnNextLogin)
        {
            throw new UserFriendlyException(this.L("RequiresPasswordChange"));
        }

        var signInResult = await signInManager.SignInOrTwoFactorAsync(loginResult, model.RememberMe);

        if (signInResult.RequiresTwoFactor)
        {
            throw new UserFriendlyException(this.L("RequiresTwoFactorAuth"));
        }

        if (!string.IsNullOrEmpty(returnUrl))
        {
            return this.Redirect(returnUrl);
        }

        return this.RedirectToAction("Index");
    }

    public async Task<ActionResult> Logout()
    {
        await signInManager.SignOutAsync();

        return this.RedirectToAction("Index");
    }

    private async Task<AbpLoginResult<Tenant, User>> GetLoginResultAsync(string usernameOrEmailAddress, string password, string tenancyName)
    {
        var loginResult = await logInManager.LoginAsync(usernameOrEmailAddress, password, tenancyName);

        switch (loginResult.Result)
        {
            case AbpLoginResultType.Success:
                return loginResult;
            default:
                throw abpLoginResultTypeHelper.CreateExceptionForFailedLoginAttempt(loginResult.Result, usernameOrEmailAddress, tenancyName);
        }
    }
}