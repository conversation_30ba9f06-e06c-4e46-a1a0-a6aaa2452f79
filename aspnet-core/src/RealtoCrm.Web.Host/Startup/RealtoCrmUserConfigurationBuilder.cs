using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Features;
using Abp.Application.Navigation;
using Abp.Authorization;
using Abp.Configuration;
using Abp.Configuration.Startup;
using Abp.Dependency;
using Abp.Localization;
using Abp.Runtime.Session;
using Abp.Web.Configuration;
using Abp.Web.Models.AbpUserConfiguration;
using Abp.Web.Security.AntiForgery;

namespace RealtoCrm.Web.Startup;

public class RealtoCrmUserConfigurationBuilder(
    IMultiTenancyConfig multiTenancyConfig,
    ILanguageManager languageManager,
    ILocalizationManager localizationManager,
    IFeatureManager featureManager,
    IFeatureChecker featureChecker,
    IPermissionManager permissionManager,
    IUserNavigationManager userNavigationManager,
    ISettingDefinitionManager settingDefinitionManager,
    ISettingManager settingManager,
    IAbpAntiForgeryConfiguration abpAntiForgeryConfiguration,
    IAbpSession abpSession,
    IPermissionChecker permissionChecker,
    IIocResolver iocResolver,
    IAbpStartupConfiguration startupConfiguration)
    : AbpUserConfigurationBuilder(multiTenancyConfig,
        languageManager,
        localizationManager,
        featureManager,
        featureChecker,
        permissionManager,
        userNavigationManager,
        settingDefinitionManager,
        settingManager,
        abpAntiForgeryConfiguration,
        abpSession,
        permissionChecker,
        iocResolver,
        startupConfiguration)
{
    protected override async Task<AbpUserAuthConfigDto> GetUserAuthConfig()
    {
        var config = new AbpUserAuthConfigDto();

        var allPermissionNames = PermissionManager.GetAllPermissions()
            .Select(p => p.Name).ToList();
        var grantedPermissionNames = new List<string>();

        if (AbpSession.UserId.HasValue)
        {
            foreach (var permissionName in allPermissionNames)
            {
                if (await PermissionChecker.IsGrantedAsync(permissionName))
                {
                    grantedPermissionNames.Add(permissionName);
                }
            }
        }

        config.AllPermissions = allPermissionNames.ToDictionary(permissionName => permissionName, permissionName => "true");
        config.GrantedPermissions = grantedPermissionNames.ToDictionary(permissionName => permissionName, permissionName => "true");

        return config;
    }
}