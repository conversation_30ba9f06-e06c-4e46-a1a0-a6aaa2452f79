using System.Collections.Generic;
using Abp.AspNetZeroCore;
using Abp.AspNetZeroCore.Web.Authentication.External;
using Abp.AspNetZeroCore.Web.Authentication.External.Facebook;
using Abp.AspNetZeroCore.Web.Authentication.External.Google;
using Abp.AspNetZeroCore.Web.Authentication.External.Microsoft;
using Abp.AspNetZeroCore.Web.Authentication.External.OpenIdConnect;
using Abp.AspNetZeroCore.Web.Authentication.External.Twitter;
using Abp.AspNetZeroCore.Web.Authentication.External.WsFederation;
using Abp.Configuration.Startup;
using Abp.Dependency;
using Abp.Extensions;
using Abp.Modules;
using Abp.Reflection.Extensions;
using Abp.Threading.BackgroundWorkers;
using Abp.Timing;
using Abp.Web.Configuration;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using RealtoCrm.Auditing;
using RealtoCrm.Authorization.Users.Password;
using RealtoCrm.Configuration;
using RealtoCrm.EntityFrameworkCore;
using RealtoCrm.MultiTenancy;
using RealtoCrm.Web.Startup.ExternalLoginInfoProviders;

namespace RealtoCrm.Web.Startup;

[DependsOn(
    typeof(RealtoCrmWebCoreModule)
)]
public class RealtoCrmWebHostModule(IWebHostEnvironment env)
    : AbpModule
{
    private readonly IConfigurationRoot appConfiguration = env.GetAppConfiguration();

    public override void PreInitialize()
    {
        this.Configuration.Modules.AbpWebCommon().MultiTenancy.DomainFormat =
            this.appConfiguration["App:ServerRootAddress"] ?? "https://localhost:44301/";
        this.Configuration.Modules.AspNetZero().LicenseCode = this.appConfiguration["AbpZeroLicenseCode"];
        
        Configuration.ReplaceService<AbpUserConfigurationBuilder, RealtoCrmUserConfigurationBuilder>(
            DependencyLifeStyle.Transient
        );
    }

    public override void Initialize()
    {
        this.IocManager.RegisterAssemblyByConvention(typeof(RealtoCrmWebHostModule).GetAssembly());
    }

    public override void PostInitialize()
    {
        using (var scope = this.IocManager.CreateScope())
        {
            if (!scope.Resolve<DatabaseCheckHelper>().Exist(this.appConfiguration["ConnectionStrings:Default"]))
            {
                return;
            }
        }

        var workManager = this.IocManager.Resolve<IBackgroundWorkerManager>();
        if (this.IocManager.Resolve<IMultiTenancyConfig>().IsEnabled)
        {
            workManager.Add(this.IocManager.Resolve<SubscriptionExpirationCheckWorker>());
            workManager.Add(this.IocManager.Resolve<SubscriptionExpireEmailNotifierWorker>());
            workManager.Add(this.IocManager.Resolve<SubscriptionPaymentNotCompletedEmailNotifierWorker>());
        }

        var expiredAuditLogDeleterWorker = this.IocManager.Resolve<ExpiredAuditLogDeleterWorker>();
        if (this.Configuration.Auditing.IsEnabled && expiredAuditLogDeleterWorker.IsEnabled)
        {
            workManager.Add(expiredAuditLogDeleterWorker);
        }

        workManager.Add(this.IocManager.Resolve<PasswordExpirationBackgroundWorker>());

        this.ConfigureExternalAuthProviders();
    }

    private void ConfigureExternalAuthProviders()
    {
        var externalAuthConfiguration = this.IocManager.Resolve<ExternalAuthConfiguration>();

        if (bool.Parse(this.appConfiguration["Authentication:OpenId:IsEnabled"]))
        {
            if (bool.Parse(this.appConfiguration["Authentication:AllowSocialLoginSettingsPerTenant"]))
            {
                externalAuthConfiguration.ExternalLoginInfoProviders.Add(this.IocManager.Resolve<TenantBasedOpenIdConnectExternalLoginInfoProvider>());
            }
            else
            {
                var jsonClaimMappings = new List<JsonClaimMap>();
                this.appConfiguration.GetSection("Authentication:OpenId:ClaimsMapping").Bind(jsonClaimMappings);

                externalAuthConfiguration.ExternalLoginInfoProviders.Add(
                    new OpenIdConnectExternalLoginInfoProvider(
                        this.appConfiguration["Authentication:OpenId:ClientId"],
                        this.appConfiguration["Authentication:OpenId:ClientSecret"],
                        this.appConfiguration["Authentication:OpenId:Authority"],
                        this.appConfiguration["Authentication:OpenId:LoginUrl"],
                        bool.Parse(this.appConfiguration["Authentication:OpenId:ValidateIssuer"]),
                        this.appConfiguration["Authentication:OpenId:ResponseType"],
                        jsonClaimMappings
                    )
                );
            }
        }

        if (bool.Parse(this.appConfiguration["Authentication:WsFederation:IsEnabled"]))
        {
            if (bool.Parse(this.appConfiguration["Authentication:AllowSocialLoginSettingsPerTenant"]))
            {
                externalAuthConfiguration.ExternalLoginInfoProviders.Add(this.IocManager.Resolve<TenantBasedWsFederationExternalLoginInfoProvider>());
            }
            else
            {
                var jsonClaimMappings = new List<JsonClaimMap>();
                this.appConfiguration.GetSection("Authentication:WsFederation:ClaimsMapping").Bind(jsonClaimMappings);

                externalAuthConfiguration.ExternalLoginInfoProviders.Add(
                    new WsFederationExternalLoginInfoProvider(
                        this.appConfiguration["Authentication:WsFederation:ClientId"],
                        this.appConfiguration["Authentication:WsFederation:Tenant"],
                        this.appConfiguration["Authentication:WsFederation:MetaDataAddress"],
                        this.appConfiguration["Authentication:WsFederation:Authority"],
                        jsonClaimMappings
                    )
                );
            }
        }

        if (bool.Parse(this.appConfiguration["Authentication:Facebook:IsEnabled"]))
        {
            if (bool.Parse(this.appConfiguration["Authentication:AllowSocialLoginSettingsPerTenant"]))
            {
                externalAuthConfiguration.ExternalLoginInfoProviders.Add(this.IocManager.Resolve<TenantBasedFacebookExternalLoginInfoProvider>());
            }
            else
            {
                externalAuthConfiguration.ExternalLoginInfoProviders.Add(new FacebookExternalLoginInfoProvider(
                    this.appConfiguration["Authentication:Facebook:AppId"],
                    this.appConfiguration["Authentication:Facebook:AppSecret"]
                ));
            }
        }

        if (bool.Parse(this.appConfiguration["Authentication:Twitter:IsEnabled"]))
        {
            if (bool.Parse(this.appConfiguration["Authentication:AllowSocialLoginSettingsPerTenant"]))
            {
                externalAuthConfiguration.ExternalLoginInfoProviders.Add(this.IocManager.Resolve<TenantBasedTwitterExternalLoginInfoProvider>());
            }
            else
            {
                var twitterExternalLoginInfoProvider = new TwitterExternalLoginInfoProvider(
                    this.appConfiguration["Authentication:Twitter:ConsumerKey"],
                    this.appConfiguration["Authentication:Twitter:ConsumerSecret"],
                    this.appConfiguration["App:ClientRootAddress"].EnsureEndsWith('/') + "account/login"
                );

                externalAuthConfiguration.ExternalLoginInfoProviders.Add(twitterExternalLoginInfoProvider);
            }
        }

        if (bool.Parse(this.appConfiguration["Authentication:Google:IsEnabled"]))
        {
            if (bool.Parse(this.appConfiguration["Authentication:AllowSocialLoginSettingsPerTenant"]))
            {
                externalAuthConfiguration.ExternalLoginInfoProviders.Add(this.IocManager.Resolve<TenantBasedGoogleExternalLoginInfoProvider>());
            }
            else
            {
                externalAuthConfiguration.ExternalLoginInfoProviders.Add(
                    new GoogleExternalLoginInfoProvider(
                        this.appConfiguration["Authentication:Google:ClientId"],
                        this.appConfiguration["Authentication:Google:ClientSecret"],
                        this.appConfiguration["Authentication:Google:UserInfoEndpoint"]
                    )
                );
            }
        }

        if (bool.Parse(this.appConfiguration["Authentication:Microsoft:IsEnabled"]))
        {
            if (bool.Parse(this.appConfiguration["Authentication:AllowSocialLoginSettingsPerTenant"]))
            {
                externalAuthConfiguration.ExternalLoginInfoProviders.Add(this.IocManager.Resolve<TenantBasedMicrosoftExternalLoginInfoProvider>());
            }
            else
            {
                externalAuthConfiguration.ExternalLoginInfoProviders.Add(
                    new MicrosoftExternalLoginInfoProvider(
                        this.appConfiguration["Authentication:Microsoft:ConsumerKey"],
                        this.appConfiguration["Authentication:Microsoft:ConsumerSecret"]
                    )
                );
            }
        }
    }
}