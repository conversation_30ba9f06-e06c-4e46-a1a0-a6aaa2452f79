using Abp.AspNetZeroCore.Web.Authentication.External.Facebook;
using Abp.AspNetZeroCore.Web.Authentication.External.Google;
using Abp.AspNetZeroCore.Web.Authentication.External.Microsoft;
using Abp.AspNetZeroCore.Web.Authentication.External.OpenIdConnect;
using Abp.AspNetZeroCore.Web.Authentication.External.Twitter;
using Abp.AspNetZeroCore.Web.Authentication.External.WsFederation;
using Abp.Dependency;
using Abp.Runtime.Caching;
using Abp.Runtime.Session;
using RealtoCrm.Configuration;

namespace RealtoCrm.Web.Startup.ExternalLoginInfoProviders;

public class ExternalLoginOptionsCacheManager(ICacheManager cacheManager, IAbpSession abpSession) : IExternalLoginOptionsCacheManager, ITransientDependency
{
    public void ClearCache()
    {
        cacheManager.GetExternalLoginInfoProviderCache().Remove(this.GetCacheKey(FacebookAuthProviderApi.Name));
        cacheManager.GetExternalLoginInfoProviderCache().Remove(this.GetCacheKey(GoogleAuthProviderApi.Name));
        cacheManager.GetExternalLoginInfoProviderCache().Remove(this.GetCacheKey(TwitterAuthProviderApi.Name));
        cacheManager.GetExternalLoginInfoProviderCache().Remove(this.GetCacheKey(MicrosoftAuthProviderApi.Name));
        cacheManager.GetExternalLoginInfoProviderCache().Remove(this.GetCacheKey(OpenIdConnectAuthProviderApi.Name));
        cacheManager.GetExternalLoginInfoProviderCache().Remove(this.GetCacheKey(WsFederationAuthProviderApi.Name));
    }

    private string GetCacheKey(string name)
    {
        if (abpSession.TenantId.HasValue)
        {
            return $"{name}-{abpSession.TenantId.Value}";
        }

        return $"{name}";
    }
}