using Abp.AspNetZeroCore.Web.Authentication.External;
using Abp.Runtime.Caching;
using Abp.Runtime.Session;
namespace RealtoCrm.Web.Startup.ExternalLoginInfoProviders;

public abstract class TenantBasedExternalLoginInfoProviderBase(
    IAbpSession abpSession,
    ICacheManager cacheManager) : IExternalLoginInfoProvider
{
    public abstract string Name { get; }

    protected abstract bool TenantHasSettings();
        
    protected abstract ExternalLoginProviderInfo GetTenantInformation();
        
    protected abstract ExternalLoginProviderInfo GetHostInformation();
        
    public virtual ExternalLoginProviderInfo GetExternalLoginInfo()
    {
        if (abpSession.TenantId.HasValue && this.TenantHasSettings())
        {
            return cacheManager.GetExternalLoginInfoProviderCache()
                .Get(this.GetCacheKey(), this.GetTenantInformation);
        }
            
        return cacheManager.GetExternalLoginInfoProviderCache()
            .Get(this.GetCacheKey(), this.GetHostInformation);
    }
        
    private string GetCacheKey()
    {
        if (abpSession.TenantId.HasValue)
        {
            return $"{this.Name}-{abpSession.TenantId.Value}";
        }

        return $"{this.Name}";
    }
}