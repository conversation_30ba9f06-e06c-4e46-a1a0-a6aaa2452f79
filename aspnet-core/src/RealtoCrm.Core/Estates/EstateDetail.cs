using Abp.Domain.Entities.Auditing;
using RealtoCrm.Nomenclatures;
namespace RealtoCrm.Estates;

public class EstateDetail : FullAuditedEntity<int>
{
    public Estate? Estate { get; set; }
    
    public double? KINTArea { get; set; }
    public double? UsefulFloorArea { get; set; }
    
    public double? DensityArea { get; set; }
    
    public double? PossibleArea { get; set; }
    
    public double? BuildupArea { get; set; }
    
    public double? PlotArea { get; set; }
    public int? HouseTypeId { get; set; }
    public HouseType? HouseType { get; set; }
    public int? InfrastructureId { get; set; }
    public Infrastructure? Infrastructure { get; set; }
    
    public int? FenceId { get; set; }
    public Fence? Fence { get; set; }
        
    public int? ConstructionPurposeId { get; set; }
    public ConstructionPurpose? ConstructionPurpose { get; set; }
    
    public int? BuildingPurposeId { get; set; }
    public ConstructionPurpose? BuildingPurpose { get; set; }
}

