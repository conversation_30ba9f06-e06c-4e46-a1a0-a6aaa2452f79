namespace RealtoCrm.Estates;

using System;
using Abp.Domain.Entities.Auditing;
using Files;

public class EstateFile : IFullAudited
{
    public int EstateId { get; set; }

    public Estate Estate { get; set; } = default!;

    public int FileId { get; set; }

    public File File { get; set; } = default!;

    public DateTime CreationTime { get; set; }

    public long? CreatorUserId { get; set; }

    public DateTime? LastModificationTime { get; set; }

    public long? LastModifierUserId { get; set; }

    public bool IsDeleted { get; set; }

    public DateTime? DeletionTime { get; set; }

    public long? DeleterUserId { get; set; }
}