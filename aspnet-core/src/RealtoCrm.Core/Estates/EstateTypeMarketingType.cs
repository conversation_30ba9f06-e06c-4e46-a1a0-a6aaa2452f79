namespace RealtoCrm.Estates;

using System;
using Abp.Domain.Entities.Auditing;

public class EstateTypeMarketingType : IFullAudited
{
    public int EstateTypeId { get; set; }

    public EstateType EstateType { get; set; } = default!;

    public int MarketingTypeId { get; set; }

    public EstateType MarketingType { get; set; } = default!;

    public bool IsDefault { get; set; }

    public DateTime CreationTime { get; set; }

    public long? CreatorUserId { get; set; }

    public DateTime? LastModificationTime { get; set; }

    public long? LastModifierUserId { get; set; }

    public bool IsDeleted { get; set; }

    public DateTime? DeletionTime { get; set; }

    public long? DeleterUserId { get; set; }
}