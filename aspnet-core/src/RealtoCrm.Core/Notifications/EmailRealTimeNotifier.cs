using System.Linq;
using System.Net.Mail;
using System.Threading.Tasks;
using Abp.Dependency;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.Extensions;
using Abp.Net.Mail;
using Abp.Notifications;
using Castle.Core.Logging;
using Microsoft.EntityFrameworkCore;
using RealtoCrm.Authorization.Users;

namespace RealtoCrm.Notifications;

public class EmailRealTimeNotifier(
    IUnitOfWorkManager unitOfWorkManager,
    IEmailSender emailSender,
    IRepository<User, long> userRepository) : IRealTimeNotifier, ITransientDependency
{
    public bool UseOnlyIfRequestedAsTarget => true;

    public ILogger Logger { get; set; } = NullLogger.Instance;

    public async Task SendNotificationsAsync(UserNotification[] userNotifications)
    {
        var userNotificationsGroupedByTenant = userNotifications.GroupBy(un => un.TenantId);
        foreach (var userNotificationByTenant in userNotificationsGroupedByTenant)
        {
            using (unitOfWorkManager.Current.SetTenantId(userNotificationByTenant.First().TenantId))
            {
                var allUserIds = userNotificationByTenant.ToList().Select(x => x.UserId).Distinct().ToList();
                var usersToNotify = await userRepository.GetAll()
                    .Where(x => allUserIds.Contains(x.Id))
                    .Select(u => new
                        {
                            u.Id,
                            u.EmailAddress,
                            u.Name
                        }
                    ).ToListAsync();

                foreach (var userNotification in userNotificationByTenant)
                {
                    if (!userNotification.Notification.Data.Properties.ContainsKey("Message") ||
                        userNotification.Notification.Data["Message"] is not string)
                    {
                        this.Logger.Info("Message property is not found in notification data. Notification cannot be sent.");
                        continue;
                    }

                    var user = usersToNotify.FirstOrDefault(x => x.Id == userNotification.UserId);
                    if (user == null)
                    {
                        this.Logger.Info("Can not send sms to user: " + userNotification.UserId + ". User does not exists!");
                        continue;
                    }

                    if (user.EmailAddress.IsNullOrWhiteSpace())
                    {
                        this.Logger.Info("Can not send email to user: " + user.Name + ". User's email is empty!");
                        continue;
                    }

                    await emailSender.SendAsync(new MailMessage
                    {
                        To = { user.EmailAddress },
                        Subject = "RealtoCrm Notification",
                        Body = userNotification.Notification.Data["Message"].ToString(),
                        IsBodyHtml = true
                    });
                }
            }
        }
    }
}