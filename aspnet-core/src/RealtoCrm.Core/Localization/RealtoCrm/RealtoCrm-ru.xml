<?xml version="1.0" encoding="utf-8" ?>
<localizationDictionary culture="ru">
  <texts>
    <text name="HomePage">Главная страница</text>
    <text name="AboutUs">О нас</text>
    <text name="Administration">Администрирование</text>
    <text name="Roles">Роли</text>
    <text name="Users">Пользователи</text>
    <text name="InvalidFormMessage">Данные в форме неверны. Пожалуйста, проверьте и измените их.</text>
    <text name="InvalidUserNameOrPassword">Неверное имя пользователя или пароль</text>
    <text name="ThereIsNoTenantDefinedWithName{0}">Нет клиента, зарегистрированного под именем {0}</text>
    <text name="TenantIsNotActive">Клиент {0} неактивен.</text>
    <text name="UserIsNotActiveAndCanNotLogin">Пользователь {0} неактивен и не может войти.</text>
    <text name="PleaseEnterLoginInformation">Пожалуйста, введите информацию для входа</text>
    <text name="TenancyName">Имя клиента</text>
    <text name="UserNameOrEmail">Имя пользователя или email</text>
    <text name="Password">Пароль</text>
    <text name="RememberMe">Запомнить</text>
    <text name="LogIn">Войти</text>
    <text name="ForgotPassword">Восстановить пароль</text>
    <text name="DontYouHaveAnAccount">Разве у вас нет профиля?</text>
    <text name="CreateAnAccount">Создать профиль</text>
    <text name="Pages">Страницы</text>
    <text name="RoleName">Имя роли</text>
    <text name="EditRole">Изменить роль</text>
    <text name="CreateNewRole">Создать новую роль</text>
    <text name="RoleProperties">Свойства роли</text>
    <text name="Permissions">Права</text>
    <text name="Save">Сохранить</text>
    <text name="Cancel">Отмена</text>
    <text name="RolesHeaderInfo">Использование ролей для группировки прав.</text>
    <text name="Static">Статичная</text>
    <text name="Actions">Действия</text>
    <text name="Delete">Удалить</text>
    <text name="SavedSuccessfully">Сохранено успешно.</text>
    <text name="AreYouSure">Вы уверены?</text>
    <text name="YesDelete">Да, удалить!</text>
    <text name="DeletingUser">Удаление пользователя</text>
    <text name="UsersHeaderInfo">Управление пользователями и правами.</text>
    <text name="CreateNewUser">Создать нового пользователя</text>
    <text name="UserName">Имя пользователя</text>
    <text name="Name">Имя</text>
    <text name="Surname">Фамилия</text>
    <text name="EmailAddress">Email адрес</text>
    <text name="LastLoginTime">Время последнего входа</text>
    <text name="EditUser">Изменить пользователя</text>
    <text name="UserInformations">Информация пользователя</text>
    <text name="PasswordRepeat">Пароль (повтор)</text>
    <text name="SuccessfullyDeleted">Успешно удалено.</text>
    <text name="RoleDeleteWarningMessage">Роль {0} будет удалена и отвязана от привязанных пользователей.</text>
    <text name="UserDeleteWarningMessage">Пользователь {0} будет удален.</text>
    <text name="Active">Активно</text>
    <text name="EmailConfirm">Email подтвержден</text>
    <text name="Yes">Да</text>
    <text name="No">Нет</text>
    <text name="MySettings">Мои настройки</text>
    <text name="Logout">Выход</text>
    <text name="Reset">Сброс</text>
    <text name="ResetPermissionsTooltip">Эта кнопка немедленно сбрасывает настройки особых прав и сохраняет изменения. Пользователь будет иметь права, привязанные к роли.</text>
    <text name="ResetSuccessfully">Успешно сброшено</text>
    <text name="Tenants">Клиенты</text>
    <text name="TenantsHeaderInfo">Управление клиентами.</text>
    <text name="CreateNewTenant">Создать клиента</text>
    <text name="TenancyCodeName">Кодовое имя клиента</text>
    <text name="TenantName">Имя клиента</text>
    <text name="TenantDeleteWarningMessage">Клиент{0} будет удален.</text>
    <text name="AdminEmailAddress">Email администратора</text>
    <text name="AdminPassword">Пароль администратора</text>
    <text name="AdminPasswordRepeat">Пароль администратора (повтор)</text>
    <text name="EditTenant">Изменить клиента</text>
    <text name="ChangingPermissions">Изменение прав</text>
    <text name="EditingUser">Изменение пользователя</text>
    <text name="CreatingNewUser">Создание нового пользователя</text>
    <text name="Edit">Изменить</text>
    <text name="EditingTenant">Изменение клиента</text>
    <text name="CreatingNewTenant">Создание нового клиента</text>
    <text name="DeletingTenant">Удаление клиента</text>
    <text name="EditingRole">Изменение роли</text>
    <text name="CreatingNewRole">Создание новой роли</text>
    <text name="DeletingRole">Удаление роли</text>
    <text name="CreationTime">Время создания</text>
    <text name="SearchWithThreeDot">Поиск...</text>
    <text name="ResetSpecialPermissions">Сброс особых прав</text>
    <text name="SavingWithThreeDot">Сохранение...</text>
    <text name="Settings">Настройки</text>
    <text name="SaveAll">Сохранить все</text>
    <text name="SettingsHeaderInfo">Отображение и изменение настроек системы.</text>
    <text name="EmailSmtp">Email (SMTP)</text>
    <text name="DefaultFromAddress">Email адрес отправителя по умолчанию</text>
    <text name="DefaultFromDisplayName">Имя отправителя по умолчанию</text>
    <text name="SmtpHost">SMTP хост</text>
    <text name="SmtpPort">SMTP порт</text>
    <text name="UseDefaultCredentials">Использовать учетные данные по умолчанию</text>
    <text name="DomainName">Имя домена</text>
    <text name="UseSsl">Использовать SSL</text>
    <text name="ThisWebSiteRootAddress">Корневой адрес Web сайта</text>
    <text name="ThisWebSiteRootAddress_Hint">Будет использован при построении ссылок на этот Web сайт. Параметр необходим для таких задач, как построение ссылок для сброса пароля. Пример: htttp://mydomain.com/</text>
    <text name="General">Общие</text>
    <text name="SendPasswordResetLink_Information">Ссылка для сброса пароля будет отправлена на ваш Email адрес. Если вы не получите сообщение в течении нескольких минут, пожалуйста, попробуйте снова.</text>
    <text name="Send">Отправить</text>
    <text name="PasswordReset">Сброс пароля</text>
    <text name="InvalidUserNameOrEmailAddress">Неверное имя пользователя или Email адрес</text>
    <text name="PasswordResetEmail_Subject">сброс пароля</text>
    <text name="PasswordResetEmail_Title">сброс пароля.</text>
    <text name="PasswordResetEmail_SubTitle">Этот Email направлен вам для сброса и пересоздания пароля.</text>
    <text name="PasswordResetEmail_ClickTheLinkBelowToResetYourPassword">Пожалуйста, нажмите на ссылку ниже, чтобы сбросить пароль:</text>
    <text name="EmailActivation_Subject">активации Email для профиля</text>
    <text name="EmailActivation_Title">Добро пожаловать.</text>
    <text name="EmailActivation_SubTitle">Этот сообщение отправлено для подтверждения вашего Email адреса.</text>
    <text name="EmailActivation_ClickTheLinkBelowToVerifyYourEmail">Пожалуйста, нажмите на ссылку ниже, чтобы подтвердить Email адрес:</text>
    <text name="InvalidEmailAddress">Неверный Email адрес</text>
    <text name="InvalidPasswordResetCode">Неверный код сброса пароля</text>
    <text name="InvalidPasswordResetCode_Detail">Пожалуйста, удостоверьтесь, что вы попали на эту страницу после перехода по ссылке, отправленной на ваш Email адрес. Если это не решает проблему, попытайтесь повторно запросить новую ссылку на смену пароля.</text>
    <text name="PleaseEnterYourNewPassword">Пожалуйста, введите ваш новый пароль.</text>
    <text name="UserEmailIsNotConfirmedAndCanNotLogin">Ваш Email адрес не подтвержден. Для активации профиля, пожалуйста, проверьте вашу электронную почту и нажмите на ссылку подтверждения Email адреса. Если вы не получили сообщение, нажмите на 'подтвердить Email', чтобы запросить новое сообщение.</text>
    <text name="LoginFailed">Ошибка входа!</text>
    <text name="InvalidEmailConfirmationCode">Неверный код подтверждения Email адреса</text>
    <text name="InvalidEmailConfirmationCode_Detail">Пожалуйста, удостоверьтесь, что вы попали на эту страницу после перехода по ссылке, отправленной на ваш Email адрес. Если это не решает проблему, попытайтесь повторно запросить новый код подтверждения.</text>
    <text name="YourEmailIsConfirmedMessage">Ваш Email адрес успешно подтвержден.</text>
    <text name="ShouldChangePasswordOnNextLogin">Необходимо изменить пароль при следующем входе.</text>
    <text name="SetRandomPassword">Установить произвольный пароль.</text>
    <text name="EmailActivation">Активация по Email</text>
    <text name="SendEmailActivationLink_Information">На ваш Email адрес будет отправлена ссылка для активации. Если вы не получите сообщение через несколько минут, пожалуйста, попробуйте снова.</text>
    <text name="SendActivationEmail">Отправить сообщение для активации.</text>
    <text name="ChangePassword">Изменить пароль</text>
    <text name="CurrentPassword">Текущий пароль</text>
    <text name="NewPassword">Новый пароль</text>
    <text name="NewPasswordRepeat">Новый пароль (повтор)</text>
    <text name="YourPasswordHasChangedSuccessfully">Ваш пароль успешно изменен.</text>
    <text name="SelfUserRegistrationIsDisabledMessage">Вы не можете регистрироваться в систему!</text>
    <text name="SelfUserRegistrationIsDisabledMessage_Detail">Самостоятельная регистрация отключена для пользователей. Для регистрации, пожалуйста, свяжитесь с системным администратором.</text>
    <text name="SuccessfullyRegistered">Успешная регистрация</text>
    <text name="ConfirmationMailSentPleaseClickLinkInTheEmail">Письмо для подтверждения отправлено на адрес {0}. Пожалуйста, перейдите по указанной в сообщении ссылке для активации Email адреса.</text>
    <text name="YourAccountIsWaitingToBeActivatedByAdmin">Ваш профиль ожидает активации системным администратором.</text>
    <text name="NameSurname">Фамилия</text>
    <text name="UserManagement">Управление пользователями</text>
    <text name="AllowUsersToRegisterThemselves">Позволить пользователям регистрироваться в системе.</text>
    <text name="AllowUsersToRegisterThemselves_Hint">Если отключить этот параметр, пользователи будут добавляться только администратором на странице управления пользователями.</text>
    <text name="NewRegisteredUsersIsActiveByDefault">Новые зарегистрированные пользователи активны по умолчанию.</text>
    <text name="NewRegisteredUsersIsActiveByDefault_Hint">Если отключить этот параметр, то новые пользователи будут неактивны (и не смогут войти) до того момента, пока администратор вручную не активирует профиль.</text>
    <text name="Default">По умолчанию</text>
    <text name="DefaultRole_Description">Привязка к новому пользователю по умолчанию.</text>
    <text name="StaticRole_Tooltip">Статичные роли не могут быть удалены.</text>
    <text name="EmailConfirmationRequiredForLogin">Для входа необходимо подтвердить Email адрес.</text>
    <text name="{0}UserCannotBeDeleted">{0} пользователь не может быть удален.</text>
    <text name="TenantNameCanNotBeEmpty">Имя клиента не может быть пустым</text>
    <text name="ExportToExcel">Экспорт в Excel</text>
    <text name="ChangeProfilePicture">Фото для профиля</text>
    <text name="TenantName_Regex_Description">Имя клиента должно иметь 2 знака, начинаться с латинской буквы и продолжаться латинской буквой, цифрой, тире или подчеркиванием.</text>
    <text name="ProfilePicture_Change_Info">Вы можете выбрать файл в формате JPG/JPEG/PNG и размером не более {0}MB.</text>
    <text name="ProfilePicture_Change_Error">Фото для вашего профиля не может быть изменено.</text>
    <text name="ProfilePicture_Warn_SizeLimit">Картинка должна быть менее {0}MB. Пожалуйста, выберите другой файл.</text>
    <text name="ProfilePicture_Warn_FileType">Вы можете выбрать только файл в формате JPG/JPEG. Пожалуйста, выберите другой файл.</text>
    <text name="Dashboard">Аналитика</text>
    <text name="DashboardHeaderInfo">статистика и отчеты</text>
    <text name="GoToApplication">Переход в систему</text>
    <text name="TenantInformations">Информация о клиенте</text>
    <text name="PersonalInformations">Персональная информация</text>
    <text name="AccountSettings">Настройки профиля</text>
    <text name="Captha_Hint">Пожалуйста, введите код безопасности, указанный на картинке ниже:</text>
    <text name="Back">Назад</text>
    <text name="Submit">Далее</text>
    <text name="SignUp">Регистрация</text>
    <text name="CaptchaCanNotBeEmpty">Код безопасности не может быть пустым.</text>
    <text name="IncorrectCaptchaAnswer">Неверный код безопасности.</text>
    <text name="FormIsNotValidMessage">Неверные данные в форме. Пожалуйста, проверьте и исправьте ошибки.</text>
    <text name="CanNotChangeAdminUserName">Нельзя изменить имя пользователя для администратора.</text>
    <text name="Error">Ошибка!</text>
    <text name="OopsYouAreLost">О, нет!</text>
    <text name="WeCanNotFindThePage">К сожалению, мы не можем найти страницу, которую вы запросили.</text>
    <text name="DashboardDemo_Note_Title">Добро пожаловать в начальный проект ASP.NET Zero!</text>
    <text name="DashboardDemo_Note_Info">
      В этом демо вы можете использовать левое меню для теста полнофункциональных страниц.
      Эта страница аналитики предназначена для демонстрационных целей, текст и данные ничего не значат.
      Почти все данные жестко закодированы в клиенте, кроме маленьких графиков MEMBER ACTIVITY.
      Когда вы нажмете на кнопку REFRESH, данные для графика запрашиваются с сервера и сервер генерирует произвольные данные.
    </text>
    <text name="WelcomePage_Title">Добро пожаловать!</text>
    <text name="WelcomePage_Info">Добро пожаловать в систему. Вы можете использовать меню для начала работы в системе. Если вы не видите пунктов меню, возможно у вас нет прав для просмотра страниц. Запросите у системного администратора права.</text>
    <text name="RequestedFileDoesNotExists">Запрошенный файл не существует!</text>
    <text name="MailSent">Письмо отправлено</text>
    <text name="PasswordResetMailSentMessage">Ссылка для сброса пароля отправлена на ваш Email адрес. Пожалуйста, проверьте свою почту.</text>
    <text name="UseCaptchaOnRegistration">Использовать вопрос с картинки безопасности (captcha) при регистрации.</text>
    <text name="FormBasedRegistration">Регистрация на базе формы</text>
    <text name="LdapSettings">Настройки LDAP</text>
    <text name="EnableLdapAuthentication">Включить LDAP авторизацию.</text>
    <text name="OtherSettings">Прочие настройки</text>
    <text name="AuditLogs">Аудит логи</text>
    <text name="Success">Успешно</text>
    <text name="Time">Время</text>
    <text name="Service">Служба</text>
    <text name="Action">Действие</text>
    <text name="Duration">Срок</text>
    <text name="IpAddress">IP адрес</text>
    <text name="Client">Клиент</text>
    <text name="Browser">Браузер</text>
    <text name="Xms">{0} мс</text>
    <text name="Close">Закрыть</text>
    <text name="Refresh">Обновить</text>
    <text name="ErrorState">Статус ошибки</text>
    <text name="All">Все</text>
    <text name="HasError">Имеет ошибку</text>
    <text name="ShowAdvancedFilters">Показать дополнительные фильтры</text>
    <text name="HideAdvancedFilters">Скрыть дополнительные фильтры</text>
    <text name="AuditLogDetail">Детали аудит логов</text>
    <text name="Parameters">Параметры</text>
    <text name="DateRange">Диапазон дат</text>
    <text name="ActionInformations">Информация о действии</text>
    <text name="Today">Сегодня</text>
    <text name="Yesterday">Вчера</text>
    <text name="Last7Days">Последние 7 дней</text>
    <text name="Last30Days">Последние 30 дней</text>
    <text name="ThisMonth">Этот месяц</text>
    <text name="LastMonth">Прошлый месяц</text>
    <text name="TenantSelection">Выбор Клиента</text>
    <text name="TenantSelection_Detail">Пожалуйста, выберите одного из следующих клиентов.</text>
    <text name="LoginWith">Вход с:</text>
    <text name="PasswordChangeDontRememberMessage">Если вы не можете вспомнить ваш пароль, {0}.</text>
    <text name="ClickHere">нажмите здесь</text>
    <text name="Apply">OK</text>
    <text name="CustomRange">Диапазон дат</text>
    <text name="DashboardNoteForMpaVersion">
      <![CDATA[
      Посмотреть демо версии Многостраничного Приложения с <strong>ASP.NET MVC</strong> и <strong>jQuery</strong>.
      ]]>
    </text>
    <text name="DashboardNoteForSpaVersion">
      <![CDATA[
      Примечание: Также существует версия <strong>Одностраничного Приложения</strong> построенная на базе ASP.NET MVC и <strong>AngularJs</strong>.
      ]]>
    </text>
    <text name="SeeDemo">Посмотреть демо</text>
    <text name="Edition">Издание</text>
    <text name="Editions">Издания</text>
    <text name="EditionsHeaderInfo">Управление изданиями и опциями системы</text>
    <text name="CreateNewEdition">Создать новое издание</text>
    <text name="EditEdition">Изменить издание</text>
    <text name="EditionProperties">Свойства издания</text>
    <text name="EditionName">Имя издания</text>
    <text name="Features">Опции</text>
    <text name="EditionDeleteWarningMessage">Вы уверены, что хотите удалить издание {0}?</text>
    <text name="CreatingNewEdition">Создание нового издания</text>
    <text name="EditingEdition">Изменение издания</text>
    <text name="DeletingEdition">Удаление издания</text>
    <text name="ChangingFeatures">Изменение опций</text>
    <text name="ResetSpecialFeatures">Сброс особых опций</text>
    <text name="ResetFeaturesTooltip">Эта кнопка немедленно сбрасывает особые опции настроек клиента и сохраняет изменения. Клиент будет иметь только опции из назначенного издания.</text>
    <text name="NotAssigned">Не назначено</text>
    <text name="InvalidFeaturesWarning">Неверно задано одно или более значение опции!</text>
    <text name="Languages">Языки</text>
    <text name="LanguagesHeaderInfo">Управление языками пользовательского интерфейса.</text>
    <text name="CreateNewLanguage">Создать новый язык</text>
    <text name="Code">Код</text>
    <text name="CreatingNewLanguage">Создание нового языка</text>
    <text name="EditingLanguage">Изменение языка</text>
    <text name="DeletingLanguages">Удаление языка</text>
    <text name="ChangingTexts">Изменение текстов</text>
    <text name="SuccessfullySaved">Успешно сохранено.</text>
    <text name="Language">Язык</text>
    <text name="Flag">Флаг</text>
    <text name="SetAsDefaultLanguage">Назначить языком по умолчанию</text>
    <text name="EditLanguage">Изменить язык</text>
    <text name="ChangeTexts">Изменить тексты</text>
    <text name="TargetValue">Целевое значение</text>
    <text name="BaseValue">Базовое значение</text>
    <text name="Key">Ключ</text>
    <text name="SaveAndClose">Сохранить, Закрыть</text>
    <text name="SaveAndNext">Сохранить, Далее</text>
    <text name="EditText">Изменить текст</text>
    <text name="LanguageTexts">Тексты языка</text>
    <text name="LanguageTextsHeaderInfo">Изменение текстов для языков.</text>
    <text name="EmptyOnes">Пустые</text>
    <text name="Source">Источник</text>
    <text name="BaseLanguage">Базовый язык</text>
    <text name="TargetLanguage">Целевой язык</text>
    <text name="Previous">Ранее</text>
    <text name="LanguageDeleteWarningMessage">Вы уверены в удалении языка {0}?</text>
    <text name="CanNotEditOrDeleteDefaultLanguages">Нельзя изменять или удалять языки по умолчанию. Вы можете изменить или удалить языки, которые добавили. Однако можно изменять тексты всех языков.</text>
    <text name="LookingForMpaVersion">Ищите Многостраничную версию системы?</text>
    <text name="LookingForSpaVersion">Ищите Одностраничную версию системы?</text>
    <text name="ThisLanguageAlreadyExists">Данный язык уже существует!</text>
    <text name="OrganizationUnits">Структура отделов</text>
    <text name="OrganizationUnitsHeaderInfo">Используйте структуру отделов, чтобы упорядочить пользователей и подразделения</text>
    <text name="OrganizationTree">Организационная структура</text>
    <text name="Members">Состав</text>
    <text name="SelectAnOrganizationUnitToSeeMembers">Для просмотра состава выберите структурный отдел.</text>
    <text name="OrganizationUnitDeleteWarningMessage">Вы уверены в удалении структурного отдела {0}?</text>
    <text name="AddSubUnit">Добавить уровень</text>
    <text name="AddMember">Добавить состав</text>
    <text name="SuccessfullyAdded">Успешно добавлено</text>
    <text name="RemoveUserFromOuWarningMessage">Вы уверены в удалении пользователя {0} из структурного отдела {1}?</text>
    <text name="SuccessfullyRemoved">Успешно удалено</text>
    <text name="SelectAnItem">Выбор позицииm</text>
    <text name="NewOrganizationUnit">Новый структурный отдел</text>
    <text name="SelectAUser">Выбрать пользователя</text>
    <text name="Select">Выбор</text>
    <text name="UserIsAlreadyInTheOrganizationUnit">Этот пользователь уже в структурном отделе!</text>
    <text name="AddRootUnit">Добавить отдел</text>
    <text name="AddUser">Добавить пользователя</text>
    <text name="NoOrganizationUnitDefinedYet">Пока нет определенных структурных отделов.</text>
    <text name="OrganizationUnitMoveConfirmMessage">Пожалуйста, подтвердите перенос {0} под {1}.</text>
    <text name="Root">Корень</text>
    <text name="SuccessfullyMoved">Успешно перенесено.</text>
    <text name="AddedTime">Время добавления</text>
    <text name="ManagingMembers">Управление составом</text>
    <text name="ManagingOrganizationTree">Управление организационной структурой</text>
    <text name="LoginForUsers">Вход под пользователями</text>
    <text name="LoginAsThisUser">Вход под этим пользователем</text>
    <text name="BackToMyAccount">Назад в профиль</text>
    <text name="LoginForTenants">Вход под клиентом</text>
    <text name="LoginAsThisTenant">Вход под этим клиентом</text>
    <text name="AuditLogImpersonatedOperationInfo">Примечание: Эта операция выполнена другим пользователем от имени данного пользователя.</text>
    <text name="CustomData">Особые данные</text>
    <text name="None">Нет</text>
    <text name="YouCanBackToYourAccount">Вы можете вернуться назад в свой профиль здесь.</text>
    <text name="CascadeImpersonationErrorMessage">Невозможно сделать каскадную персонализацию. Вход уже персонализирован!</text>
    <text name="FromTenantToHostImpersonationErrorMessage">Невозможно персонализировать системного пользователя из пользователя клиента!</text>
    <text name="DifferentTenantImpersonationErrorMessage">Невозможно персонализировать пользователя другого клиента!</text>
    <text name="ImpersonationTokenErrorMessage">Недействительный или просроченный персонализированный токен!</text>
    <text name="NotImpersonatedLoginErrorMessage">Вход не является персонализированным!</text>
    <text name="YouCannotLinkToSameAccount">Нельзя добавить такой же профиль.</text>
    <text name="ChangePasswordBeforeLinkToAnAccount">Вы должны изменить свой пароль до добавления этого профиля!</text>
    <text name="LinkedAccounts">Связанные профили</text>
    <text name="ManageLinkedAccounts">Управление</text>
    <text name="LinkNewAccount">Добавить новый профиль</text>
    <text name="SuccessfullyUnlinked">Успешно отвязано</text>
    <text name="LinkedUserDeleteWarningMessage">Связь с пользователем {0} будет удалена.</text>
    <text name="SwitchToLinkedAccountTokenErrorMessage">Недействительный или просроченный персонализированный токен!</text>
    <text name="WelcomeToTheApplicationNotificationMessage">Добро пожаловать в omni! Система сообщений используется для оповещения об определенных событиях. Вы можете выбрать какие типы оповещений вы хотите получать от системы сообщений.</text>
    <text name="NewUserRegisteredNotificationDefinition">При регистрации нового пользователя в системе.</text>
    <text name="NewUserRegisteredNotificationMessage">Новый пользователь зарегистрирован в системе. Имя пользователя: {userName}, Email адрес: {emailAddress}.</text>
    <text name="SeeAllNotifications">Просмотр всех сообщений</text>
    <text name="SetAsRead">Отметить как прочитанное</text>
    <text name="SetAllAsRead">Отметить все как прочитанные</text>
    <text name="NotificationSettings">Настройки сообщений</text>
    <text name="ReceiveNotifications">Получать сообщения</text>
    <text name="ReceiveNotifications_Definition">Эта опция может полностью включить/выключить получение сообщений.</text>
    <text name="NotificationTypes">Типы сообщений</text>
    <text name="ReceiveNotifications_DisableInfo">Вы полностью выключили получение сообщений. Вы можете включить получение сообщений и выбрать типы, которые хотите получать.</text>
    <text name="Notifications">Сообщения</text>
    <text name="ThereAreNoNotifications">Нет сообщений.</text>
    <text name="Unread">Непрочтенные</text>
    <text name="YouCanNotDeleteOwnAccount">Вы не можете удалить собственный профиль!</text>
    <text name="On">Вкл.</text>
    <text name="Off">Откл.</text>
    <text name="Maintenance">Обслуживание</text>
    <text name="Caches">Кэши</text>
    <text name="CacheSuccessfullyCleared">Кэш успешно очищен.</text>
    <text name="AllCachesSuccessfullyCleared">Весь кэш успешно очищен.</text>
    <text name="ClearAll">Очистить все</text>
    <text name="Clear">Очистить</text>
    <text name="CachesHeaderInfo">Вы можете очистить все кэши системы на этой странице.</text>
    <text name="TestEmailSettingsHeader">Настройки тестового Email</text>
    <text name="SendTestEmail">Отправить тестовый Email</text>
    <text name="TestEmail_Subject">Тестовый Email</text>
    <text name="TestEmail_Body">Это тестовое Email сообщение.</text>
    <text name="TestEmailSentSuccessfully">Тестовое Email сообщение успешно отправлено.</text>
    <text name="WebSiteLogs">Логи Web сайта</text>
    <text name="WebSiteLogsHeaderInfo">На этой странице вы можете посмотреть последние логи или выгрузить их в одном ZIP файле.</text>
    <text name="DownloadAll">Выгрузить все</text>
    <text name="TenantManagement">Управление клиентами</text>
    <text name="AllowTenantsToRegisterThemselves">Позволить клиентам регистрироваться в системе.</text>
    <text name="AllowTenantsToRegisterThemselves_Hint">Отключив эту опцию, клиенты будут добавляться только администратором через страницу управления клиентами.</text>
    <text name="NewRegisteredTenantsIsActiveByDefault">Новые клиенты активны по умолчанию.</text>
    <text name="NewRegisteredTenantsIsActiveByDefault_Hint">Если отключить эту опцию, новые клиенты будут неактивны (и не смогут входить) до того, как администратор вручную не активирует профиль.</text>
    <text name="TenantSignUp">Регистрация клиента</text>
    <text name="NewTenantRegisteredNotificationDefinition">При регистрации нового клиента в системе.</text>
    <text name="NewTenantRegisteredNotificationMessage">Новый клиент зарегистрирован в системе. Имя клиента: {tenancyName}.</text>
    <text name="LoginAttempts">Попытки входа</text>
    <text name="Failed">Неуспешно</text>
    <text name="NewTenant">Новый клиент</text>
    <text name="ResizedProfilePicture_Warn_SizeLimit">Сжатая картинка должна быть менее {0}KB. Пожалуйста, уменьшите и попытайтесь снова.</text>
    <text name="RegisterFormUserNameInvalidMessage">Пожалуйста, не вводите Email адрес для имени пользователя.</text>
    <text name="UseHostDatabase">Использовать общую базу данных</text>
    <text name="DatabaseConnectionString">Строка подключения к базе данных</text>
    <text name="TenantDatabaseConnectionStringChangeWarningMessage">Внимание: Перед изменением для клиента строки подключения к базе данных, необходимо перенести базу данных в новое место. Изменение строки подключения не переносит базу данных клиента.</text>
    <text name="Timezone">Часовой пояс</text>
    <text name="HasOwnDatabase">Имеет свою базу данных</text>
    <text name="TimeZoneSettingChangedRefreshPageNotification">Настройки часового пояса изменены. Нажмите кнопку OK для обновления страницы и вступления в силу изменений.</text>
    <text name="Ok">OK</text>
    <text name="Permission">Право</text>
    <text name="Role">Роль</text>
    <text name="Security">Безопасность</text>
    <text name="PasswordComplexity">Сложность пароля</text>
    <text name="UseDefaultSettings">Использовать настройки по умолчанию</text>
    <text name="PasswordComplexityNotSatisfied">Неудовлетворительная сложность пароля.</text>
    <text name="YouAlreadySentAFriendshipRequestToThisUser">Вы же добавили данного пользователя</text>
    <text name="YouCannotBeFriendWithYourself">Вы не можете добавить себя в друзья.</text>
    <text name="NewChatMessageEmail_Title">Детали сообщения</text>
    <text name="NewChatMessageEmail_SubTitle">...</text>
    <text name="NewChatMessageEmail_Subject">У вас новое сообщение в чате</text>
    <text name="Sender">Отправитель</text>
    <text name="Message">Сообщение</text>
    <text name="UserBlocked">Пользователь успешно заблокирован.</text>
    <text name="UserUnblocked">Блокировка с пользователя успешно снята.</text>
    <text name="BlockUser">Блокировать пользователя</text>
    <text name="UnblockUser">Разблокировать</text>
    <text name="FriendshipRequestAccepted">Запрос на дружбу принят</text>
    <text name="UserSendYouAFriendshipRequest">{0} добавил вас в друзья</text>
    <text name="Friends">Друзья</text>
    <text name="Others">Прочие</text>
    <text name="BlockedUsers">Блокированные пользователи</text>
    <text name="YouDontHaveAnyFriend">У вас нет друзей. Введите имя пользователя в поле сверху и нажмите кнопку "Добавить друга".</text>
    <text name="YouDontHaveAnyBlockedFriend">У вас нет заброкированных пользователей. Для блокировки пользователя, выберите друга и нажмите на "Блокировать пользователя" в действиях.</text>
    <text name="ChatUserSearch_Hint">
      <![CDATA[
      Введите только имя пользоватеня для пользователей внтри клиента,<br>
      <strong>[tenancyName]\[userName]</strong> для пользователей другого клиента <br><br>
      Например: <br>
      .\admin -> для администратора Хоста <br>
      Test\admin -> для администратора клиента Тест <br>
      admin -> для вашего администратора
      ]]>
    </text>
    <text name="AddFriend">Добавить друга</text>
    <text name="TypeAMessageHere">Введите ваше сообщение здесь...</text>
    <text name="Chat">Чат</text>
    <text name="ChatFeature">Чат</text>
    <text name="ChatFeatureIsNotEnabledForSender">Чат не активирован для вас.</text>
    <text name="ChatFeatureIsNotEnabledForReceiver">Чат не активирован для клиента.</text>
    <text name="UserIsBlocked">Пользователь заблокирован.</text>
    <text name="ChatIsNotConnectedWarning">Чат не подключен.</text>
    <text name="Demo_SampleChatMessage">Привет! Это тестовое сообщение. Пожалуйста, войдите в мою учетную запись из другого бразера, чтобы проверить как работает чат.</text>
    <text name="TenantToTenantChatFeatureIsNotEnabledForSender">Чат с другими клиентами не активирован для вас.</text>
    <text name="TenantToTenantChatFeatureIsNotEnabledForReceiver">Чат с другими клиентами не активирован для клиента.</text>
    <text name="TenantToHostChatFeatureIsNotEnabledForSender">Чат с хостом не активирован для вас.</text>
    <text name="TenantToHostChatFeatureIsNotEnabledForReceiver">Чат с хостом не активирован для клиента.</text>
    <text name="TenantToTenantChatFeature">Чат с другими клиентами</text>
    <text name="TenantToHostChatFeature">Чат с хостом</text>
    <text name="FilterByPermission">Фильтр на права</text>
    <text name="FilterByRole">Фильтр на роль</text>
    <text name="FilterOrAddUser">Фильтр/Добавить пользователя</text>
    <text name="HangfireDashboard">Hangfire dashboard</text>
    <text name="TargetUserNotFoundProbablyDeleted">Заданный пользователь не найден. Возможно, он удален.</text>
    <text name="UserLockedOutMessage">Учетная запись пользователя была заблокирована. Пожалуйста, повторите попытку позже.</text>
    <text name="SendSecurityCode">Отправить код безопасности.</text>
    <text name="SendSecurityCode_Information">Для входа вам необходимо подтверждение. Выберите тип подтверждения. Вам будет отправлен код подтверждения на базе выбранного типа.</text>
    <text name="SendSecurityCodeErrorMessage">Код безопасности не может быть отправлен!</text>
    <text name="VerifySecurityCodeNotLoggedInErrorMessage">Для своего подтверждения необхоимо выполнить вход! Возможно, на попытку вашего входа наступил тайм-аутю. Вернитесь на страницу входа и попробуйте заново.</text>
    <text name="VerificationCode">Код подтверждения</text>
    <text name="VerifySecurityCode">Подтвердить код безопасности</text>
    <text name="VerifySecurityCode_Information">Введите код подтверждения, который был отправлен вам.</text>
    <text name="RememberThisBrowser">Запомнить этот бразер</text>
    <text name="InvalidSecurityCode">Неверный код безопасности!</text>
    <text name="PhoneNumber">Номер телефона</text>
    <text name="Unlock">Разблокировать</text>
    <text name="UnlockedTheUser">Разблокировал пользователя {0}</text>
    <text name="UserLockOut">Блокировка пользователя</text>
    <text name="EnableUserAccountLockingOnFailedLoginAttempts">Включить блокировку учетной записи пользователя при неверных попытках входа</text>
    <text name="MaxFailedAccessAttemptsBeforeLockout">Максимальное число неверных попыток входа до блокировки учетной записи</text>
    <text name="DefaultAccountLockoutDurationAsSeconds">Срок блокировки учетной записи (секунд)</text>
    <text name="TwoFactorLogin">Двухфакторная аутентификация</text>
    <text name="EnableTwoFactorLogin">Включить двухфакторную аутентификация.</text>
    <text name="IsEmailVerificationEnabled">Включить проверку по Email.</text>
    <text name="IsSmsVerificationEnabled">Включить проверку по SMS.</text>
    <text name="AllowToRememberBrowserForTwoFactorLogin">Разрешить запоминание браузера. Если выбрать эту опцию, пользователи смогут запомнить браузер, чтобы пропустить повторную двухфакторную аутентификацию для того же браузера.</text>

    <text name="CurrentTenant">Текущий клиент</text>
    <text name="NotSelected">Не выбрано</text>
    <text name="Change">Изменить</text>
    <text name="ChangeTenant">Изменить клиента</text>
    <text name="UnknownTenantId{0}">Неизвестный клиент id: {0}</text>
    <text name="TenantIdIsNotActive{0}">Неактивный клиент id: {0}</text>
    <text name="TimeoutPleaseTryAgain">Время истекло! Попробуйте снова.</text>
    <text name="RemainingTime">Оставшееся время</text>
    <text name="SecondShort{0}">{0} сек</text>
    <text name="ResetCode">Сбросить код</text>
    <text name="IsTwoFactorEnabled">Двухфакторная аутентификация доступна?</text>
    <text name="IsLockoutEnabled">Блокировка доступна</text>
    <text name="IsLockoutEnabled_Hint">Пользователь блокируется после определенного количества неудачных попыток входа.</text>
    <text name="TenantRegistrationLoginInfo">Кликните здесь для входа</text>
    <text name="CouldNotValidateExternalUser">Не могу подтвердить логин внешнего пользователя</text>
    <text name="LeaveEmptyToSwitchToHost">Оставьте пустым для подключения в качестве системного пользователя</text>
    <text name="PleaseWaitToConfirmYourEmailMessage">Пожалуйста, подождите подтверждения вашего email...</text>
    <text name="ChatIsConnecting">Подключение чата...</text>
    <text name="Note_RefreshPageForPermissionChanges">Если Вы измените Ваши разрешения, возможно, потребуется обновить страницу (F5), чтобы изменения вступили в силу!</text>
    <text name="ApplicationLogo">Лого приложения</text>
    <text name="CustomCSS">Собственный CSS</text>
    <text name="Upload">Upload</text>
    <text name="Download">Download</text>
    <text name="UploadLogo_Info">Выберите JPG/JPEG/PNG/GIF файл с максимальным размером 100KB и с разрешением 512x128 пикселей.</text>
    <text name="UploadCSS_Info">Выберите .css файл с максимальным размером 1MB.</text>
    <text name="Browse">Обзор</text>
    <text name="Appearance">Внешний вид</text>
    <text name="ClearedSuccessfully">Успешно очищено</text>
    <text name="File_Empty_Error">Пожалуйста, выберите файл!</text>
    <text name="File_SizeLimit_Error">Размер файла превышает допустимый!</text>
    <text name="File_Invalid_Type_Error">Неправильный тип файла!</text>
    <text name="MaximumUserCount">Максимальное количество пользователь (0 = не ограничено)</text>
    <text name="MaximumUserCount_Error_Message">Достигнуто макимально допустимое количество пользователей!</text>
    <text name="MaximumUserCount_Error_Detail">У этого клиента допускается макимально {0} пользователей.</text>
    <text name="UnlockedTenandAdmin">Разблокировать администратора клиента {0}</text>
    <text name="Email">Email</text>
    <text name="EmailSecurityCodeSubject">Код безопасности</text>
    <text name="EmailSecurityCodeBody">Ваш код безопасности: {0}</text>
    <text name="Sms">SMS</text>
    <text name="SmsSecurityCodeMessage">Ваш код безопасности: {0}</text>

    <text name="PasswordComplexity_RequireDigit_Hint">Пароль должен содержать хотя бы одну цифру ('0'-'9').</text>
    <text name="PasswordComplexity_RequireLowercase_Hint">Пароль должен содержать хотя бы одну букву в нижнем регистре ('a'-'z').</text>
    <text name="PasswordComplexity_RequireNonAlphanumeric_Hint">Пароль должен содержать хотя бы один символ, отличный от букв и цифр.</text>
    <text name="PasswordComplexity_RequireUppercase_Hint">Пароль должен содержать хотя бы одну заглавную букву ('A'-'Z').</text>
    <text name="PasswordComplexity_RequiredLength_Hint">Пароль должен быть длинной не менее {0} символов.</text>

    <text name="PasswordComplexity_RequireDigit">Требуется цифра</text>
    <text name="PasswordComplexity_RequireLowercase">Требуется буква в нижнем регистре</text>
    <text name="PasswordComplexity_RequireNonAlphanumeric">Требуется символ, отличный от букв и цифр</text>
    <text name="PasswordComplexity_RequireUppercase">Требуется заглавная буква</text>
    <text name="PasswordComplexity_RequiredLength">Недостаточная длина</text>
  </texts>
</localizationDictionary>
