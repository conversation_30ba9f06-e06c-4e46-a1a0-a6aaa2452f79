namespace RealtoCrm.Comments;

using System.Collections.Generic;
using Abp.Domain.Entities.Auditing;
using Authorization.Users;
using Clients;
using Deals;
using Deposits;
using Employees;
using Offers;
using Projects;
using Searches;
using Viewings;

public class Comment : FullAuditedEntity<int>
{
    public string Text { get; set; } = default!;

    public bool IsPrivate { get; set; }

    public int? EmployeeId { get; set; }

    public Employee? Employee { get; set; }

    public User CreatorUser { get; set; } = default!;

    public ICollection<OfferComment> OffersComments { get; } = new List<OfferComment>();

    public ICollection<ClientComment> ClientsComments { get; } = new List<ClientComment>();

    public ICollection<SearchComment> SearchesComments { get; } = new List<SearchComment>();

    public ICollection<ProjectComment> ProjectsComments { get; } = new List<ProjectComment>();

    public ICollection<ViewingComment> ViewingsComments { get; } = new List<ViewingComment>();

    public ICollection<DepositComment> DepositsComments { get; } = new List<DepositComment>();

    public ICollection<DealParticipationComment> DealParticipationsComments { get; } =
        new List<DealParticipationComment>();
}