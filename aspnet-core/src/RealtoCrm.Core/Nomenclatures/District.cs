namespace RealtoCrm.Nomenclatures;

using Searches;
using System.Collections.Generic;
using Addresses;
using Translations;

public class District : Nomenclature<int>
{
    public string? Coordinates { get; set; }

    public int PopulatedPlaceId { get; set; }

    public PopulatedPlace PopulatedPlace { get; set; } = default!;

    public ICollection<Street> Streets { get; } = new List<Street>();

    public ICollection<Address> Addresses { get; } = new List<Address>();
    
    public ICollection<SearchDistrict> SearchDistricts { get; } = new List<SearchDistrict>();
    
    public ICollection<DistrictTranslation> Translations { get; } = new List<DistrictTranslation>();
}