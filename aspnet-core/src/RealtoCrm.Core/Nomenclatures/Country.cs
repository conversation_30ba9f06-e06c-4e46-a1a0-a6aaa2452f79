namespace RealtoCrm.Nomenclatures;

using System.Collections.Generic;
using Addresses;
using Searches;
using Translations;

public class Country : Nomenclature<int>
{
    public string CodeAlpha2 { get; set; } = default!;

    public string CodeAlpha3 { get; set; } = default!;

    public ICollection<Province> Provinces { get; } = new List<Province>();

    public ICollection<Address> Addresses { get; } = new List<Address>();

    public ICollection<SearchCountry> SearchesCountries { get; } = new List<SearchCountry>();
    
    public ICollection<CountryTranslation> Translations { get; } = new List<CountryTranslation>();
}