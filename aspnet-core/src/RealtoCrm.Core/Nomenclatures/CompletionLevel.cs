using RealtoCrm.EstateObjects;

namespace RealtoCrm.Nomenclatures;

using System.Collections.Generic;
using Buildings;
using Estates;
using Projects;
using Searches;

public class CompletionLevel : Nomenclature<int>
{
    public EstateGroup? EstateGroup { get; set; }
    
    public ICollection<Estate> Estates { get; } = new List<Estate>();

    public ICollection<Project> Projects { get; } = new List<Project>();

    public ICollection<Building> Buildings { get; } = new List<Building>();

    public ICollection<SearchCompletionLevel> SearchesCompletionLevels { get; } = new List<SearchCompletionLevel>();
}