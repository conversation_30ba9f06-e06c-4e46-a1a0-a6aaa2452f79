using Abp.Domain.Entities.Auditing;
using RealtoCrm.Nomenclatures;

namespace RealtoCrm.Searches;

public class SearchDetail : FullAuditedEntity<int>
{
    public Search Search { get; set; } = default!;
    
    public double? CeilingHeight { get; set; }
    
    public bool? HasAirConditionSystem { get; set; }
    
    public bool? HasAttic { get; set; }
    
    public bool? HasBasement { get; set; }
    
    public bool? HasBuildingVisa { get; set; }
    
    public bool? HasCableTv { get; set; }
    
    public bool? HasCanal { get; set; }
    
    public bool? HasDetailedSpatialPlan { get; set; }
    
    public bool? HasElectricity { get; set; }
    
    public bool? HasElevator { get; set; }
    
    public bool? HasFitness { get; set; }
    
    public bool? HasFreightPlatform { get; set; }
    
    public bool? HasGarden { get; set; }
    
    public bool? HasGas { get; set; }
    
    public bool? HasInternet { get; set; }
    
    public bool? HasLevel { get; set; }
    
    public bool? HasMortgage { get; set; }
    
    public bool? HasParking { get; set; }
    
    public bool? HasPhone { get; set; }
    
    public bool? HasPool { get; set; }
    
    public bool? HasPublicTransportNearby { get; set; }
    
    public bool? HasRailwayAccess { get; set; }
    
    public bool? HasRestaurant { get; set; }
    
    public bool? HasSanitationRoom { get; set; }
    
    public bool? HasSewage { get; set; }
    
    public bool? HasSolarPanels { get; set; }
    
    public bool? HasSpecialPrice { get; set; }
    
    public bool? HasStorage { get; set; }
    
    public bool? HasTirAccess { get; set; }
    
    public bool? HasView { get; set; }
    
    public bool? HasWater { get; set; }
    
    public bool? IsEnergyEfficient { get; set; }
    
    public bool? IsEquipped { get; set; }
    
    public bool? IsGatedComplex { get; set; }
    
    public bool? IsGuarded { get; set; }
    
    public bool? IsMansard { get; set; }
    
    public bool? IsPetFriendly { get; set; }
    
    public bool? IsRegulated { get; set; }
    
    public bool? IsSuitableForInvestment { get; set; }
    
    public bool? IsUnderground { get; set; }
    
    public int? LeaseTermId { get; set; }
    
    public LeaseTerm? LeaseTerm { get; set; }
    
    public double? WindowHeight { get; set; }
    
    public bool? ZeroCommission { get; set; }
}