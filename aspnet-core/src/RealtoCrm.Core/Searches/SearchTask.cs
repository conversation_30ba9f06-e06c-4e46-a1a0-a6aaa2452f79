namespace RealtoCrm.Searches;

using System;
using Abp.Domain.Entities.Auditing;
using Tasks;

public class SearchTask : IFullAudited, IHaveTask
{
    public int SearchId { get; set; }

    public Search Search { get; set; } = default!;

    public int TaskId { get; set; }

    public Task Task { get; set; } = default!;

    public DateTime CreationTime { get; set; }

    public long? CreatorUserId { get; set; }

    public DateTime? LastModificationTime { get; set; }

    public long? LastModifierUserId { get; set; }

    public bool IsDeleted { get; set; }

    public DateTime? DeletionTime { get; set; }

    public long? DeleterUserId { get; set; }
}