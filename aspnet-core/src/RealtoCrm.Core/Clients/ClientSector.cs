namespace RealtoCrm.Clients;

using System;
using Abp.Domain.Entities.Auditing;
using Nomenclatures;

public class ClientSector : IFullAudited
{
    public int ClientId { get; set; }

    public Client Client { get; set; } = default!;

    public int SectorId { get; set; }

    public Sector Sector { get; set; } = default!;

    public DateTime CreationTime { get; set; }

    public long? CreatorUserId { get; set; }

    public DateTime? LastModificationTime { get; set; }

    public long? LastModifierUserId { get; set; }

    public bool IsDeleted { get; set; }

    public DateTime? DeletionTime { get; set; }

    public long? DeleterUserId { get; set; }
}