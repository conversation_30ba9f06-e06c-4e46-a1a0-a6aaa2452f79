namespace RealtoCrm.Clients;

public static class ModelConstants
{
    public static class Client
    {
        public const int MinNameLength = 2;
        public const int MaxNameLength = 100;
        public const int MinDocumentNumberLength = 2;
        public const int MaxDocumentNumberLength = 50;
        public const int MinDocumentAuthorityLength = 2;
        public const int MaxDocumentAuthorityLength = 50;
        public const int MinVatRegistrationLength = 2;
        public const int MaxVatRegistrationLength = 50;
    }
}