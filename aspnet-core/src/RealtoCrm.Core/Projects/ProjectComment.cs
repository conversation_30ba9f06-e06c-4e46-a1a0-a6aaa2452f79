namespace RealtoCrm.Projects;

using System;
using Abp.Domain.Entities.Auditing;
using Comments;

public class ProjectComment : IFullAudited, IHaveComment
{
    public int ProjectId { get; set; }

    public Project Project { get; set; } = default!;

    public int CommentId { get; set; }

    public Comment Comment { get; set; } = default!;

    public DateTime CreationTime { get; set; }

    public long? CreatorUserId { get; set; }

    public DateTime? LastModificationTime { get; set; }

    public long? LastModifierUserId { get; set; }

    public bool IsDeleted { get; set; }

    public DateTime? DeletionTime { get; set; }

    public long? DeleterUserId { get; set; }
}