namespace RealtoCrm.OffersEmployeesChangeHistory;

using Abp.Domain.Entities.Auditing;
using MultiTenancy;
using MultiTenancy.Interfaces;
using Offers;

public class OfferEmployeesChangeHistory : FullAuditedEntity<int>, IHaveTenant
{
    public int NewEmployeeId { get; set; }
    
    public int OldEmployeeId { get; set; }
    
    public int OfferId { get; set; }
    
    public Offer Offer { get; set; }
    
    public int TenantId { get; set; }
    
    public Tenant Tenant { get; set; }
}