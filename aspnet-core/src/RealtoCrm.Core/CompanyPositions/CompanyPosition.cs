namespace RealtoCrm.CompanyPositions;

using System.Collections.Generic;
using Abp.Domain.Entities.Auditing;
using Employees;
using Estates;
using MultiTenancy;
using MultiTenancy.Interfaces;

public class CompanyPosition : FullAuditedEntity<int>, IHaveTenant
{
    private string? Name { get; set; }
    
    public int TenantId { get; set; }

    public Tenant Tenant { get; set; } = default!;
    
    public ICollection<Employee> Employees { get; } = new List<Employee>();
}