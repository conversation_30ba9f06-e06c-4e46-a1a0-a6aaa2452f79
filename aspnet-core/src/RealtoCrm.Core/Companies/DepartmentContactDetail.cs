namespace RealtoCrm.Companies;

using Abp.Domain.Entities.Auditing;
using ContactDetails;

public class DepartmentContactDetail : FullAuditedEntity<int>
{
    public int DepartmentId { get; set; }

    public Department Department { get; set; } = default!;

    public int ContactDetailId { get; set; }

    public ContactDetail ContactDetail { get; set; } = default!;

    public string Value { get; set; } = default!;

    public bool IsDefault { get; set; }
}