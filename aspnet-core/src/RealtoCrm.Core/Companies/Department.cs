namespace RealtoCrm.Companies;

using System.Collections.Generic;
using Abp.Domain.Entities.Auditing;
using Employees;
using MultiTenancy;
using MultiTenancy.Interfaces;

public class Department : FullAuditedEntity<int>, IHaveOptionalTenant
{
    public string Name { get; set; } = default!;

    public bool IsActive { get; set; }

    public int OfficeId { get; set; }

    public Office Office { get; set; } = default!;

    public int DivisionId { get; set; }

    public Division Division { get; set; } = default!;

    public int? TenantId { get; set; }

    public Tenant? Tenant { get; set; } = default!;

    public int CompanyId { get; set; }

    public Company Company { get; set; } = default!;
    
    public ICollection<Team> Teams { get; } = new List<Team>();

    public ICollection<DepartmentContactDetail> ContactDetails { get; } = new List<DepartmentContactDetail>();

    public ICollection<Employee> Employees { get; } = new List<Employee>();
}