namespace RealtoCrm.Companies;

using System.Collections.Generic;
using Abp.Domain.Entities.Auditing;
using Addresses;
using Employees;
using MultiTenancy;
using MultiTenancy.Interfaces;

public class Company : FullAuditedEntity<int>, IHaveTenant
{
    public string Name { get; set; } = default!;

    public string? Bulstat { get; set; }

    public string? MaterialResponsiblePerson { get; set; }

    public bool IsActive { get; set; }

    public int TenantId { get; set; }

    public Tenant Tenant { get; set; } = default!;

    public int? RegistrationAddressId { get; set; }

    public Address? RegistrationAddress { get; set; }

    public ICollection<Team> Teams { get; } = new List<Team>();

    public ICollection<Division> Divisions { get; } = new List<Division>();

    public ICollection<Office> Offices { get; } = new List<Office>();

    public ICollection<Department> Departments { get; } = new List<Department>();
    
    public ICollection<Employee> Employees { get; } = new List<Employee>();
    
}