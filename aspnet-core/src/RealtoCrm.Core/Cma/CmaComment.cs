namespace RealtoCrm.Cma;

using Abp.Domain.Entities.Auditing;

public enum CmaCommentType
{
    Advantage = 1,
    Disadvantage = 2,
}

public class CmaComment : FullAuditedEntity<int>
{
    public string Content { get; set; } = default!;
    public CmaCommentType Type { get; set; } = CmaCommentType.Advantage;
    
    public int CmaAnalysisId { get; set; }
    public CmaAnalysis CmaAnalysis { get; set; } = default!;
}