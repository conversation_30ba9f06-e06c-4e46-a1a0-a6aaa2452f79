namespace RealtoCrm.MultiTenancy;

using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Abp.MultiTenancy;
using Companies;
using RealtoCrm.Authorization.Users;
using RealtoCrm.Files;
using RealtoCrm.Addresses;

/// <summary>
/// Represents a Tenant in the system.
/// A tenant is a isolated customer for the application
/// which has it's own users, roles and other application entities.
/// </summary>
public class Tenant(string tenancyName, string name)
    : AbpTenant<User>(tenancyName, name)
{
    public const int MaxLogoMimeTypeLength = 64;

    //Can add application specific tenant properties here

    public virtual Guid? CustomCssId { get; set; }

    public virtual Guid? DarkLogoId { get; set; }

    [MaxLength(MaxLogoMimeTypeLength)]
    public virtual string? DarkLogoFileType { get; set; }
        
    public virtual Guid? DarkLogoMinimalId { get; set; }

    [MaxLength(MaxLogoMimeTypeLength)]
    public virtual string? DarkLogoMinimalFileType { get; set; }
        
    public virtual Guid? LightLogoId { get; set; }

    [MaxLength(MaxLogoMimeTypeLength)]
    public virtual string? LightLogoFileType { get; set; }
        
    public virtual Guid? LightLogoMinimalId { get; set; }

    [MaxLength(MaxLogoMimeTypeLength)]
    public virtual string? LightLogoMinimalFileType { get; set; }

    public string? LogoUrl { get; set; }

    public string? SmallLogoUrl { get; set; }

    public ICollection<File> Files { get; } = new List<File>();

    public ICollection<Address> Addresses { get; } = new List<Address>();

    public ICollection<Team> Teams { get; } = new List<Team>();

    public ICollection<Division> Divisions { get; } = new List<Division>();

    public ICollection<Department> Departments { get; } = new List<Department>();

    public ICollection<Office> Offices { get; } = new List<Office>();

    // Leave this here. Needed for data seeding to work with ABP's Tenant. 
    public Tenant()
    : this(null, null)
    {

    }

    public virtual bool HasLogo()
    {
        return (this.DarkLogoId != null && this.DarkLogoFileType != null) ||
               (this.LightLogoId != null && this.LightLogoFileType != null) ||
               (this.DarkLogoMinimalId != null && this.DarkLogoMinimalFileType != null) ||
               (this.LightLogoMinimalId != null && this.LightLogoMinimalFileType != null);
    }
        
    public virtual bool HasDarkLogo()
    {
        return this.DarkLogoId != null && this.DarkLogoFileType != null;
    }     
        
    public virtual bool HasLightLogo()
    {
        return this.LightLogoId != null && this.LightLogoFileType != null;
    }
        
    public bool HasLightLogoMinimal()
    {
        return this.LightLogoMinimalId != null && this.LightLogoMinimalFileType != null;
    }

    public bool HasDarkLogoMinimal()
    {
        return this.DarkLogoMinimalId != null && this.DarkLogoMinimalFileType != null;
    }

    public void ClearDarkLogo()
    {
        this.DarkLogoId = null;
        this.DarkLogoFileType = null;
    }
        
    public void ClearLightLogo()
    {
        this.LightLogoId = null;
        this.LightLogoFileType = null;
    }
        
    public void ClearDarkLogoMinimal()
    {
        this.DarkLogoMinimalId = null;
        this.DarkLogoMinimalFileType = null;
    }
        
    public void ClearLightLogoMinimal()
    {
        this.LightLogoMinimalId = null;
        this.LightLogoMinimalFileType = null;
    }
}