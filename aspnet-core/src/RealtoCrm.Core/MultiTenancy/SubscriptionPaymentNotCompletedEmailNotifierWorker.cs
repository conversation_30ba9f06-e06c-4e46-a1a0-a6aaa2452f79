using System;
using System.Linq;
using Abp.Dependency;
using Abp.Domain.Uow;
using Abp.Threading.BackgroundWorkers;
using Abp.Threading.Timers;
using RealtoCrm.Authorization.Users;
using RealtoCrm.MultiTenancy.Payments;

namespace RealtoCrm.MultiTenancy;

public class SubscriptionPaymentNotCompletedEmailNotifierWorker : PeriodicBackgroundWorkerBase, ISingletonDependency
{
    private const int CheckPeriodAsMilliseconds = 1 * 60 * 60 * 1000 * 24; //1 day

    private readonly UserEmailer userEmailer;
    private readonly IUnitOfWorkManager unitOfWorkManager;
    private readonly ISubscriptionPaymentRepository subscriptionPaymentRepository;
    private readonly IPaymentUrlGenerator paymentUrlGenerator;

    public SubscriptionPaymentNotCompletedEmailNotifierWorker(
        AbpTimer timer,
        UserEmailer userEmailer,
        IUnitOfWorkManager unitOfWorkManager,
        ISubscriptionPaymentRepository subscriptionPaymentRepository,
        IPaymentUrlGenerator paymentUrlGenerator) : base(timer)
    {
        this.userEmailer = userEmailer;
        this.unitOfWorkManager = unitOfWorkManager;
        this.subscriptionPaymentRepository = subscriptionPaymentRepository;
        this.paymentUrlGenerator = paymentUrlGenerator;

        this.Timer.Period = CheckPeriodAsMilliseconds;
        this.Timer.RunOnStart = true;

        this.LocalizationSourceName = RealtoCrmConsts.LocalizationSourceName;
    }

    protected override void DoWork()
    {
        this.unitOfWorkManager.WithUnitOfWork(() =>
        {
            var notCompletedPayments = this.subscriptionPaymentRepository.GetAll()
                .Where(new NotCompletedYesterdayPaymentSpecification().ToExpression())
                .ToList();

            foreach (var notCompletedPayment in notCompletedPayments)
            {
                try
                {
                    var paymentUrl = this.paymentUrlGenerator.CreatePaymentRequestUrl(notCompletedPayment);
                    this.userEmailer.TryToSendPaymentNotCompletedEmail(notCompletedPayment.TenantId, paymentUrl);
                }
                catch (Exception exception)
                {
                    this.Logger.Error(exception.Message, exception);
                }
            }
        });
    }
}