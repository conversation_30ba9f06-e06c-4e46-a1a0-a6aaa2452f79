namespace RealtoCrm.Configuration;

/// <summary>
/// Defines string constants for setting names in the application.
/// See <see cref="AppSettingProvider"/> for setting definitions.
/// </summary>
public static class AppSettings
{
    public static class HostManagement
    {
        public const string BillingLegalName = "App.HostManagement.BillingLegalName";
        public const string BillingAddress = "App.HostManagement.BillingAddress";
    }

    public static class DashboardCustomization {
        public const string Configuration = "App.DashboardCustomization.Configuration";
    }

    public static class UiManagement
    {
        public const string LayoutType = "App.UiManagement.LayoutType";
        public const string DarkMode = "App.UiManagement.DarkMode";
        public const string FixedBody = "App.UiManagement.Layout.FixedBody";
        public const string MobileFixedBody = "App.UiManagement.Layout.MobileFixedBody";

        public const string Theme = "App.UiManagement.Theme";

        public const string SearchActive = "App.UiManagement.MenuSearch";

        public static class Header
        {
            public const string DesktopFixedHeader = "App.UiManagement.Header.DesktopFixedHeader";
            public const string MobileFixedHeader = "App.UiManagement.Header.MobileFixedHeader";
            public const string Skin = "App.UiManagement.Header.Skin";
            public const string MinimizeType = "App.UiManagement.Header.MinimizeType";
            public const string MenuArrows = "App.UiManagement.Header.MenuArrows";
        }

        public static class SubHeader
        {
            public const string Fixed = "App.UiManagement.SubHeader.Fixed";
            public const string Style = "App.UiManagement.SubHeader.Style";
        }

        public static class LeftAside
        {
            public const string Position = "App.UiManagement.Left.Position";
            public const string AsideSkin = "App.UiManagement.Left.AsideSkin";
            public const string FixedAside = "App.UiManagement.Left.FixedAside";
            public const string AllowAsideMinimizing = "App.UiManagement.Left.AllowAsideMinimizing";
            public const string DefaultMinimizedAside = "App.UiManagement.Left.DefaultMinimizedAside";
            public const string HoverableAside = "App.UiManagement.Left.HoverableAside";
            public const string SubmenuToggle = "App.UiManagement.Left.SubmenuToggle";
        }

        public static class Footer
        {
            public const string FixedFooter = "App.UiManagement.Footer.FixedFooter";
        }
            
        public static class Toolbar
        {
            public const string DesktopFixedToolbar = "App.UiManagement.Toolbar.DesktopFixedToolbar";
            public const string MobileFixedToolbar = "App.UiManagement.Toolbar.MobileFixedToolbar";
        }

        public static class PageSize
        {
            public const string PageSizeOptions = "App.UiManagement.PageSizeOptions";
        }

        public static class Searches
        {
            public const string SourceCategoryDefaultId = "App.UiManagement.Searches.SourceCategoryDefaultId";
            public const string SourceDetailDefaultId = "App.UiManagement.Searches.SourceDetailDefaultId";
            public const string ProvincesIds = "App.UiManagement.Searches.ProvincesIds";
        }

        public static class Offers
        {
            public const string ContractDate = "App.UiManagement.Offers.ContractDate";
            public const string ShowDescription = "App.UiManagement.Offers.ShowDescription";
            public const string ContractExistenceForOfferToBecomeActive = "App.UiManagement.Offers.ContractExistenceForOfferToBecomeActive";
        }

        public static class PotentialContacts
        {
            public const string RegisterSearchBuyDays = "App.UiManagement.PotentialContacts.RegisterSearchBuyDays";
            public const string RegisterSearchRentDays = "App.UiManagement.PotentialContacts.RegisterSearchRentDays";
            public const string ReleaseSearchDaysForInfluenceSphereRecommendationExternalBrokerSource
                = "App.UiManagement.PotentialContacts.ReleaseSearchDaysForInfluenceSphereRecommendationExternalBrokerSource";
            public const string ReleaseSearchDaysForConsultingActivityFiledPotentialsCorporateClientsSource
                = "App.UiManagement.PotentialContacts.ReleaseSearchDaysForConsultingActivityFiledPotentialsCorporateClientsSource";
            public const string ReleaseSearchDaysForAdvertisementSource 
                = "App.UiManagement.PotentialContacts.ReleaseSearchDaysForAdvertisementSource";
            public const string ReleaseSearchDaysForCommissionContract 
                = "App.UiManagement.PotentialContacts.ReleaseSearchDaysForCommissionContract";
            public const string ReleaseSearchDaysForExclusiveContract 
                = "App.UiManagement.PotentialContacts.ReleaseSearchDaysForExclusiveContract";
            public const string ReleaseSearchDaysForExclusiveContractForInfluenceSphereRecommendationExternalBrokerSource
                = "App.UiManagement.PotentialContacts.ReleaseSearchDaysForExclusiveContractForInfluenceSphereRecommendationExternalBrokerSource";
            public const string ReleaseSearchDaysForExclusiveContractForConsultingActivityFiledPotentialsCorporateClientsSource
                = "App.UiManagement.PotentialContacts.ReleaseSearchDaysForExclusiveContractForConsultingActivityFiledPotentialsCorporateClientsSource";
            public const string ReleaseSearchDaysForExclusiveContractForAdvertisementSource
                = "App.UiManagement.PotentialContacts.ReleaseSearchDaysForExclusiveContractForAdvertisementSource";
        }

        public static class Clients
        {
            public const string ShowDocumentsTab = "App.UiManagement.Clients.ShowDocumentsTab";
            public const string RegisterOfferSellDaysSingleOffer = "App.UiManagement.Clients.RegisterOfferSellDaysSingleOffer";
            public const string RegisterOfferSellDaysOfferInProject = "App.UiManagement.Clients.RegisterOfferSellDaysOfferInProject";
            public const string RegisterOfferRentDays = "App.UiManagement.Clients.RegisterOfferRentDays";
            public const string ReleaseOfferDaysForInfluenceSphereRecommendationExternalBrokerSource = "App.UiManagement.Clients.ReleaseOfferDaysForInfluenceSphereRecommendationExternalBrokerSource";
            public const string ReleaseOfferDaysForConsultingActivityFiledPotentialsCorporateClientsSource = "App.UiManagement.Clients.ReleaseOfferDaysForConsultingActivityFiledPotentialsCorporateClientsSource";
            public const string ReleaseOfferDaysForAdvertisementSource = "App.UiManagement.Clients.ReleaseOfferDaysForAdvertisementSource";
            public const string ReleaseOfferDaysForCommissionContract = "App.UiManagement.Clients.ReleaseOfferDaysForCommissionContract";
            public const string ReleaseOfferDaysForExclusiveContract = "App.UiManagement.Clients.ReleaseOfferDaysForExclusiveContract";
            public const string ReleaseOfferDaysForProject = "App.UiManagement.Clients.ReleaseOfferDaysForProject";
            public const string ReleaseOfferDaysForUnfilledOfferWithContract = "App.UiManagement.Clients.ReleaseOfferDaysForUnfilledOfferWithContract";
        }

        public static class Employees
        {
            public const string IsMiddleNameRequired = "App.UiManagement.Employees.IsMiddleNameRequired";
            public const string IsIdentificationNumberRequired = "App.UiManagement.Employees.IsIdentificationNumberRequired";
            public const string IsTeamRequired = "App.UiManagement.Employees.IsTeamRequired";
        }
    }

    public static class TenantManagement
    {
        public const string AllowSelfRegistration = "App.TenantManagement.AllowSelfRegistration";
        public const string IsNewRegisteredTenantActiveByDefault = "App.TenantManagement.IsNewRegisteredTenantActiveByDefault";
        public const string UseCaptchaOnRegistration = "App.TenantManagement.UseCaptchaOnRegistration";
        public const string DefaultEdition = "App.TenantManagement.DefaultEdition";
        public const string SubscriptionExpireNotifyDayCount = "App.TenantManagement.SubscriptionExpireNotifyDayCount";
        public const string BillingLegalName = "App.TenantManagement.BillingLegalName";
        public const string BillingAddress = "App.TenantManagement.BillingAddress";
        public const string BillingTaxVatNo = "App.TenantManagement.BillingTaxVatNo";
    }

    public static class UserManagement
    {
        public static class TwoFactorLogin
        {
            public const string IsGoogleAuthenticatorEnabled = "App.UserManagement.TwoFactorLogin.IsGoogleAuthenticatorEnabled";
        }

        public static class SessionTimeOut
        {
            public const string IsEnabled = "App.UserManagement.SessionTimeOut.IsEnabled";
            public const string TimeOutSecond = "App.UserManagement.SessionTimeOut.TimeOutSecond";
            public const string ShowTimeOutNotificationSecond = "App.UserManagement.SessionTimeOut.ShowTimeOutNotificationSecond";
            public const string ShowLockScreenWhenTimedOut = "App.UserManagement.SessionTimeOut.ShowLockScreenWhenTimedOut";
        }

        public const string AllowSelfRegistration = "App.UserManagement.AllowSelfRegistration";
        public const string IsNewRegisteredUserActiveByDefault = "App.UserManagement.IsNewRegisteredUserActiveByDefault";
        public const string UseCaptchaOnRegistration = "App.UserManagement.UseCaptchaOnRegistration";
        public const string UseCaptchaOnLogin = "App.UserManagement.UseCaptchaOnLogin";
        public const string SmsVerificationEnabled = "App.UserManagement.SmsVerificationEnabled";
        public const string IsCookieConsentEnabled = "App.UserManagement.IsCookieConsentEnabled";
        public const string IsQuickThemeSelectEnabled = "App.UserManagement.IsQuickThemeSelectEnabled";
        public const string AllowOneConcurrentLoginPerUser = "App.UserManagement.AllowOneConcurrentLoginPerUser";
        public const string AllowUsingGravatarProfilePicture = "App.UserManagement.AllowUsingGravatarProfilePicture";
        public const string UseGravatarProfilePicture = "App.UserManagement.UseGravatarProfilePicture";

        public static class Password
        {
            public const string EnableCheckingLastXPasswordWhenPasswordChange = "App.UserManagement.EnableCheckingLastXPasswordWhenPasswordChange";
            public const string CheckingLastXPasswordCount = "App.UserManagement.CheckingLastXPasswordCount";

            public const string EnablePasswordExpiration = "App.UserManagement.EnablePasswordExpiration";
            public const string PasswordExpirationDayCount = "App.UserManagement.PasswordExpirationDayCount";
            public const string PasswordResetCodeExpirationHours = "App.UserManagement.PasswordResetCodeExpirationHours";
        }
    }

    public static class Email
    {
        public const string UseHostDefaultEmailSettings = "App.Email.UseHostDefaultEmailSettings";
    }

    public static class Recaptcha
    {
        public const string SiteKey = "Recaptcha.SiteKey";
    }

    public static class ExternalLoginProvider
    {
        public const string OpenIdConnectMappedClaims = "ExternalLoginProvider.OpenIdConnect.MappedClaims";
        public const string WsFederationMappedClaims = "ExternalLoginProvider.WsFederation.MappedClaims";

        public static class Host
        {
            public const string Facebook = "ExternalLoginProvider.Facebook";
            public const string Google = "ExternalLoginProvider.Google";
            public const string Twitter = "ExternalLoginProvider.Twitter";
            public const string Microsoft = "ExternalLoginProvider.Microsoft";
            public const string OpenIdConnect = "ExternalLoginProvider.OpenIdConnect";
            public const string WsFederation = "ExternalLoginProvider.WsFederation";
        }

        public static class Tenant
        {
            public const string Facebook = "ExternalLoginProvider.Facebook.Tenant";
            public const string FacebookIsDeactivated = "ExternalLoginProvider.Facebook.IsDeactivated";
            public const string Google = "ExternalLoginProvider.Google.Tenant";
            public const string GoogleIsDeactivated = "ExternalLoginProvider.Google.IsDeactivated";
            public const string Twitter = "ExternalLoginProvider.Twitter.Tenant";
            public const string TwitterIsDeactivated = "ExternalLoginProvider.Twitter.IsDeactivated";
            public const string Microsoft = "ExternalLoginProvider.Microsoft.Tenant";
            public const string MicrosoftIsDeactivated = "ExternalLoginProvider.Microsoft.IsDeactivated";
            public const string OpenIdConnect = "ExternalLoginProvider.OpenIdConnect.Tenant";
            public const string OpenIdConnectIsDeactivated = "ExternalLoginProvider.OpenIdConnect.IsDeactivated";
            public const string WsFederation = "ExternalLoginProvider.WsFederation.Tenant";
            public const string WsFederationIsDeactivated = "ExternalLoginProvider.WsFederation.IsDeactivated";
        }
    }
}