namespace RealtoCrm.Deals;

using System;
using Abp.Domain.Entities.Auditing;
using Comments;

public class DealParticipationComment : IFullAudited, IHaveComment
{
    public int DealParticipationId { get; set; }

    public DealParticipation DealParticipation { get; set; } = default!;

    public int CommentId { get; set; }

    public Comment Comment { get; set; } = default!;

    public DateTime CreationTime { get; set; }

    public long? CreatorUserId { get; set; }

    public DateTime? LastModificationTime { get; set; }

    public long? LastModifierUserId { get; set; }

    public bool IsDeleted { get; set; }

    public DateTime? DeletionTime { get; set; }

    public long? DeleterUserId { get; set; }
}