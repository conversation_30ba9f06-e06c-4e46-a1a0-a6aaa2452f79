namespace RealtoCrm.Authorization.Roles;

using Abp.Authorization.Roles;
using MultiTenancy;
using Users;

/// <summary>
/// Represents a role in the system.
/// </summary>
public class Role : AbpRole<User>
{
    public Role()
    {
    }

    public Role(int? tenantId, string displayName)
        : base(tenantId, displayName)
    {
    }

    public Role(int? tenantId, string name, string displayName)
        : base(tenantId, name, displayName)
    {
    }

    public Tenant? Tenant { get; set; }
}