namespace RealtoCrm.Authorization;

using Abp.Authorization;
using Abp.Configuration.Startup;
using Abp.Localization;

/// <summary>
/// Application's authorization provider.
/// Defines permissions for the application.
/// See <see cref="AppPermissions"/> for all permission names.
/// </summary>
public class AppAuthorizationProvider(bool isMultiTenancyEnabled) : AuthorizationProvider
{
    public AppAuthorizationProvider(IMultiTenancyConfig multiTenancyConfig)
        : this(multiTenancyConfig.IsEnabled)
    {
    }

    public override void SetPermissions(IPermissionDefinitionContext context)
    {
        CreatePermission(context, AppPermissions.AdministrativeDivisionsRead, L(nameof(AppPermissions.AdministrativeDivisionsRead)));
        CreatePermission(context, AppPermissions.AdministrativeDivisionsUpdate, L(nameof(AppPermissions.AdministrativeDivisionsUpdate)));
        CreatePermission(context, AppPermissions.AdministrativeDivisionsCreate, L(nameof(AppPermissions.AdministrativeDivisionsCreate)));
        CreatePermission(context, AppPermissions.AdministrativeDivisionsDelete, L(nameof(AppPermissions.AdministrativeDivisionsDelete)));
        CreatePermission(context, AppPermissions.EstateRead, L(nameof(AppPermissions.EstateRead)));
        CreatePermission(context, AppPermissions.EstateUpdate, L(nameof(AppPermissions.EstateUpdate)));
        CreatePermission(context, AppPermissions.EstateCreate, L(nameof(AppPermissions.EstateCreate)));
        CreatePermission(context, AppPermissions.EstateDelete, L(nameof(AppPermissions.EstateDelete)));
        CreatePermission(context, AppPermissions.TeamRead, L(nameof(AppPermissions.TeamRead)));
        CreatePermission(context, AppPermissions.TeamUpdate, L(nameof(AppPermissions.TeamUpdate)));
        CreatePermission(context, AppPermissions.TeamCreate, L(nameof(AppPermissions.TeamCreate)));
        CreatePermission(context, AppPermissions.TeamDelete, L(nameof(AppPermissions.TeamDelete)));
        CreatePermission(context, AppPermissions.MarketingRead, L(nameof(AppPermissions.MarketingRead)));
        CreatePermission(context, AppPermissions.MarketingUpdate, L(nameof(AppPermissions.MarketingUpdate)));
        CreatePermission(context, AppPermissions.MarketingCreate, L(nameof(AppPermissions.MarketingCreate)));
        CreatePermission(context, AppPermissions.MarketingDelete, L(nameof(AppPermissions.MarketingDelete)));
        CreatePermission(context, AppPermissions.GeneralSettingsRead, L(nameof(AppPermissions.GeneralSettingsRead)));
        CreatePermission(context, AppPermissions.GeneralSettingsUpdate, L(nameof(AppPermissions.GeneralSettingsUpdate)));
        CreatePermission(context, AppPermissions.GeneralSettingsCreate, L(nameof(AppPermissions.GeneralSettingsCreate)));
        CreatePermission(context, AppPermissions.GeneralSettingsDelete, L(nameof(AppPermissions.GeneralSettingsDelete)));
        CreatePermission(context, AppPermissions.OfferRead, L(nameof(AppPermissions.OfferRead)));
        CreatePermission(context, AppPermissions.OfferUpdate, L(nameof(AppPermissions.OfferUpdate)));
        CreatePermission(context, AppPermissions.OfferCreate, L(nameof(AppPermissions.OfferCreate)));
        CreatePermission(context, AppPermissions.OfferChangeStatus, L(nameof(AppPermissions.OfferChangeStatus)));
        CreatePermission(context, AppPermissions.OfferPublishToWebsites, L(nameof(AppPermissions.OfferPublishToWebsites)));
        CreatePermission(context, AppPermissions.OfferPublishingAuthorize, L(nameof(AppPermissions.OfferPublishingAuthorize)));
        CreatePermission(context, AppPermissions.OfferUnpublishToWebsites, L(nameof(AppPermissions.OfferUnpublishToWebsites)));
        CreatePermission(context, AppPermissions.OfferArchiveAction, L(nameof(AppPermissions.OfferArchiveAction)));
        CreatePermission(context, AppPermissions.OfferUploadContract, L(nameof(AppPermissions.OfferUploadContract)));
        CreatePermission(context, AppPermissions.OfferOfferToEmployeeAction, L(nameof(AppPermissions.OfferOfferToEmployeeAction)));
        CreatePermission(context, AppPermissions.OfferTransferToEmployeeAction, L(nameof(AppPermissions.OfferTransferToEmployeeAction)));
        CreatePermission(context, AppPermissions.OfferGenerateSpaAction, L(nameof(AppPermissions.OfferGenerateSpaAction)));
        CreatePermission(context, AppPermissions.OfferChangeOwnerAction, L(nameof(AppPermissions.OfferChangeOwnerAction)));
        CreatePermission(context, AppPermissions.OfferAddEncumbrancesAction, L(nameof(AppPermissions.OfferAddEncumbrancesAction)));
        CreatePermission(context, AppPermissions.EstateChangeOwner, L(nameof(AppPermissions.EstateChangeOwner)));
        CreatePermission(context, AppPermissions.ContactView, L(nameof(AppPermissions.ContactView)));
        CreatePermission(context, AppPermissions.ContactAllAccess, L(nameof(AppPermissions.ContactAllAccess)));
        CreatePermission(context, AppPermissions.ContactUpdate, L(nameof(AppPermissions.ContactUpdate)));
        CreatePermission(context, AppPermissions.ContactCreate, L(nameof(AppPermissions.ContactCreate)));
        CreatePermission(context, AppPermissions.ContactBrokerAssign, L(nameof(AppPermissions.ContactBrokerAssign)));
        CreatePermission(context, AppPermissions.ContactSoftDelete, L(nameof(AppPermissions.ContactSoftDelete)));
        CreatePermission(context, AppPermissions.OfferEstateLocationVerification, L(nameof(AppPermissions.OfferEstateLocationVerification)));
        CreatePermission(context, AppPermissions.OfferEstateAddressVerification, L(nameof(AppPermissions.OfferEstateAddressVerification)));
        CreatePermission(context, AppPermissions.SearchRead, L(nameof(AppPermissions.SearchRead)));
        CreatePermission(context, AppPermissions.SearchCreate, L(nameof(AppPermissions.SearchCreate)));
        CreatePermission(context, AppPermissions.SearchUpdate, L(nameof(AppPermissions.SearchUpdate)));
        CreatePermission(context, AppPermissions.SearchChangeStatus, L(nameof(AppPermissions.SearchChangeStatus)));
        CreatePermission(context, AppPermissions.SearchAddContract, L(nameof(AppPermissions.SearchAddContract)));
        CreatePermission(context, AppPermissions.SearchAssignTo, L(nameof(AppPermissions.SearchAssignTo)));
        CreatePermission(context, AppPermissions.EstateHistoryRead, L(nameof(AppPermissions.EstateHistoryRead)));
        CreatePermission(context, AppPermissions.EstateHistoryUpdate, L(nameof(AppPermissions.EstateHistoryUpdate)));
        CreatePermission(context, AppPermissions.BuildingProjectRead, L(nameof(AppPermissions.BuildingProjectRead)));
        CreatePermission(context, AppPermissions.BuildingProjectCreate, L(nameof(AppPermissions.BuildingProjectCreate)));
        CreatePermission(context, AppPermissions.BuildingProjectUpdate, L(nameof(AppPermissions.BuildingProjectUpdate)));
        CreatePermission(context, AppPermissions.BuildingProjectAddContract, L(nameof(AppPermissions.BuildingProjectAddContract)));
        CreatePermission(context, AppPermissions.BuildingProjectChangeOwner, L(nameof(AppPermissions.BuildingProjectChangeOwner)));
        CreatePermission(context, AppPermissions.BuildingProjectAssignTo, L(nameof(AppPermissions.BuildingProjectAssignTo)));
        CreatePermission(context, AppPermissions.OfferSaveToFavourites, L(nameof(AppPermissions.OfferSaveToFavourites)));
        CreatePermission(context, AppPermissions.OfferSendOverViber, L(nameof(AppPermissions.OfferSendOverViber)));
        CreatePermission(context, AppPermissions.OfferSendOverEmail, L(nameof(AppPermissions.OfferSendOverEmail)));
        CreatePermission(context, AppPermissions.OfferSendOverWhatsapp, L(nameof(AppPermissions.OfferSendOverWhatsapp)));
        CreatePermission(context, AppPermissions.SPACreate, L(nameof(AppPermissions.SPACreate)));
        CreatePermission(context, AppPermissions.SPAUpdate, L(nameof(AppPermissions.SPAUpdate)));
        CreatePermission(context, AppPermissions.SPAViewPDF, L(nameof(AppPermissions.SPAViewPDF)));
        CreatePermission(context, AppPermissions.SPADownloadPDF, L(nameof(AppPermissions.SPADownloadPDF)));
        CreatePermission(context, AppPermissions.NomenclaturesRead, L(nameof(AppPermissions.NomenclaturesRead)));
        CreatePermission(context, AppPermissions.NomenclaturesUpdate, L(nameof(AppPermissions.NomenclaturesUpdate)));
        CreatePermission(context, AppPermissions.NomenclaturesCreate, L(nameof(AppPermissions.NomenclaturesCreate)));
        CreatePermission(context, AppPermissions.NomenclaturesDelete, L(nameof(AppPermissions.NomenclaturesDelete)));
        CreatePermission(context, AppPermissions.MatchRead, L(nameof(AppPermissions.MatchRead)));
        CreatePermission(context, AppPermissions.MatchUpdate, L(nameof(AppPermissions.MatchUpdate)));
        CreatePermission(context, AppPermissions.MatchCreate, L(nameof(AppPermissions.MatchCreate)));
        CreatePermission(context, AppPermissions.MatchDelete, L(nameof(AppPermissions.MatchDelete)));
        CreatePermission(context, AppPermissions.ProjectRead, L(nameof(AppPermissions.ProjectRead)));
        CreatePermission(context, AppPermissions.ProjectUpdate, L(nameof(AppPermissions.ProjectUpdate)));
        CreatePermission(context, AppPermissions.ProjectCreate, L(nameof(AppPermissions.ProjectCreate)));
        CreatePermission(context, AppPermissions.ProjectDelete, L(nameof(AppPermissions.ProjectDelete)));
        CreatePermission(context, AppPermissions.FilterSaveToFavourites, L(nameof(AppPermissions.FilterSaveToFavourites)));
        CreatePermission(context, AppPermissions.SearchesHistoryRead, L(nameof(AppPermissions.SearchesHistoryRead)));
        CreatePermission(context, AppPermissions.TaskRead, L(nameof(AppPermissions.TaskRead)));
        CreatePermission(context, AppPermissions.TaskUpdate, L(nameof(AppPermissions.TaskUpdate)));
        CreatePermission(context, AppPermissions.TaskCreate, L(nameof(AppPermissions.TaskCreate)));
        CreatePermission(context, AppPermissions.TaskAssign, L(nameof(AppPermissions.TaskAssign)));
        CreatePermission(context, AppPermissions.TaskDone, L(nameof(AppPermissions.TaskDone)));
        CreatePermission(context, AppPermissions.TaskManagerApproved, L(nameof(AppPermissions.TaskManagerApproved)));
        CreatePermission(context, AppPermissions.NoteCreate, L(nameof(AppPermissions.NoteCreate)));
        CreatePermission(context, AppPermissions.NoteRead, L(nameof(AppPermissions.NoteRead)));
        CreatePermission(context, AppPermissions.NoteUpdate, L(nameof(AppPermissions.NoteUpdate)));
        CreatePermission(context, AppPermissions.NoteDelete, L(nameof(AppPermissions.NoteDelete)));
        CreatePermission(context, AppPermissions.ViewingCreate, L(nameof(AppPermissions.ViewingCreate)));
        CreatePermission(context, AppPermissions.ViewingRead, L(nameof(AppPermissions.ViewingRead)));
        CreatePermission(context, AppPermissions.ViewingUpdate, L(nameof(AppPermissions.ViewingUpdate)));
        CreatePermission(context, AppPermissions.ViewingChangeStatus, L(nameof(AppPermissions.ViewingChangeStatus)));
        CreatePermission(context, AppPermissions.ViewingAddRecord, L(nameof(AppPermissions.ViewingAddRecord)));
        CreatePermission(context, AppPermissions.MeetingCreate, L(nameof(AppPermissions.MeetingCreate)));
        CreatePermission(context, AppPermissions.MeetingRead, L(nameof(AppPermissions.MeetingRead)));
        CreatePermission(context, AppPermissions.MeetingUpdate, L(nameof(AppPermissions.MeetingUpdate)));
        CreatePermission(context, AppPermissions.MeetingChangeStatus, L(nameof(AppPermissions.MeetingChangeStatus)));
        CreatePermission(context, AppPermissions.OfferCreatePicturesFor, L(nameof(AppPermissions.OfferCreatePicturesFor)));
        CreatePermission(context, AppPermissions.OfferUpdatePicturesFor, L(nameof(AppPermissions.OfferUpdatePicturesFor)));
        CreatePermission(context, AppPermissions.DepositRead, L(nameof(AppPermissions.DepositRead)));
        CreatePermission(context, AppPermissions.DepositUpdate, L(nameof(AppPermissions.DepositUpdate)));
        CreatePermission(context, AppPermissions.DepositCreate, L(nameof(AppPermissions.DepositCreate)));
        CreatePermission(context, AppPermissions.DepositChangeStatus, L(nameof(AppPermissions.DepositChangeStatus)));
        CreatePermission(context, AppPermissions.DepositAssignToAnotherOffer, L(nameof(AppPermissions.DepositAssignToAnotherOffer)));
        CreatePermission(context, AppPermissions.DepositAddDocument, L(nameof(AppPermissions.DepositAddDocument)));
        CreatePermission(context, AppPermissions.DepositActions, L(nameof(AppPermissions.DepositActions)));
        CreatePermission(context, AppPermissions.DealCreate, L(nameof(AppPermissions.DealCreate)));
        CreatePermission(context, AppPermissions.DealUpdate, L(nameof(AppPermissions.DealUpdate)));
        CreatePermission(context, AppPermissions.DealRead, L(nameof(AppPermissions.DealRead)));
        CreatePermission(context, AppPermissions.DealChangeStatus, L(nameof(AppPermissions.DealChangeStatus)));
        CreatePermission(context, AppPermissions.DealAddCommissionPayment, L(nameof(AppPermissions.DealAddCommissionPayment)));
        CreatePermission(context, AppPermissions.DealAddPreliminaryContract, L(nameof(AppPermissions.DealAddPreliminaryContract)));
        CreatePermission(context, AppPermissions.DealDoneWithTransaction, L(nameof(AppPermissions.DealDoneWithTransaction)));
        CreatePermission(context, AppPermissions.DealArchiveWithoutTransaction, L(nameof(AppPermissions.DealArchiveWithoutTransaction)));
        CreatePermission(context, AppPermissions.DealAllSidesAccess, L(nameof(AppPermissions.DealAllSidesAccess)));
        CreatePermission(context, AppPermissions.NotificationsRead, L(nameof(AppPermissions.NotificationsRead)));
        CreatePermission(context, AppPermissions.NotificationsUpdate, L(nameof(AppPermissions.NotificationsUpdate)));
        CreatePermission(context, AppPermissions.NotificationsClear, L(nameof(AppPermissions.NotificationsClear)));
        CreatePermission(context, AppPermissions.KanbanPieChartRead, L(nameof(AppPermissions.KanbanPieChartRead)));
        CreatePermission(context, AppPermissions.KanbanPieChartUpdate, L(nameof(AppPermissions.KanbanPieChartUpdate)));
        CreatePermission(context, AppPermissions.KanbanPieChartCreate, L(nameof(AppPermissions.KanbanPieChartCreate)));
        CreatePermission(context, AppPermissions.UserCreate, L(nameof(AppPermissions.UserCreate)));
        CreatePermission(context, AppPermissions.UserUpdate, L(nameof(AppPermissions.UserUpdate)));
        CreatePermission(context, AppPermissions.UserDelete, L(nameof(AppPermissions.UserDelete)));
        CreatePermission(context, AppPermissions.UserRead, L(nameof(AppPermissions.UserRead)));
        CreatePermission(context, AppPermissions.UserImpersonate, L(nameof(AppPermissions.UserImpersonate)));
        CreatePermission(context, AppPermissions.UserPhoneNumberRead, L(nameof(AppPermissions.UserPhoneNumberRead)));
        CreatePermission(context, AppPermissions.UserRoleCreate, L(nameof(AppPermissions.UserRoleCreate)));
        CreatePermission(context, AppPermissions.UserRoleUpdate, L(nameof(AppPermissions.UserRoleUpdate)));
        CreatePermission(context, AppPermissions.UserRoleDelete, L(nameof(AppPermissions.UserRoleDelete)));
        CreatePermission(context, AppPermissions.UserRoleRead, L(nameof(AppPermissions.UserRoleRead)));
        CreatePermission(context, AppPermissions.UserRoleCompanyFilter, L(nameof(AppPermissions.UserRoleCompanyFilter)));
        CreatePermission(context, AppPermissions.EmployeeCreate, L(nameof(AppPermissions.EmployeeCreate)));
        CreatePermission(context, AppPermissions.EmployeeUpdate, L(nameof(AppPermissions.EmployeeUpdate)));
        CreatePermission(context, AppPermissions.EmployeeDelete, L(nameof(AppPermissions.EmployeeDelete)));
        CreatePermission(context, AppPermissions.EmployeeRead, L(nameof(AppPermissions.EmployeeRead)));
        CreatePermission(context, AppPermissions.TrainingsRead, L(nameof(AppPermissions.TrainingsRead)));
        CreatePermission(context, AppPermissions.TrainingsUpdate, L(nameof(AppPermissions.TrainingsUpdate)));
        CreatePermission(context, AppPermissions.TrainingsDelete, L(nameof(AppPermissions.TrainingsDelete)));
        CreatePermission(context, AppPermissions.RankingListRead, L(nameof(AppPermissions.RankingListRead)));
        CreatePermission(context, AppPermissions.CallsLogRead, L(nameof(AppPermissions.CallsLogRead)));
        CreatePermission(context, AppPermissions.DashboardRead, L(nameof(AppPermissions.DashboardRead)));
        CreatePermission(context, AppPermissions.DashboardSetting, L(nameof(AppPermissions.DashboardSetting)));
        CreatePermission(context, AppPermissions.ProfilePictureCreate, L(nameof(AppPermissions.ProfilePictureCreate)));
        CreatePermission(context, AppPermissions.ProfilePictureUpdate, L(nameof(AppPermissions.ProfilePictureUpdate)));
        CreatePermission(context, AppPermissions.DisplayNameCreate, L(nameof(AppPermissions.DisplayNameCreate)));
        CreatePermission(context, AppPermissions.DisplayNameUpdate, L(nameof(AppPermissions.DisplayNameUpdate)));
        CreatePermission(context, AppPermissions.SetDefaultCity, L(nameof(AppPermissions.SetDefaultCity)));
        CreatePermission(context, AppPermissions.SetDefaultListSize, L(nameof(AppPermissions.SetDefaultListSize)));
        CreatePermission(context, AppPermissions.SetDefaultTheme, L(nameof(AppPermissions.SetDefaultTheme)));
        CreatePermission(context, AppPermissions.CallCenterSurveyCreate, L(nameof(AppPermissions.CallCenterSurveyCreate)));
        CreatePermission(context, AppPermissions.CallCenterSurveyUpdate, L(nameof(AppPermissions.CallCenterSurveyUpdate)));
        CreatePermission(context, AppPermissions.InquiryCreate, L(nameof(AppPermissions.InquiryCreate)));
        CreatePermission(context, AppPermissions.InquiryUpdate, L(nameof(AppPermissions.InquiryUpdate)));
        CreatePermission(context, AppPermissions.InquiryRead, L(nameof(AppPermissions.InquiryRead)));
        CreatePermission(context, AppPermissions.InquiryDelete, L(nameof(AppPermissions.InquiryDelete)));
        CreatePermission(context, AppPermissions.InquirySendToEmail, L(nameof(AppPermissions.InquirySendToEmail)));
        CreatePermission(context, AppPermissions.ContactsMerge, L(nameof(AppPermissions.ContactsMerge)));
        CreatePermission(context, AppPermissions.TargetSetForTeam, L(nameof(AppPermissions.TargetSetForTeam)));
        CreatePermission(context, AppPermissions.TargetSetForExternalExport, L(nameof(AppPermissions.TargetSetForExternalExport)));
        CreatePermission(context, AppPermissions.OfferTeamEdit, L(nameof(AppPermissions.OfferTeamEdit)));
        CreatePermission(context, AppPermissions.OfferDepartmentEdit, L(nameof(AppPermissions.OfferDepartmentEdit)));
        CreatePermission(context, AppPermissions.OfferDivisionEdit, L(nameof(AppPermissions.OfferDivisionEdit)));
        CreatePermission(context, AppPermissions.AdministrationFullAccess, L(nameof(AppPermissions.AdministrationFullAccess)));
        CreatePermission(context, AppPermissions.OfferDelete, L(nameof(AppPermissions.OfferDelete)));
        CreatePermission(context, AppPermissions.OfferTeamAccess, L(nameof(AppPermissions.OfferTeamAccess)));
        CreatePermission(context, AppPermissions.OfferArchiveAccess, L(nameof(AppPermissions.OfferArchiveAccess)));
        CreatePermission(context, AppPermissions.OfferFreeAccess, L(nameof(AppPermissions.OfferFreeAccess)));
        CreatePermission(context, AppPermissions.SearchTeamAccess, L(nameof(AppPermissions.SearchTeamAccess)));
        CreatePermission(context, AppPermissions.SearchArchiveAccess, L(nameof(AppPermissions.SearchArchiveAccess)));
        CreatePermission(context, AppPermissions.SearchAllAccess, L(nameof(AppPermissions.SearchAllAccess)));
        CreatePermission(context, AppPermissions.ContactTeamAccess, L(nameof(AppPermissions.ContactTeamAccess)));
        CreatePermission(context, AppPermissions.DocumentsRead, L(nameof(AppPermissions.DocumentsRead)));
        CreatePermission(context, AppPermissions.DocumentsUpdate, L(nameof(AppPermissions.DocumentsUpdate)));
        CreatePermission(context, AppPermissions.DocumentsCreate, L(nameof(AppPermissions.DocumentsCreate)));
        CreatePermission(context, AppPermissions.DocumentsDelete, L(nameof(AppPermissions.DocumentsDelete)));
        CreatePermission(context, AppPermissions.LinkedProjectsCreate, L(nameof(AppPermissions.LinkedProjectsCreate)));
        CreatePermission(context, AppPermissions.OfferAllAccess, L(nameof(AppPermissions.OfferAllAccess)));
        CreatePermission(context, AppPermissions.SearchFreeAccess, L(nameof(AppPermissions.SearchFreeAccess)));
        CreatePermission(context, AppPermissions.SearchTransferTo, L(nameof(AppPermissions.SearchTransferTo)));
        CreatePermission(context, AppPermissions.SearchChangeOwner, L(nameof(AppPermissions.SearchChangeOwner)));
        CreatePermission(context, AppPermissions.SearchArchive, L(nameof(AppPermissions.SearchArchive)));
        CreatePermission(context, AppPermissions.SearchActivate, L(nameof(AppPermissions.SearchActivate)));
        CreatePermission(context, AppPermissions.SearchDelete, L(nameof(AppPermissions.SearchDelete)));
        CreatePermission(context, AppPermissions.SearchTeamEdit, L(nameof(AppPermissions.SearchTeamEdit)));
        CreatePermission(context, AppPermissions.SearchDepartmentEdit, L(nameof(AppPermissions.SearchDepartmentEdit)));
        CreatePermission(context, AppPermissions.SearchDivisionEdit, L(nameof(AppPermissions.SearchDivisionEdit)));
    }

    private static Permission CreatePermission(
        IPermissionDefinitionContext context,
        string name,
        ILocalizableString displayName)
        => context.GetPermissionOrNull(name)
           ?? context.CreatePermission(name, displayName);

    private static ILocalizableString L(string name)
        => new LocalizableString(name, RealtoCrmConsts.LocalizationSourceName);
}