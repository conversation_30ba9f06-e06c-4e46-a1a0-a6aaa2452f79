using System.Threading.Tasks;

namespace RealtoCrm.Authorization.Users;

public interface IUserEmailer
{
    /// <summary>
    /// Send email activation link to user's email address.
    /// </summary>
    /// <param name="user">User</param>
    /// <param name="link">Email activation link</param>
    /// <param name="plainPassword">
    /// Can be set to user's plain password to include it in the email.
    /// </param>
    Task SendEmailActivationLinkAsync(User user, string link, string plainPassword = null);

    /// <summary>
    /// Sends a password reset link to user's email.
    /// </summary>
    /// <param name="user">User</param>
    /// <param name="employeeName">Employee name</param>
    /// <param name="link">Password reset link (optional)</param>
    Task SendPasswordResetLinkAsync(User user, string employeeName, string? link = null);

    /// <summary>
    /// Send email change link to user's email address.
    /// </summary>
    /// <param name="user">User</param>
    /// <param name="emailAddress">Email address</param>
    /// <param name="link">Email activation link</param>
    Task SendEmailChangeRequestLinkAsync(User user, string emailAddress, string link);
}