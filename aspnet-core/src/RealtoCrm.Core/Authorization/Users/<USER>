using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Authorization.Users;
using Abp.Configuration;
using Abp.IdentityFramework;
using Abp.Linq;
using Abp.Notifications;
using Abp.Runtime.Session;
using Abp.UI;
using Microsoft.AspNetCore.Identity;
using RealtoCrm.Authorization.Roles;
using RealtoCrm.Configuration;
using RealtoCrm.Debugging;
using RealtoCrm.MultiTenancy;
using RealtoCrm.Notifications;

namespace RealtoCrm.Authorization.Users;

public class UserRegistrationManager(
    TenantManager tenantManager,
    UserManager userManager,
    RoleManager roleManager,
    IUserEmailer userEmailer,
    INotificationSubscriptionManager notificationSubscriptionManager,
    IAppNotifier appNotifier,
    IUserPolicy userPolicy)
    : RealtoCrmDomainServiceBase
{
    public IAbpSession AbpSession { get; set; } = NullAbpSession.Instance;
    public IAsyncQueryableExecuter AsyncQueryableExecuter { get; set; } = NullAsyncQueryableExecuter.Instance;


    public async Task<User> RegisterAsync(string name, string surname, string emailAddress, string userName, string plainPassword, bool isEmailConfirmed, string emailActivationLink)
    {
        this.CheckForTenant();
        this.CheckSelfRegistrationIsEnabled();

        var tenant = await this.GetActiveTenantAsync();
        var isNewRegisteredUserActiveByDefault = await this.SettingManager.GetSettingValueAsync<bool>(AppSettings.UserManagement.IsNewRegisteredUserActiveByDefault);

        await userPolicy.CheckMaxUserCountAsync(tenant.Id);

        var user = new User
        {
            TenantId = tenant.Id,
            Name = name,
            Surname = surname,
            EmailAddress = emailAddress,
            IsActive = isNewRegisteredUserActiveByDefault,
            UserName = userName,
            IsEmailConfirmed = isEmailConfirmed,
            Roles = new List<UserRole>()
        };

        user.SetNormalizedNames();

        var defaultRoles = await this.AsyncQueryableExecuter.ToListAsync(roleManager.Roles.Where(r => r.IsDefault));
        foreach (var defaultRole in defaultRoles)
        {
            user.Roles.Add(new UserRole(tenant.Id, user.Id, defaultRole.Id));
        }

        await userManager.InitializeOptionsAsync(this.AbpSession.TenantId);
        this.CheckErrors(await userManager.CreateAsync(user, plainPassword));
        await this.CurrentUnitOfWork.SaveChangesAsync();

        if (!user.IsEmailConfirmed)
        {
            user.SetNewEmailConfirmationCode();
            await userEmailer.SendEmailActivationLinkAsync(user, emailActivationLink);
        }

        //Notifications
        await notificationSubscriptionManager.SubscribeToAllAvailableNotificationsAsync(user.ToUserIdentifier());
        await appNotifier.WelcomeToTheApplicationAsync(user);
        await appNotifier.NewUserRegisteredAsync(user);

        return user;
    }

    private void CheckForTenant()
    {
        if (!this.AbpSession.TenantId.HasValue)
        {
            throw new InvalidOperationException("Can not register host users!");
        }
    }

    private void CheckSelfRegistrationIsEnabled()
    {
        if (!this.SettingManager.GetSettingValue<bool>(AppSettings.UserManagement.AllowSelfRegistration))
        {
            throw new UserFriendlyException(this.L("SelfUserRegistrationIsDisabledMessage_Detail"));
        }
    }

    private bool UseCaptchaOnRegistration()
    {
        return this.SettingManager.GetSettingValue<bool>(AppSettings.UserManagement.UseCaptchaOnRegistration);
    }

    private async Task<Tenant> GetActiveTenantAsync()
    {
        if (!this.AbpSession.TenantId.HasValue)
        {
            return null;
        }

        return await this.GetActiveTenantAsync(this.AbpSession.TenantId.Value);
    }

    private async Task<Tenant> GetActiveTenantAsync(int tenantId)
    {
        var tenant = await tenantManager.FindByIdAsync(tenantId);
        if (tenant == null)
        {
            throw new UserFriendlyException(this.L("UnknownTenantId{0}", tenantId));
        }

        if (!tenant.IsActive)
        {
            throw new UserFriendlyException(this.L("TenantIdIsNotActive{0}", tenantId));
        }

        return tenant;
    }

    protected virtual void CheckErrors(IdentityResult identityResult)
    {
        identityResult.CheckErrors(this.LocalizationManager);
    }
}