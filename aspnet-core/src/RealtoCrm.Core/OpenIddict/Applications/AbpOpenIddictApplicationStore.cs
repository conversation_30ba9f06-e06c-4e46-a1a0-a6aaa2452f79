using System;
using System.Collections.Generic;
using System.Collections.Immutable;
using System.Globalization;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using System.Transactions;
using Abp;
using Abp.Domain.Uow;
using Microsoft.Extensions.Logging;
using RealtoCrm.OpenIddict.Tokens;
using OpenIddict.Abstractions;

namespace RealtoCrm.OpenIddict.Applications;

public class AbpOpenIddictApplicationStore(
    IOpenIddictApplicationRepository repository,
    IUnitOfWorkManager unitOfWorkManager,
    IOpenIddictTokenRepository tokenRepository,
    IGuidGenerator guidGenerator,
    IOpenIddictDbConcurrencyExceptionHandler concurrencyExceptionHandler)
    : AbpOpenIddictStoreBase<IOpenIddictApplicationRepository>(repository, unitOfWorkManager, guidGenerator, concurrencyExceptionHandler),
        IAbpOpenIdApplicationStore
{
    protected IOpenIddictTokenRepository TokenRepository { get; } = tokenRepository;

    public async ValueTask<long> CountAsync(CancellationToken cancellationToken)
    {
        return await this.UnitOfWorkManager.WithUnitOfWorkAsync(async () =>
            await this.Repository.GetCountAsync(null, cancellationToken)
        );
    }

    public ValueTask<long> CountAsync<TResult>(
        Func<IQueryable<OpenIddictApplicationModel>, IQueryable<TResult>> query,
        CancellationToken cancellationToken)
    {
        throw new NotSupportedException();
    }

    public async ValueTask CreateAsync(OpenIddictApplicationModel application, CancellationToken cancellationToken)
    {
        Check.NotNull(application, nameof(application));

        await this.UnitOfWorkManager.WithUnitOfWorkAsync(async () =>
        {
            await this.Repository.InsertAsync(application.ToEntity());
            application = (await this.Repository.FindByClientIdAsync(application.Id.ToString(), cancellationToken))
                .ToModel();
        });
    }

    public async ValueTask DeleteAsync(OpenIddictApplicationModel application, CancellationToken cancellationToken)
    {
        Check.NotNull(application, nameof(application));

        try
        {
            using (var uow = this.UnitOfWorkManager.Begin(new UnitOfWorkOptions
                   {
                       Scope = TransactionScopeOption.RequiresNew,
                       IsolationLevel = IsolationLevel.RepeatableRead,
                       IsTransactional = true,
                   }))
            {
                await this.TokenRepository.DeleteManyByApplicationIdAsync(application.Id,
                    cancellationToken: cancellationToken);

                await this.Repository.DeleteAsync(application.Id);

                await uow.CompleteAsync();
            }
        }
        catch (AbpDbConcurrencyException e)
        {
            this.Logger.LogError(e, e.Message);
            await this.ConcurrencyExceptionHandler.HandleAsync(e);
            throw new OpenIddictExceptions.ConcurrencyException(e.Message, e.InnerException);
        }
    }

    public async ValueTask<OpenIddictApplicationModel> FindByIdAsync(string identifier,
        CancellationToken cancellationToken)
    {
        Check.NotNullOrEmpty(identifier, nameof(identifier));

        return await this.UnitOfWorkManager.WithUnitOfWorkAsync(async () =>
            (await this.Repository.FindByClientIdAsync(identifier, cancellationToken)).ToModel()
        );
    }

    public async ValueTask<OpenIddictApplicationModel> FindByClientIdAsync(string identifier,
        CancellationToken cancellationToken)
    {
        Check.NotNullOrEmpty(identifier, nameof(identifier));

        return await this.UnitOfWorkManager.WithUnitOfWorkAsync(async () =>
            (await this.Repository.FindByClientIdAsync(identifier, cancellationToken)).ToModel()
        );
    }

    public async IAsyncEnumerable<OpenIddictApplicationModel> FindByPostLogoutRedirectUriAsync(string uris,
        [EnumeratorCancellation] CancellationToken cancellationToken)
    {
        Check.NotNullOrEmpty(uris, nameof(uris));

        var applications = await this.UnitOfWorkManager.WithUnitOfWorkAsync(async () =>
            await this.Repository.FindByPostLogoutRedirectUriAsync(uris, cancellationToken)
        );

        foreach (var application in applications)
        {
            var addresses = await this.GetPostLogoutRedirectUrisAsync(application.ToModel(), cancellationToken);
            if (addresses.Contains(uris, StringComparer.Ordinal))
            {
                yield return application.ToModel();
            }
        }
    }

    public async IAsyncEnumerable<OpenIddictApplicationModel> FindByRedirectUriAsync(string uri,
        [EnumeratorCancellation] CancellationToken cancellationToken)
    {
        Check.NotNullOrEmpty(uri, nameof(uri));

        var applications = await this.UnitOfWorkManager.WithUnitOfWorkAsync(async () =>
            await this.Repository.FindByRedirectUriAsync(uri, cancellationToken)
        );

        foreach (var application in applications)
        {
            var uris = await this.GetRedirectUrisAsync(application.ToModel(), cancellationToken);
            if (uris.Contains(uri, StringComparer.Ordinal))
            {
                yield return application.ToModel();
            }
        }
    }

    public ValueTask<TResult> GetAsync<TState, TResult>(
        Func<IQueryable<OpenIddictApplicationModel>, TState, IQueryable<TResult>> query, TState state,
        CancellationToken cancellationToken)
    {
        throw new NotSupportedException();
    }

    public ValueTask<string> GetClientIdAsync(OpenIddictApplicationModel application,
        CancellationToken cancellationToken)
    {
        Check.NotNull(application, nameof(application));

        return new ValueTask<string>(application.ClientId);
    }

    public ValueTask<string> GetClientSecretAsync(OpenIddictApplicationModel application,
        CancellationToken cancellationToken)
    {
        Check.NotNull(application, nameof(application));

        return new ValueTask<string>(application.ClientSecret);
    }

    public ValueTask<string> GetClientTypeAsync(OpenIddictApplicationModel application,
        CancellationToken cancellationToken)
    {
        Check.NotNull(application, nameof(application));
        return new ValueTask<string>(application.Type);
    }

    public ValueTask<string> GetConsentTypeAsync(OpenIddictApplicationModel application,
        CancellationToken cancellationToken)
    {
        Check.NotNull(application, nameof(application));

        return new ValueTask<string>(application.ConsentType);
    }

    public ValueTask<string> GetDisplayNameAsync(OpenIddictApplicationModel application,
        CancellationToken cancellationToken)
    {
        Check.NotNull(application, nameof(application));

        return new ValueTask<string>(application.DisplayName);
    }

    public ValueTask<ImmutableDictionary<CultureInfo, string>> GetDisplayNamesAsync(
        OpenIddictApplicationModel application, CancellationToken cancellationToken)
    {
        Check.NotNull(application, nameof(application));

        if (string.IsNullOrEmpty(application.DisplayNames))
        {
            return new ValueTask<ImmutableDictionary<CultureInfo, string>>(ImmutableDictionary
                .Create<CultureInfo, string>());
        }

        using (var document = JsonDocument.Parse(application.DisplayNames))
        {
            var builder = ImmutableDictionary.CreateBuilder<CultureInfo, string>();

            foreach (var property in document.RootElement.EnumerateObject())
            {
                var value = property.Value.GetString();
                if (string.IsNullOrEmpty(value))
                {
                    continue;
                }

                builder[CultureInfo.GetCultureInfo(property.Name)] = value;
            }

            return new ValueTask<ImmutableDictionary<CultureInfo, string>>(builder.ToImmutable());
        }
    }

    public ValueTask<string> GetIdAsync(OpenIddictApplicationModel application, CancellationToken cancellationToken)
    {
        Check.NotNull(application, nameof(application));

        return new ValueTask<string>(application.Id.ToString());
    }

    public ValueTask<ImmutableArray<string>> GetPermissionsAsync(OpenIddictApplicationModel application,
        CancellationToken cancellationToken)
    {
        Check.NotNull(application, nameof(application));

        if (string.IsNullOrEmpty(application.Permissions))
        {
            return new ValueTask<ImmutableArray<string>>(ImmutableArray.Create<string>());
        }

        using (var document = JsonDocument.Parse(application.Permissions))
        {
            var builder = ImmutableArray.CreateBuilder<string>(document.RootElement.GetArrayLength());

            foreach (var element in document.RootElement.EnumerateArray())
            {
                var value = element.GetString();
                if (string.IsNullOrEmpty(value))
                {
                    continue;
                }

                builder.Add(value);
            }

            return new ValueTask<ImmutableArray<string>>(builder.ToImmutable());
        }
    }

    public ValueTask<ImmutableArray<string>> GetPostLogoutRedirectUrisAsync(OpenIddictApplicationModel application,
        CancellationToken cancellationToken)
    {
        Check.NotNull(application, nameof(application));

        if (string.IsNullOrEmpty(application.PostLogoutRedirectUris))
        {
            return new ValueTask<ImmutableArray<string>>(ImmutableArray.Create<string>());
        }

        using (var document = JsonDocument.Parse(application.PostLogoutRedirectUris))
        {
            var builder = ImmutableArray.CreateBuilder<string>(document.RootElement.GetArrayLength());

            foreach (var element in document.RootElement.EnumerateArray())
            {
                var value = element.GetString();
                if (string.IsNullOrEmpty(value))
                {
                    continue;
                }

                builder.Add(value);
            }

            return new ValueTask<ImmutableArray<string>>(builder.ToImmutable());
        }

        ;
    }

    public ValueTask<ImmutableDictionary<string, JsonElement>> GetPropertiesAsync(
        OpenIddictApplicationModel application, CancellationToken cancellationToken)
    {
        Check.NotNull(application, nameof(application));

        if (string.IsNullOrEmpty(application.Properties))
        {
            return new ValueTask<ImmutableDictionary<string, JsonElement>>(ImmutableDictionary
                .Create<string, JsonElement>());
        }

        using (var document = JsonDocument.Parse(application.Properties))
        {
            var builder = ImmutableDictionary.CreateBuilder<string, JsonElement>();

            foreach (var property in document.RootElement.EnumerateObject())
            {
                builder[property.Name] = property.Value.Clone();
            }

            return new ValueTask<ImmutableDictionary<string, JsonElement>>(builder.ToImmutable());
        }
    }

    public ValueTask<ImmutableArray<string>> GetRedirectUrisAsync(OpenIddictApplicationModel application,
        CancellationToken cancellationToken)
    {
        Check.NotNull(application, nameof(application));

        if (string.IsNullOrEmpty(application.RedirectUris))
        {
            return new ValueTask<ImmutableArray<string>>(ImmutableArray.Create<string>());
        }

        using (var document = JsonDocument.Parse(application.RedirectUris))
        {
            var builder = ImmutableArray.CreateBuilder<string>(document.RootElement.GetArrayLength());

            foreach (var element in document.RootElement.EnumerateArray())
            {
                var value = element.GetString();
                if (string.IsNullOrEmpty(value))
                {
                    continue;
                }

                builder.Add(value);
            }

            return new ValueTask<ImmutableArray<string>>(builder.ToImmutable());
        }
    }

    public ValueTask<ImmutableArray<string>> GetRequirementsAsync(OpenIddictApplicationModel application,
        CancellationToken cancellationToken)
    {
        Check.NotNull(application, nameof(application));

        if (string.IsNullOrEmpty(application.Requirements))
        {
            return new ValueTask<ImmutableArray<string>>(ImmutableArray.Create<string>());
        }

        using (var document = JsonDocument.Parse(application.Requirements))
        {
            var builder = ImmutableArray.CreateBuilder<string>(document.RootElement.GetArrayLength());

            foreach (var element in document.RootElement.EnumerateArray())
            {
                var value = element.GetString();
                if (string.IsNullOrEmpty(value))
                {
                    continue;
                }

                builder.Add(value);
            }

            return new ValueTask<ImmutableArray<string>>(builder.ToImmutable());
        }
    }

    public ValueTask<OpenIddictApplicationModel> InstantiateAsync(CancellationToken cancellationToken)
    {
        return new ValueTask<OpenIddictApplicationModel>(new OpenIddictApplicationModel
        {
            Id = this.GuidGenerator.Create()
        });
    }

    public async IAsyncEnumerable<OpenIddictApplicationModel> ListAsync(int? count, int? offset,
        [EnumeratorCancellation] CancellationToken cancellationToken)
    {
        var applications = await this.UnitOfWorkManager.WithUnitOfWorkAsync(async () =>
            await this.Repository.ListAsync(count, offset, cancellationToken)
        );

        foreach (var application in applications)
        {
            yield return application.ToModel();
        }
    }

    public IAsyncEnumerable<TResult> ListAsync<TState, TResult>(
        Func<IQueryable<OpenIddictApplicationModel>, TState, IQueryable<TResult>> query, TState state,
        CancellationToken cancellationToken)
    {
        throw new NotSupportedException();
    }

    public virtual ValueTask SetClientIdAsync(OpenIddictApplicationModel application, string identifier,
        CancellationToken cancellationToken)
    {
        Check.NotNull(application, nameof(application));

        application.ClientId = identifier;
        return default;
    }

    public virtual ValueTask SetClientSecretAsync(OpenIddictApplicationModel application, string secret,
        CancellationToken cancellationToken)
    {
        Check.NotNull(application, nameof(application));

        application.ClientSecret = secret;
        return default;
    }

    public virtual ValueTask SetClientTypeAsync(OpenIddictApplicationModel application, string type,
        CancellationToken cancellationToken)
    {
        Check.NotNull(application, nameof(application));

        application.Type = type;
        return default;
    }

    public virtual ValueTask SetConsentTypeAsync(OpenIddictApplicationModel application, string type,
        CancellationToken cancellationToken)
    {
        Check.NotNull(application, nameof(application));

        application.ConsentType = type;
        return default;
    }

    public virtual ValueTask SetDisplayNameAsync(OpenIddictApplicationModel application, string name,
        CancellationToken cancellationToken)
    {
        Check.NotNull(application, nameof(application));

        application.DisplayName = name;
        return default;
    }

    public virtual ValueTask SetDisplayNamesAsync(OpenIddictApplicationModel application,
        ImmutableDictionary<CultureInfo, string> names, CancellationToken cancellationToken)
    {
        Check.NotNull(application, nameof(application));

        if (names is null || names.IsEmpty)
        {
            application.DisplayNames = null;
            return default;
        }

        application.DisplayNames = this.WriteStream(writer =>
        {
            writer.WriteStartObject();
            foreach (var pair in names)
            {
                writer.WritePropertyName(pair.Key.Name);
                writer.WriteStringValue(pair.Value);
            }

            writer.WriteEndObject();
        });

        return default;
    }

    public virtual ValueTask SetPermissionsAsync(OpenIddictApplicationModel application,
        ImmutableArray<string> permissions,
        CancellationToken cancellationToken)
    {
        Check.NotNull(application, nameof(application));

        if (permissions.IsDefaultOrEmpty)
        {
            application.Permissions = null;
            return default;
        }

        application.Permissions = this.WriteStream(writer =>
        {
            writer.WriteStartArray();
            foreach (var permission in permissions)
            {
                writer.WriteStringValue(permission);
            }

            writer.WriteEndArray();
        });

        return default;
    }

    public virtual ValueTask SetPostLogoutRedirectUrisAsync(OpenIddictApplicationModel application,
        ImmutableArray<string> uris,
        CancellationToken cancellationToken)
    {
        Check.NotNull(application, nameof(application));

        if (uris.IsDefaultOrEmpty)
        {
            application.PostLogoutRedirectUris = null;
            return default;
        }

        application.PostLogoutRedirectUris = this.WriteStream(writer =>
        {
            writer.WriteStartArray();
            foreach (var uri in uris)
            {
                writer.WriteStringValue(uri);
            }

            writer.WriteEndArray();
        });

        return default;
    }

    public virtual ValueTask SetPropertiesAsync(OpenIddictApplicationModel application,
        ImmutableDictionary<string, JsonElement> properties,
        CancellationToken cancellationToken)
    {
        Check.NotNull(application, nameof(application));

        if (properties is null || properties.IsEmpty)
        {
            application.Properties = null;
            return default;
        }

        application.Properties = this.WriteStream(writer =>
        {
            writer.WriteStartObject();
            foreach (var property in properties)
            {
                writer.WritePropertyName(property.Key);
                property.Value.WriteTo(writer);
            }

            writer.WriteEndObject();
        });

        return default;
    }

    public virtual ValueTask SetRedirectUrisAsync(OpenIddictApplicationModel application,
        ImmutableArray<string> uris,
        CancellationToken cancellationToken)
    {
        Check.NotNull(application, nameof(application));

        if (uris.IsDefaultOrEmpty)
        {
            application.RedirectUris = null;
            return default;
        }

        application.RedirectUris = this.WriteStream(writer =>
        {
            writer.WriteStartArray();
            foreach (var uri in uris)
            {
                writer.WriteStringValue(uri);
            }

            writer.WriteEndArray();
        });

        return default;
    }

    public virtual ValueTask SetRequirementsAsync(OpenIddictApplicationModel application,
        ImmutableArray<string> requirements,
        CancellationToken cancellationToken)
    {
        Check.NotNull(application, nameof(application));

        if (requirements.IsDefaultOrEmpty)
        {
            application.Requirements = null;
            return default;
        }

        application.Requirements = this.WriteStream(writer =>
        {
            writer.WriteStartArray();
            foreach (var requirement in requirements)
            {
                writer.WriteStringValue(requirement);
            }

            writer.WriteEndArray();
        });

        return default;
    }

    public virtual async ValueTask UpdateAsync(OpenIddictApplicationModel application,
        CancellationToken cancellationToken)
    {
        Check.NotNull(application, nameof(application));

        var entity = await this.UnitOfWorkManager.WithUnitOfWorkAsync(async () =>
            await this.Repository.GetAsync(application.Id)
        );

        try
        {
            await this.UnitOfWorkManager.WithUnitOfWorkAsync(async () =>
            {
                await this.Repository.UpdateAsync(application.ToEntity(entity));
            });
        }
        catch (AbpDbConcurrencyException e)
        {
            this.Logger.LogError(e, e.Message);
            await this.ConcurrencyExceptionHandler.HandleAsync(e);
            throw new OpenIddictExceptions.ConcurrencyException(e.Message, e.InnerException);
        }

        await this.UnitOfWorkManager.WithUnitOfWorkAsync(async () =>
        {
            application =
                (await this.Repository.FindByClientIdAsync(entity.Id.ToString(), cancellationToken: cancellationToken))
                .ToModel();
        });
    }

    public virtual ValueTask<string> GetClientUriAsync(OpenIddictApplicationModel application,
        CancellationToken cancellationToken = default)
    {
        Check.NotNull(application, nameof(application));

        return new ValueTask<string>(application.ClientUri);
    }

    public virtual ValueTask<string> GetLogoUriAsync(OpenIddictApplicationModel application,
        CancellationToken cancellationToken = default)
    {
        Check.NotNull(application, nameof(application));

        return new ValueTask<string>(application.LogoUri);
    }
}