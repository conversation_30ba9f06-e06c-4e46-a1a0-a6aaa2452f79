using Abp.Runtime.Caching;

namespace RealtoCrm.DashboardCustomization.Definitions.Cache;

public class DashboardDefinitionCacheManager(ICacheManager cacheManager) : IDashboardDefinitionCacheManager
{
    private const string DashboardDefinitionsCacheName = "DashboardDefinitionsCacheName";

    private readonly ITypedCache<string, DashboardDefinition> cache = cacheManager.GetCache<string, DashboardDefinition>(DashboardDefinitionsCacheName);

    public DashboardDefinition Get(string name)
    {
        return this.cache.GetOrDefault(name);
    }

    public void Set(DashboardDefinition definition)
    {
        this.cache.Set(definition.Name, definition);
    }
}