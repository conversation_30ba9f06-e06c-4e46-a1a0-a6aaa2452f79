using Abp.Dependency;
using Abp.Extensions;
using Microsoft.Extensions.Configuration;
using RealtoCrm.Configuration;

namespace RealtoCrm.Net.Sms;

public class TwilioSmsSenderConfiguration(IAppConfigurationAccessor configurationAccessor) : ITransientDependency
{
    private readonly IConfigurationRoot appConfiguration = configurationAccessor.Configuration;

    public string AccountSid => this.appConfiguration["Twilio:AccountSid"];

    public string AuthToken => this.appConfiguration["Twilio:AuthToken"];

    public string SenderNumber => this.appConfiguration["Twilio:SenderNumber"];
}