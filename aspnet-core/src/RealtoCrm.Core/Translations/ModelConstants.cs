namespace RealtoCrm.Translations;

public static class ModelConstants
{
    public static class Common
    {
        public const int MaxNameLength = 250;
    }
    
    public static class OfferTranslation
    {
        public const int MaxTitleLength = 250;
        
        public const int MaxDescriptionLength = 64000;
        public const int MaxDistributionLength = 64000;
        public const int MaxAdvantagesLength = 64000;
        public const int MaxLandmarkLength = 64000;
        public const int MaxParkingLength = 64000;
        public const int MaxLocationLength = 64000;
        public const int MaxAdditionalInformationLength = 64000;
    }
}