using System.Threading.Tasks;
using Abp.Configuration;
using Abp.Dependency;
using Abp.Zero.Configuration;

namespace RealtoCrm.Security;

public class PasswordComplexitySettingStore(ISettingManager settingManager) : IPasswordComplexitySettingStore, ITransientDependency
{
    public async Task<PasswordComplexitySetting> GetSettingsAsync()
    {
        return new PasswordComplexitySetting
        {
            RequireDigit = await settingManager.GetSettingValueAsync<bool>(AbpZeroSettingNames.UserManagement.PasswordComplexity.RequireDigit),
            RequireLowercase = await settingManager.GetSettingValueAsync<bool>(AbpZeroSettingNames.UserManagement.PasswordComplexity.RequireLowercase),
            RequireNonAlphanumeric = await settingManager.GetSettingValueAsync<bool>(AbpZeroSettingNames.UserManagement.PasswordComplexity.RequireNonAlphanumeric),
            RequireUppercase = await settingManager.GetSettingValueAsync<bool>(AbpZeroSettingNames.UserManagement.PasswordComplexity.RequireUppercase),
            RequiredLength = await settingManager.GetSettingValueAsync<int>(AbpZeroSettingNames.UserManagement.PasswordComplexity.RequiredLength)
        };
    }
}