namespace RealtoCrm.Calls;

using System;
using Abp.Domain.Entities.Auditing;
using Clients;
using Employees;
using MultiTenancy;
using MultiTenancy.Interfaces;

public class Call : FullAuditedEntity<int>, IHaveTenant
{
    public DateTime Start { get; set; }

    public int? ClientId { get; set; }

    public Client? Client { get; set; }

    public int TenantId { get; set; }

    public Tenant Tenant { get; set; } = default!;

    public int ConsultantId { get; set; }

    public CallCategory Category { get; set; }

    public Employee Consultant { get; set; } = default!;

    public Guid SalestrailUserId { get; set; }

    public string UserName { get; set; } = string.Empty;

    public string UserEmail { get; set; } = string.Empty;

    public string UserPhone { get; set; } = string.Empty;

    public Guid CallId { get; set; }

    public string Source { get; set; } = string.Empty;

    public string SourceDetail { get; set; } = string.Empty;

    public int Duration { get; set; }

    public bool Answered { get; set; }

    public bool Inbound { get; set; }

    public string Number { get; set; } = string.Empty;

    public string FormattedNumber { get; set; } = string.Empty;

    public string PhoneBookName { get; set; } = string.Empty;
}