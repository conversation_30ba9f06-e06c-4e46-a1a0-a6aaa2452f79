namespace RealtoCrm.Money;

using System.Collections.Generic;
using Abp.Domain.Values;

public class Money : ValueObject
{
    public decimal Amount { get; set; }

    public Currency Currency { get; set; }

    public static Money Zero(Currency currency = Currency.EUR)
        => new()
        {
            Amount = decimal.Zero,
            Currency = currency,
        };

    protected override IEnumerable<object> GetAtomicValues()
    {
        yield return this.Amount;
        yield return this.Currency;
    }
}