namespace RealtoCrm.PropertyToEstateCategorySettings.Models;

using Common.Models;using RealtoCrm.Mapping;


public class PropertyToEstateCategorySettingResponseModel : BaseCharacteristicsModel, IMapFrom<PropertyToEstateCategorySetting>
{
    public string PropertyName { get; set; } = default!;

    public PropertyToEstateCategoryModel Model { get; set; }

    public int CategoryId { get; set; } = default!;
    
    public string CategoryName { get; set; } = default!;

    public int TenantId { get; set; }

    public string TenantName { get; set; } = default!;
    
    public RequiredSettingForOfferStatus RequiredForOfferStatus { get; set; }
}