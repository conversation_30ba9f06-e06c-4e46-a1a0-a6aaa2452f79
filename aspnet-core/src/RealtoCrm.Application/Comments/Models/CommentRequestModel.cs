namespace RealtoCrm.Comments.Models;

using System.ComponentModel.DataAnnotations;
using Mapping;
using static Comments.ModelConstants.Comment;

public class CommentRequestModel : IMapTo<Comment>
{
    [Required]
    [MinLength(MinTextLength)]
    [MaxLength(MaxTextLength)]
    public string Text { get; init; } = default!;

    public bool IsPrivate { get; init; }

    public int? EmployeeId { get; set; }
}