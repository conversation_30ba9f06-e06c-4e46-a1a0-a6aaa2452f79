namespace RealtoCrm.Comments;

using System.Linq;
using Abp.Domain.Entities;
using Abp.Domain.Entities.Auditing;
using Abp.EntityFrameworkCore;
using Abp.Runtime.Session;
using Abp.UI;
using Authorization;
using Common.Attributes;
using DataCrudModels;
using Employees;
using EntityFrameworkCore;
using Mapping;
using Microsoft.EntityFrameworkCore;
using Models;
using Expressions;
using Task = System.Threading.Tasks.Task;

public abstract class CommentsAppService<
    TPrimaryKey,
    TEntity,
    TEntityComment,
    TCreateRequestModel,
    TUpdateRequestModel,
    TPaginatedRequestModel,
    TDetailsResponseModel,
    TListingResponseModel>(
    IEmployeesAppService employeesAppService,
    IDbContextProvider<RealtoCrmDbContext> dbContextProvider,
    IExpressionsBuilder expressionsBuilder)
    : DataCrudAppService<
            TPrimaryKey,
            TEntity,
            TCreateRequestModel,
            TUpdateRequestModel,
            TPaginatedRequestModel,
            TDetailsResponseModel,
            TListingResponseModel>(dbContextProvider, expressionsBuilder),
        ICommentsAppService<
            TPrimaryKey,
            TEntity,
            TEntityComment,
            TCreateRequestModel,
            TUpdateRequestModel,
            TPaginatedRequestModel,
            TDetailsResponseModel,
            TListingResponseModel>
    where TPrimaryKey : struct
    where TEntity : FullAuditedEntity<TPrimaryKey>, IAddComment
    where TEntityComment : class, IHaveComment
    where TCreateRequestModel : class, IMapTo<TEntity>
    where TUpdateRequestModel : class, IMapTo<TEntity>
    where TPaginatedRequestModel : PaginatedRequestModel, new()
    where TDetailsResponseModel : class, IMapFrom<TEntity>
    where TListingResponseModel : class, IMapFrom<TEntity>
{
    private const string NotCommentOwner = "You are not the owner of this comment.";

    protected IEmployeesAppService EmployeesAppService { get; } = employeesAppService;

    protected override ActionAttributeConfiguration AttributeConfiguration
        => new ActionAttributeConfiguration()
            .ConfigureAuthorize(nameof(this.AddCommentAsync), AppPermissions.NoteCreate)
            .AddCustomAttribute(nameof(this.AddCommentAsync), new RequireTenantAttribute())
            .ConfigureAuthorize(nameof(this.UpdateCommentAsync), AppPermissions.NoteUpdate)
            .AddCustomAttribute(nameof(this.UpdateCommentAsync), new RequireTenantAttribute())
            .ConfigureAuthorize(nameof(this.DeleteAsync), AppPermissions.NoteDelete)
            .AddCustomAttribute(nameof(this.DeleteAsync), new RequireTenantAttribute());

    public virtual async Task AddCommentAsync(TPrimaryKey id, CommentRequestModel request)
    {
        var entity = await this.GetAsync(id);

        if (entity is null)
        {
            throw new EntityNotFoundException(typeof(TEntity), id);
        }

        request.EmployeeId = await this.EmployeesAppService.GetIdOrDefaultAsync(this.AbpSession.GetUserId());

        var comment = this.ObjectMapper.Map<Comment>(request);

        entity.AddComment(comment);

        await this.Data.SaveChangesAsync();
    }

    public virtual async Task UpdateCommentAsync(int commentId, CommentRequestModel request)
    {
        var comment = await this.Data
            .Comments
            .Where(c => c.Id == commentId)
            .FirstOrDefaultAsync();

        if (comment is null)
        {
            throw new EntityNotFoundException(typeof(Comment), commentId);
        }

        var employeeId = await this.EmployeesAppService.GetIdOrDefaultAsync(this.AbpSession.GetUserId());

        if (comment.EmployeeId != employeeId)
        {
            throw new UserFriendlyException(NotCommentOwner);
        }

        this.ObjectMapper.Map(request, comment);

        await this.Data.SaveChangesAsync();
    }

    public virtual async Task DeleteCommentAsync(int commentId)
    {
        var entityComment = await this.Data
            .Set<TEntityComment>()
            .Where(ec => ec.CommentId == commentId)
            .Include(ec => ec.Comment)
            .FirstOrDefaultAsync();

        if (entityComment is null)
        {
            throw new EntityNotFoundException(typeof(TEntityComment), commentId);
        }

        var employeeId = await this.EmployeesAppService.GetIdOrDefaultAsync(this.AbpSession.GetUserId());

        if (entityComment.Comment.EmployeeId != employeeId)
        {
            throw new UserFriendlyException(NotCommentOwner);
        }

        this.Data.Comments.Remove(entityComment.Comment);
        this.Data.Set<TEntityComment>().Remove(entityComment);

        await this.Data.SaveChangesAsync();
    }
}