namespace RealtoCrm.Nomenclatures.PopulatedPlaces;

using Common;
using Models;
using System.Threading.Tasks;

public interface IPopulatedPlacesAppService : INomenclaturesAppService<
    int,
    PopulatedPlace,
    PopulatedPlaceRequestModel,
    PopulatedPlaceRequestModel,
    PopulatedPlacesPaginatedRequestModel,
    PopulatedPlaceResponseModel,
    PopulatedPlaceResponseModel>
{
    Task<PopulatedPlaceToOfferModel?> GetPopulatedPlaceInfoAsync(int id);
    
    Task<MunicipalityAndProvinceId> GetMunicipalityAndProvinceIdByPopulatedPlaceIdAsync(int id);
}

