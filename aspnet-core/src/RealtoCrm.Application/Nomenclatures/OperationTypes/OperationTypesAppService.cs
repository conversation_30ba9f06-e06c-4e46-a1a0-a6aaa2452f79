namespace RealtoCrm.Nomenclatures.OperationTypes;

using Abp.EntityFrameworkCore;
using Common;
using Common.Models;
using DataExporting;
using EntityFrameworkCore;
using Expressions;

public class OperationTypesAppService(
    IExcelService excelService,
    IDbContextProvider<RealtoCrmDbContext> dbContextProvider,
    IExpressionsBuilder expressionsBuilder) : NomenclaturesAppService<
    int,
    OperationType,
    NomenclatureRequestModel,
    NomenclatureRequestModel,
    NomenclaturesPaginatedRequestModel,
    NomenclatureResponseModel,
    NomenclatureResponseModel>(excelService, dbContextProvider, expressionsBuilder), IOperationTypesAppService;