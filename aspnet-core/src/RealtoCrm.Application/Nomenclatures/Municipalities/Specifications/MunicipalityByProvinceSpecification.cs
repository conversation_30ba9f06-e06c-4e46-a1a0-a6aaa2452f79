namespace RealtoCrm.Nomenclatures.Municipalities.Specifications;

using System;
using System.Linq.Expressions;

public class MunicipalityByProvinceSpecification : Specification<Municipality>
{
    private readonly int? provinceId;

    public MunicipalityByProvinceSpecification(int? provinceId) => this.provinceId = provinceId;

    protected override bool Include => this.provinceId is not null;

    public override Expression<Func<Municipality, bool>> ToExpression()
        => municipality => municipality.ProvinceId == this.provinceId;
}