namespace RealtoCrm.Nomenclatures.Municipalities;

using Abp.EntityFrameworkCore;
using Common;
using DataExporting;
using EntityFrameworkCore;
using Models;
using Expressions;
using Specifications;
using System.Collections.Generic;
using System.Linq.Expressions;
using Authorization;
using RealtoCrm.Common.Attributes;

public class MunicipalitiesAppService(
    IExcelService excelService,
    IDbContextProvider<RealtoCrmDbContext> dbContextProvider,
    IExpressionsBuilder expressionsBuilder) : NomenclaturesAppService<
    int,
    Municipality,
    MunicipalityRequestModel,
    MunicipalityRequestModel,
    MunicipalitiesPaginatedRequestModel,
    MunicipalityResponseModel,
    MunicipalityResponseModel>(excelService, dbContextProvider, expressionsBuilder), IMunicipalitiesAppService
{
    protected override Dictionary<string, FilterExpression> CustomFilters
        => new()
        {
            [nameof(MunicipalityResponseModel.ProvinceName)]
                = new FilterExpression(
                    typeof(Province).GetProperty(nameof(Province.Name))!,
                    Expression.PropertyOrField(
                        Expression.PropertyOrField(this.Parameter, nameof(Municipality.Province)),
                        nameof(Province.Name))),
        };

    protected override Specification<Municipality> GetSpecification(MunicipalitiesPaginatedRequestModel request)
        => base.GetSpecification(request)
            .And(new MunicipalityByProvinceSpecification(request.ProvinceId));

    protected override ActionAttributeConfiguration AttributeConfiguration
        => new ActionAttributeConfiguration()
            .ConfigureAuthorize(nameof(this.CreateAsync), AppPermissions.AdministrativeDivisionsCreate)
            .ConfigureAuthorize(nameof(this.UpdateAsync), AppPermissions.AdministrativeDivisionsUpdate)
            .ConfigureAuthorize(nameof(this.DeleteAsync), AppPermissions.AdministrativeDivisionsDelete)
            .ConfigureAuthorize(nameof(this.GetDetailsAsync), AppPermissions.AdministrativeDivisionsRead);
}