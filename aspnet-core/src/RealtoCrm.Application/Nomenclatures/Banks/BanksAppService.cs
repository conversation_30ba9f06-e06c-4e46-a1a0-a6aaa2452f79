namespace RealtoCrm.Nomenclatures.Banks;

using Abp.EntityFrameworkCore;
using Common;
using Common.Models;
using DataExporting;
using EntityFrameworkCore;
using Expressions;

public class BanksAppService(
    IExcelService excelService,
    IDbContextProvider<RealtoCrmDbContext> dbContextProvider,
    IExpressionsBuilder expressionsBuilder)
    : NomenclaturesAppService<
        int,
        Bank,
        NomenclatureRequestModel,
        NomenclatureRequestModel,
        NomenclaturesPaginatedRequestModel,
        NomenclatureResponseModel,
        NomenclatureResponseModel>(excelService, dbContextProvider, expressionsBuilder), IBanksAppService;