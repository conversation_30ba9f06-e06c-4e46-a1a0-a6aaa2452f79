namespace RealtoCrm.Nomenclatures.Workplaces;

using Common;
using Common.Models;
using Models;
using System.Collections.Generic;
using System.Threading.Tasks;

public interface IWorkplacesAppService : INomenclaturesAppService<
    int,
    Workplace,
    WorkplaceRequestModel,
    WorkplaceRequestModel,
    NomenclaturesPaginatedRequestModel,
    WorkplaceResponseModel,
    WorkplaceResponseModel>
{
    Task<IEnumerable<NomenclatureResponseModel>> GetAllWithoutPagination();
}