namespace RealtoCrm.Nomenclatures.DealTypes;

using Abp.EntityFrameworkCore;
using Common;
using Common.Models;
using DataExporting;
using EntityFrameworkCore;
using Expressions;

public class DealTypesAppService(
    IExcelService excelService,
    IDbContextProvider<RealtoCrmDbContext> dbContextProvider,
    IExpressionsBuilder expressionsBuilder) : NomenclaturesAppService<
    int,
    DealType,
    NomenclatureRequestModel,
    NomenclatureRequestModel,
    NomenclaturesPaginatedRequestModel,
    NomenclatureResponseModel,
    NomenclatureResponseModel>(excelService, dbContextProvider, expressionsBuilder), IDealTypesAppService;