namespace RealtoCrm.Nomenclatures.Furnitures;

using Abp.EntityFrameworkCore;
using Common;
using Common.Models;
using DataExporting;
using EntityFrameworkCore;
using Expressions;

public class FurnitureAppService(
    IExcelService excelService,
    IDbContextProvider<RealtoCrmDbContext> dbContextProvider,
    IExpressionsBuilder expressionsBuilder) : NomenclaturesAppService<
    int,
    Furniture,
    NomenclatureRequestModel,
    NomenclatureRequestModel,
    NomenclaturesPaginatedRequestModel,
    NomenclatureResponseModel,
    NomenclatureResponseModel>(excelService, dbContextProvider, expressionsBuilder), IFurnitureAppService;