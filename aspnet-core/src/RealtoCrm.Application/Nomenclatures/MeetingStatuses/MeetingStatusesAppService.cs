namespace RealtoCrm.Nomenclatures.MeetingStatuses;

using Abp.EntityFrameworkCore;
using RealtoCrm.DataExporting;
using RealtoCrm.EntityFrameworkCore;
using RealtoCrm.Expressions;
using RealtoCrm.Nomenclatures.Common;
using RealtoCrm.Nomenclatures.Common.Models;

public class MeetingStatusesAppService(
    IExcelService excelService,
    IDbContextProvider<RealtoCrmDbContext> dbContextProvider,
    IExpressionsBuilder expressionsBuilder) : NomenclaturesAppService<
    int,
    MeetingStatus,
    NomenclatureRequestModel,
    NomenclatureRequestModel,
    NomenclaturesPaginatedRequestModel,
    NomenclatureResponseModel,
    NomenclatureResponseModel>(excelService, dbContextProvider, expressionsBuilder), IMeetingStatusesAppService;