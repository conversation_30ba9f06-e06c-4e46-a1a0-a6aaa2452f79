namespace RealtoCrm.Nomenclatures.ImageCategories;

using System.Threading.Tasks;
using DataCrudModels;
using Images;
using Models;

public interface IImageCategoriesAppService : IDataCrudAppService<
    int,
    ImageCategory,
    ImageCategoryRequestModel,
    ImageCategoryRequestModel,
    PaginatedRequestModel,
    ImageCategoryResponseModel,
    ImageCategoryResponseModel>
{
    Task<int> GetIdByNameAsync(string name);
}