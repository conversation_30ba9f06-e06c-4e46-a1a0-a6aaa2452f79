namespace RealtoCrm.Nomenclatures.ImageCategories;

using System.Linq;
using System.Threading.Tasks;
using Abp.EntityFrameworkCore;
using DataCrudModels;
using EntityFrameworkCore;
using Expressions;
using Images;
using Microsoft.EntityFrameworkCore;
using Models;

public class ImageCategoriesAppService(
    IDbContextProvider<RealtoCrmDbContext> dbContextProvider,
    IExpressionsBuilder expressionsBuilder)
    : DataCrudAppService<
        int,
        ImageCategory,
        ImageCategoryRequestModel,
        ImageCategoryRequestModel,
        PaginatedRequestModel,
        ImageCategoryResponseModel,
        ImageCategoryResponseModel>(dbContextProvider, expressionsBuilder), IImageCategoriesAppService
{
    public async Task<int> GetIdByNameAsync(string name)
        => await this
            .AllAsNoTracking()
            .Where(ic => ic.Name == name)
            .Select(ic => ic.Id)
            .FirstAsync();
}