namespace RealtoCrm.Deals.Specifications;

using System;
using System.Linq.Expressions;

public class DealWithoutAnnulledSpecification(int? status, bool? withoutAnnulled) : Specification<Deal>
{
    protected override bool Include
        => withoutAnnulled.HasValue && status is not (int)DealStatus.IsAnnulled;

    public override Expression<Func<Deal, bool>> ToExpression()
        => deal => withoutAnnulled!.Value
            ? deal.DealStatus != DealStatus.IsAnnulled
            : true;
}