namespace RealtoCrm.Deals.Models;

using System.Collections.Generic;
using Mapping;
using Money;

public class DealParticipationRequestModel : IMapTo<DealParticipation>
{
    public int? Id { get; init; }

    public DealParticipationSide Side { get; init; }

    public DealPaymentType? PaymentType { get; init; }

    public int? ExternalBrokerId { get; init; }

    public decimal? ExternalBrokerPercentage { get; init; }

    public int? RecommendationEmployeeId { get; init; }

    public decimal? RecommendationPercentage { get; init; }

    public decimal? PercentageOnContract { get; init; }

    public decimal? PercentageOnNotary { get; init; }

    public decimal? CommissionOnContractPercentage { get; init; }

    public MoneyRequestModel? Commission { get; init; }

    public int TenantId { get; set; }

    public ICollection<DealParticipantRequestModel> DealParticipants { get; init; } = [];

    public ICollection<DealParticipationCommentRequestModel> DealParticipationsComments { get; init; } = [];
}