namespace RealtoCrm.Deals.Models;

using System.Collections.Generic;

public class DealCreateRequestModel : DealUpdateRequestModel
{
    public DealStatus DealStatus { get; set; }

    public int? VatId { get; set; }

    public int? EmployeeId { get; set; }

    public int? OppositeSideEmployeeId { get; set; }

    public int? OfferId { get; init; }

    public int? SearchId { get; init; }

    public int? SearchClientId { get; set; }

    public int? OfferClientId { get; set; }

    public int? SearchContractTypeId { get; set; }

    public int? OfferContractTypeId { get; set; }

    public int? ExternalSearchId { get; init; }

    public int? ExternalOfferId { get; init; }

    public ICollection<DealTenantRequestModel> DealTenants { get; init; } = [];
}