namespace RealtoCrm.EstateCategories.Specifications;

using System;
using System.Linq.Expressions;
using RealtoCrm.Estates;

public class EstateCategoryWithParentSpecification : Specification<EstateCategory>
{
    private readonly bool? withParent;

    public EstateCategoryWithParentSpecification(bool? withParent) => this.withParent = withParent;

    protected override bool Include => this.withParent.HasValue;
    public override Expression<Func<EstateCategory, bool>> ToExpression()
    {
        if (this.withParent!.Value)
        {
            return estateCategory => estateCategory.ParentId.HasValue;
        }
        
        return estateCategory => !estateCategory.ParentId.HasValue;
    }
}