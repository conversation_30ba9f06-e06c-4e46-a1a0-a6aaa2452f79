namespace RealtoCrm.EstateCategories.Specifications;

using System;
using System.Linq.Expressions;
using RealtoCrm.Estates;

public class EstateCategoryByParentSpecification : Specification<EstateCategory>
{
    private readonly int? parentId;

    public EstateCategoryByParentSpecification(int? parentId) => this.parentId = parentId;
    
    protected override bool Include => this.parentId.HasValue;

    public override Expression<Func<EstateCategory, bool>> ToExpression() 
        => estateCategory => estateCategory.ParentId == this.parentId;


}