using System.Collections.Generic;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.DynamicEntityProperties;
using Abp.UI.Inputs;
using RealtoCrm.Authorization;
using RealtoCrm.DynamicEntityProperties.Dto;

namespace RealtoCrm.DynamicEntityProperties;

[AbpAuthorize]
public class DynamicPropertyAppService(
    IDynamicPropertyManager dynamicPropertyManager,
    IDynamicPropertyStore dynamicPropertyStore,
    IDynamicEntityPropertyDefinitionManager dynamicEntityPropertyDefinitionManager)
    : RealtoCrmAppServiceBase, IDynamicPropertyAppService
{
    public async Task<DynamicPropertyDto> Get(int id)
    {
        var entity = await dynamicPropertyManager.GetAsync(id);
        return this.ObjectMapper.Map<DynamicPropertyDto>(entity);
    }

    public async Task<ListResultDto<DynamicPropertyDto>> GetAll()
    {
        var entities = await dynamicPropertyStore.GetAllAsync();

        return new ListResultDto<DynamicPropertyDto>(this.ObjectMapper.Map<List<DynamicPropertyDto>>(entities)
        );
    }

    public async Task Add(DynamicPropertyDto dto)
    {
        dto.TenantId = this.AbpSession.TenantId;
        await dynamicPropertyManager.AddAsync(this.ObjectMapper.Map<DynamicProperty>(dto));
    }

    public async Task Update(DynamicPropertyDto dto)
    {
        dto.TenantId = this.AbpSession.TenantId;
        await dynamicPropertyManager.UpdateAsync(this.ObjectMapper.Map<DynamicProperty>(dto));
    }

    public async Task Delete(int id)
    {
        await dynamicPropertyManager.DeleteAsync(id);
    }

    public IInputType FindAllowedInputType(string name)
    {
        return dynamicEntityPropertyDefinitionManager.GetOrNullAllowedInputType(name);
    }
}