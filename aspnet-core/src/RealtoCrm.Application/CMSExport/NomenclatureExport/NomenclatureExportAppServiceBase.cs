using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Domain.Entities;
using Abp.EntityFrameworkCore;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using RealtoCrm.EntityFrameworkCore;

namespace RealtoCrm.CMSExport.NomenclatureExport;

public abstract class NomenclatureExportAppServiceBase<TEntity, TModel>(
    IDbContextProvider<RealtoCrmDbContext> dbContextProvider,
    IHttpContextAccessor accessor
)
    : RealtoCrmAppServiceBase
    where TEntity : class, IEntity<int>
    where TModel : class
{
    
    private const string ExpectedBearerToken = "Bearer 7d9859d2-44c1-4c6f-9954-5cc2f7c47a76";
    
    public async Task<List<TModel>> GetLatestAsync()
    {
        AuthorizeRequest();

        return await this.ObjectMapper
            .ProjectTo<TModel>(DbSet.OrderBy(x => x.Id))
            .ToListAsync();
    }

    protected void AuthorizeRequest()
    {
        var hasHeader = accessor.HttpContext.Request.Headers.TryGetValue("Authorization", out var token);

        if (!hasHeader || token != ExpectedBearerToken)
        {
            throw new Abp.Authorization.AbpAuthorizationException("Unauthorized");
        }
    }

    protected virtual IQueryable<TEntity> DbSet =>
        dbContextProvider.GetDbContext().Set<TEntity>().AsNoTracking();

}