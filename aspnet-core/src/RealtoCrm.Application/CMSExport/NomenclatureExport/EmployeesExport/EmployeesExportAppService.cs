using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.EntityFrameworkCore;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using RealtoCrm.CMSExport.NomenclatureExport.EmployeesExport.Models;
using RealtoCrm.Employees;
using RealtoCrm.EntityFrameworkCore;

namespace RealtoCrm.CMSExport.NomenclatureExport.EmployeesExport;

public class EmployeesExportAppService(
    IDbContextProvider<RealtoCrmDbContext> dbContextProvider,
    IHttpContextAccessor accessor
)
    : NomenclatureExportAppServiceBase<Employee, EmployeesExportModel>(dbContextProvider, accessor)
{

    public async Task<List<EmployeesExportModel>> GetEmployeesByTenantAsync(EmployeesExportRequestModel input)
    {
        AuthorizeRequest();

        var query = DbSet.Where(e => e.UserAccount.TenantId == input.TenantId);

        return await this.ObjectMapper
            .ProjectTo<EmployeesExportModel>(query)
            .OrderBy(x => x.Id)
            .ToListAsync();
    }
}