using Abp.EntityFrameworkCore;
using Microsoft.AspNetCore.Http;
using RealtoCrm.CMSExport.NomenclatureExport.ProvincesExport.Models;
using RealtoCrm.EntityFrameworkCore;
using RealtoCrm.Nomenclatures;

namespace RealtoCrm.CMSExport.NomenclatureExport.ProvincesExport;

public class ProvincesExportAppService(
    IDbContextProvider<RealtoCrmDbContext> dbContextProvider,
    IHttpContextAccessor accessor
)
    : NomenclatureExportAppServiceBase<Province, ProvinceExportModel>(dbContextProvider, accessor)
{

}