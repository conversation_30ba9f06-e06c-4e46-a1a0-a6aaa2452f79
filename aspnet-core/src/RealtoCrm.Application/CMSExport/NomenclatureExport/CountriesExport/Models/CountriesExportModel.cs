using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using RealtoCrm.Mapping;
using RealtoCrm.Nomenclatures;

namespace RealtoCrm.CMSExport.NomenclatureExport.CountriesExport.Models;

public class CountriesExportModel : IMapFrom<Country>, IMapExplicitly
{
    public int Id { get; set; }
    
    public string Name { get; set; } = default!;
    
    public string CodeAlpha2 { get; set; } = default!;
    
    public string CodeAlpha3 { get; set; } = default!;
    
    public List<TranslationExportModel>? Translations { get; set; }
    public void RegisterMappings(IProfileExpression mapper)
    {
        mapper.CreateMap<Country, CountriesExportModel>()
            .ForMember(dest => dest.Translations,
                opt =>
                {
                    opt.Condition(src => src.Translations != null && src.Translations.Count > 0);
                    opt.MapFrom(src => src.Translations.Select(t => new TranslationExportModel
                    {
                        LanguageId = t.LanguageId,
                        Name = t.Name
                    }));
                });
    }
}