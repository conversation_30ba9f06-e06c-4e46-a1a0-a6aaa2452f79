using Abp.EntityFrameworkCore;
using Microsoft.AspNetCore.Http;
using RealtoCrm.CMSExport.NomenclatureExport.MunicipalitiesExport.Models;
using RealtoCrm.EntityFrameworkCore;
using RealtoCrm.Nomenclatures;

namespace RealtoCrm.CMSExport.NomenclatureExport.MunicipalitiesExport;

public class MunicipalitiesExportAppService(
    IDbContextProvider<RealtoCrmDbContext> dbContextProvider,
    IHttpContextAccessor accessor
)
    : NomenclatureExportAppServiceBase<Municipality, MunicipalitiesExportModel>(dbContextProvider, accessor)
{
    
}