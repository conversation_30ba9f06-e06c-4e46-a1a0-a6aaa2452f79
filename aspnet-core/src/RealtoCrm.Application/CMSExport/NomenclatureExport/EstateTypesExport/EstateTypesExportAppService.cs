using Abp.EntityFrameworkCore;
using Microsoft.AspNetCore.Http;
using RealtoCrm.CMSExport.NomenclatureExport.EstateTypesExport.Models;
using RealtoCrm.EntityFrameworkCore;
using RealtoCrm.Estates;

namespace RealtoCrm.CMSExport.NomenclatureExport.EstateTypesExport;

public class EstateTypesExportAppService(
    IDbContextProvider<RealtoCrmDbContext> dbContextProvider,
    IHttpContextAccessor accessor
)
    : NomenclatureExportAppServiceBase<EstateType, EstateTypeExportModel>(dbContextProvider, accessor)
{
    
}