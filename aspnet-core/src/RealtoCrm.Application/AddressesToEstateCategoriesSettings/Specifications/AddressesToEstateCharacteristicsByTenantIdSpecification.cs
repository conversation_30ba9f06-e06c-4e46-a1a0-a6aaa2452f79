namespace RealtoCrm.AddressesToEstateCategoriesSettings.Specifications;

using System;
using System.Linq.Expressions;
using AddressesEstateCharacteristics;
using EntityFrameworkCore.Migrations;

public class AddressesToEstateCharacteristicsByTenantIdSpecification(int tenantId)
    : Specification<AddressToEstateCategoryCharacteristic>
{
    protected override bool Include => tenantId > 0;
    public override Expression<Func<AddressToEstateCategoryCharacteristic, bool>> ToExpression()
        => addressToEstateCharacteristic => addressToEstateCharacteristic.TenantId == tenantId;
}