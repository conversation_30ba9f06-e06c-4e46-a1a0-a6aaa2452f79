namespace RealtoCrm.Tasks;

using System.Collections.Generic;
using Abp.Domain.Entities.Auditing;
using DataCrudModels;
using Mapping;
using Models;

public interface ITasksAppService<
    TPrimaryKey,
    TEntity,
    TEntityTask,
    in TCreateRequestModel,
    in TUpdateRequestModel,
    in TPaginatedRequestModel,
    TDetailsResponseModel,
    TListingResponseModel>
    : IDataCrudAppService<
        TPrimaryKey,
        TEntity,
        TCreateRequestModel,
        TUpdateRequestModel,
        TPaginatedRequestModel,
        TDetailsResponseModel,
        TListingResponseModel>
    where TPrimaryKey : struct
    where TEntity : FullAuditedEntity<TPrimaryKey>, IAddTask
    where TEntityTask : class, IHaveTask
    where TCreateRequestModel : class, IMapTo<TEntity>
    where TUpdateRequestModel : class, IMapTo<TEntity>
    where TPaginatedRequestModel : PaginatedRequestModel
    where TDetailsResponseModel : class, IMapFrom<TEntity>
    where TListingResponseModel : class, IMapFrom<TEntity>
{
    System.Threading.Tasks.Task AddTasksAsync(TPrimaryKey id, IEnumerable<TaskRequestModel> request);

    System.Threading.Tasks.Task UpdateTaskAsync(int taskId, TaskRequestModel request);

    System.Threading.Tasks.Task DeleteTaskAsync(int taskId);
}