namespace RealtoCrm.Tasks.Models;

using System;
using Mapping;

public class TaskResponseModel : IMapFrom<Task>
{
    public int Id { get; init; }

    public bool IsDone { get; init; } = default!;

    public string Text { get; init; } = default!;

    public int? EmployeeId { get; init; }

    public string? EmployeeFirstName { get; init; } = default!;

    public string? EmployeeMiddleName { get; init; } = default!;

    public string? EmployeeLastName { get; init; } = default!;
    
    public DateTime CreationTime { get; init; }
}