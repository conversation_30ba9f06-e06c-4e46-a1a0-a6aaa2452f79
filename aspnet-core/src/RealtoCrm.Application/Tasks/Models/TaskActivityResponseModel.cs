namespace RealtoCrm.Tasks.Models;

using System;
using AutoMapper;
using Mapping;
using Offers;
using Projects;
using Searches;

public class TaskActivityResponseModel :
    IMapFrom<OfferTask>,
    IMapFrom<SearchTask>,
    IMapFrom<ProjectTask>,
    IMapExplicitly
{
    public bool TaskIsDone { get; init; }

    public string? Text { get; init; }

    public int TaskId { get; init; }

    public DateTime CreationTime { get; init; }

    public string? EmployeeFirstName { get; init; }

    public string? EmployeeLastName { get; init; }

    public void RegisterMappings(IProfileExpression mapper)
    {
        mapper.CreateMap<SearchTask, TaskActivityResponseModel>()
            .ForMember(dest => dest.Text, opt
                => opt.MapFrom(src => src.Task.Text))
            .ForMember(dest => dest.EmployeeFirstName, opt
                => opt.MapFrom(src => src.Task.Employee != null ? src.Task.Employee.FirstName : null))
            .ForMember(dest => dest.EmployeeLastName, opt
                => opt.MapFrom(src => src.Task.Employee != null ? src.Task.Employee.LastName : null));

        mapper.CreateMap<OfferTask, TaskActivityResponseModel>()
            .ForMember(dest => dest.Text, opt
                => opt.MapFrom(src => src.Task.Text))
            .ForMember(dest => dest.EmployeeFirstName, opt
                => opt.MapFrom(src => src.Task.Employee != null ? src.Task.Employee.FirstName : null))
            .ForMember(dest => dest.EmployeeLastName, opt
                => opt.MapFrom(src => src.Task.Employee != null ? src.Task.Employee.LastName : null));

        mapper.CreateMap<ProjectTask, TaskActivityResponseModel>()
            .ForMember(dest => dest.Text, opt
                => opt.MapFrom(src => src.Task.Text))
            .ForMember(dest => dest.EmployeeFirstName, opt
                => opt.MapFrom(src => src.Task.Employee != null ? src.Task.Employee.FirstName : null))
            .ForMember(dest => dest.EmployeeLastName, opt
                => opt.MapFrom(src => src.Task.Employee != null ? src.Task.Employee.LastName : null));
    }
}