namespace RealtoCrm.Estates.Models;

using System.Collections.Generic;
using Addresses;
using AutoMapper;
using Expressions;
using Mapping;
using MiniExcelLibs.Attributes;
using Money;
using Offers;

public class EstateExcelHouseImportRequestModel : EstateExcelImportRequestModel,
    IMapTo<Estate>,
    IMapExplicitly
{
    [ExcelColumnIndex(0)]
    public string TypeName { get; init; } = string.Empty;

    [ExcelColumnIndex(1)]
    public string HouseTypeName { get; init; } = string.Empty;

    [ExcelColumnIndex(2)]
    public string Bedrooms { get; init; } = string.Empty;

    [ExcelColumnIndex(3)]
    public string Bathrooms { get; init; } = string.Empty;

    [ExcelColumnIndex(4)]
    public string FloorNumber { get; init; } = string.Empty;

    [ExcelColumnIndex(5)]
    public string HouseNumber { get; init; } = string.Empty;

    [ExcelColumnIndex(6)]
    public string GarageName { get; init; } = string.Empty;

    [ExcelColumnIndex(7)]
    public string UsefulFloorArea { get; init; } = string.Empty;

    [ExcelColumnIndex(8)]
    public string Area { get; init; } = string.Empty;

    [ExcelColumnIndex(9)]
    public string Price { get; init; } = string.Empty;

    [ExcelColumnIndex(10)]
    public string Vats { get; init; } = string.Empty;

    public int HouseTypeId { get; set; }

    public void RegisterMappings(IProfileExpression mapper)
        => mapper
            .CreateMap<EstateExcelHouseImportRequestModel, Estate>()
            .ForMember(m => m.BedroomsCount, cfg => cfg
                .MapFrom(m => m.Bedrooms.ParseToIntOrNull()))
            .ForMember(m => m.BathroomsCount, cfg => cfg
                .MapFrom(m => m.Bathrooms.ParseToIntOrNull()))
            .ForMember(m => m.Area, cfg => cfg
                .MapFrom(m => m.Area.ParseToDoubleOrNull()))
            .ForMember(m => m.EstateDetail, cfg => cfg
                .MapFrom(m => new EstateDetail
                {
                    HouseTypeId = m.HouseTypeId,
                    UsefulFloorArea = m.UsefulFloorArea.ParseToDoubleOrNull()
                }))
            .ForMember(m => m.Address, cfg => cfg
                .MapFrom(m => new Address
                {
                    FloorNumber = m.FloorNumber,
                    ApartmentNumber = m.HouseNumber,
                    TenantId = m.TenantId
                }))
            .ForMember(m => m.Offers, cfg => cfg
                .MapFrom(m => new List<Offer>
                {
                    new()
                    {
                        OfferStatusId = m.OfferStatusId,
                        OperationTypeId = m.OfferOperationTypeId,
                        ClientId = m.ClientId,
                        TenantId = m.TenantId,
                        ProjectId = m.ProjectId,
                        VatId = m.VatId,
                        EmployeeId = m.EmployeeId,
                        Price = new Money
                        {
                            Amount = m.Price.ParseToDecimalOrZero(),
                            Currency = Currency.EUR
                        }
                    }
                }));
}