namespace RealtoCrm.Estates;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.EntityFrameworkCore;
using Abp.Runtime.Session;
using Authorization;
using Buildings;
using Common.Attributes;
using DataCrudModels;
using DataExporting;
using Employees;
using EntityFrameworkCore;
using EstateTypes;
using Models;
using Expressions;
using Extensions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Nomenclatures;
using Nomenclatures.HouseTypes;
using Nomenclatures.OfferStatuses;
using Nomenclatures.OperationTypes;
using Nomenclatures.Vats;
using Offers;
using static CosherConsts;
using static CosherConsts.OfferStatuses;
using static CosherConsts.OperationTypes;

public class EstatesAppService(
    IExcelService excelService,
    IVatsAppService vatsAppService,
    IEmployeesAppService employeesAppService,
    IBuildingsAppService buildingsAppService,
    IHouseTypesAppService houseTypesAppService,
    IEstateTypesAppService estateTypesAppService,
    IOfferStatusesAppService offerStatusesAppService,
    IOperationTypesAppService operationTypesAppService,
    IDbContextProvider<RealtoCrmDbContext> dbContextProvider,
    IExpressionsBuilder expressionsBuilder)
    : DataCrudAppService<
        int,
        Estate,
        EstateCreateRequestModel,
        EstateUpdateRequestModel,
        PaginatedRequestModel,
        EstateDetailsResponseModel,
        EstateListingResponseModel>(dbContextProvider, expressionsBuilder), IEstatesAppService
{
    protected override ActionAttributeConfiguration AttributeConfiguration
        => new ActionAttributeConfiguration()
            .ConfigureAuthorize(
                nameof(this.CreateAsync),
                AppPermissions.EstateCreate,
                AppPermissions.ProjectCreate)
            .AddCustomAttribute(nameof(this.CreateAsync), new RequireTenantAttribute())
            .ConfigureAuthorize(
                nameof(this.CreateForLinkedProjectAsync),
                AppPermissions.EstateCreate,
                AppPermissions.LinkedProjectsCreate)
            .AddCustomAttribute(nameof(this.CreateAsync), new RequireTenantAttribute());

    public override async Task<int> CreateAsync(EstateCreateRequestModel request)
    {
        request.TenantId = this.GetTenantId();
        request.OfferStatusId = await offerStatusesAppService.GetIdByNameAsync(DraftOfferStatusName);
        request.OfferOperationTypeId = await operationTypesAppService.GetIdByNameAsync(SellingOperationTypeName);
        request.EmployeeId = await employeesAppService.GetIdOrDefaultAsync(this.AbpSession.GetUserId());

        var facingDirections = await this.GetFacingDirectionEntitiesAsync(request.FacingDirectionIds);

        var estate = this.ObjectMapper.Map<Estate>(request);

        estate.FacingDirections.AddRange(facingDirections);

        this.Data.Add(estate);

        await this.Data.SaveChangesAsync();

        return estate.Id;
    }

    public async Task<int> CreateForLinkedProjectAsync(int linkedProjectId, EstateForLinkedProjectRequestModel request)
    {
        var tenantId = this.GetTenantId();
        var employeeId = await employeesAppService.GetIdOrDefaultAsync(this.AbpSession.GetUserId());

        request.OfferStatusId = await offerStatusesAppService.GetIdByNameAsync(DraftOfferStatusName);
        request.OfferOperationTypeId = await operationTypesAppService.GetIdByNameAsync(SellingOperationTypeName);

        var facingDirections = await this.GetFacingDirectionEntitiesAsync(request.FacingDirectionIds);

        var linkedProjects = await this.ObjectMapper
            .ProjectTo<LinkedProjectForEstateResponseModel>(this
                .Data
                .Projects
                .AsNoTracking()
                .Where(p => p.LinkedProjectId == linkedProjectId))
            .ToListAsync();

        var orderedLinkedProjects = linkedProjects
            .OrderBy(p => p.TenantId != tenantId)
            .ThenBy(p => p.TenantId)
            .ToList();

        int? firstLinkedOfferId = null;

        foreach (var linkedProject in orderedLinkedProjects)
        {
            request.TenantId = linkedProject.TenantId!.Value;
            request.EstateGroupId = linkedProject.EstateGroupId;

            var offer = new EstateLinkedOfferRequestModel
            {
                OfferStatusId = request.OfferStatusId,
                OperationTypeId = request.OfferOperationTypeId,
                ClientId = request.ClientId,
                TenantId = linkedProject.TenantId,
                ProjectId = linkedProject.Id,
                VatId = request.VatId,
                PriceAmount = request.OfferPrice,
                EmployeeId = employeeId,
            };

            if (firstLinkedOfferId is not null)
            {
                offer.LinkedOfferId = firstLinkedOfferId;
            }

            var linkedOffer = this.ObjectMapper.Map<Offer>(offer);
            var estate = this.ObjectMapper.Map<Estate>(request);

            estate.FacingDirections.AddRange(facingDirections);

            linkedOffer.Estate = estate;

            this.Data.Offers.Add(linkedOffer);

            await this.Data.SaveChangesAsync();

            if (firstLinkedOfferId is null)
            {
                firstLinkedOfferId = linkedOffer.Id;
                linkedOffer.LinkedOfferId = linkedOffer.Id;

                await this.Data.SaveChangesAsync();
            }
        }

        return firstLinkedOfferId!.Value;
    }

    [Consumes("multipart/form-data")]
    public async Task<int> ImportForProjectFromExcelAsync([FromForm] EstateFromExcelRequestModel request)
    {
        var importedApartsCount = await this.ImportForBuildingFromExcelAsync<EstateExcelApartmentImportRequestModel>(
            request.ClientId,
            request.ProjectId,
            request.EstateGroupId,
            request.ApartmentsExcelFiles?.FirstOrDefault());

        var importedGaragesCount = await this.ImportForBuildingFromExcelAsync<EstateExcelGarageImportRequestModel>(
            request.ClientId,
            request.ProjectId,
            request.EstateGroupId,
            request.GaragesExcelFiles?.FirstOrDefault());

        var importedOfficesCount = await this.ImportForBuildingFromExcelAsync<EstateExcelOfficeImportRequestModel>(
            request.ClientId,
            request.ProjectId,
            request.EstateGroupId,
            request.OfficesExcelFiles?.FirstOrDefault());

        var importedHousesCount = await this.ImportForHouseFromExcelAsync(
            request.ClientId,
            request.ProjectId,
            request.EstateGroupId,
            request.HousesExcelFiles?.FirstOrDefault());

        var totalEstates = importedApartsCount + importedGaragesCount + importedOfficesCount + importedHousesCount;

        return totalEstates;
    }

    private async Task<int> ImportForBuildingFromExcelAsync<TImportRequestMode>(
        int clientId,
        int projectId,
        int estateGroupId,
        IFormFile? excelFile)
        where TImportRequestMode : EstateExcelImportRequestModel, IImportBuildingRequest, new()
    {
        if (excelFile is null)
        {
            return 0;
        }

        var fileBytes = await excelFile.ToBytesAsync();

        var estatesFromExcelImport = (await excelService.ImportAsync<TImportRequestMode>(fileBytes))
            .Where(e => !string.IsNullOrWhiteSpace(e.TypeName))
            .ToList();

        var vatNames = estatesFromExcelImport.Select(e => e.Vats).Distinct().ToList();
        var estateTypeNames = estatesFromExcelImport.Select(e => e.TypeName).Distinct().ToList();
        var buildingNames = estatesFromExcelImport.Select(e => e.BuildingName).Distinct().ToList();
        var entranceNumbers = estatesFromExcelImport.Select(e => e.EntranceNumber).Distinct().ToList();
        var apartmentNumbers = estatesFromExcelImport.Select(e => e.ApartmentNumber).Distinct().ToList();

        var duplicatedEstates = await this.GetDuplicatedForBuildingAsync(
            estateGroupId,
            buildingNames,
            entranceNumbers,
            apartmentNumbers);

        var abbreviationsSeparator = new[] { ",", ", " };

        var tenantId = this.GetTenantId();
        var vats = await vatsAppService.GetByNamesAsync(vatNames);
        var estateTypes = await estateTypesAppService.GetWithCategoryByNamesAsync(estateTypeNames);
        var buildings = await buildingsAppService.GetByNamesAsync(buildingNames);
        var offerStatusId = await offerStatusesAppService.GetIdByNameAsync(DraftOfferStatusName);
        var offerOperationTypeId = await operationTypesAppService.GetIdByNameAsync(SellingOperationTypeName);
        var employeeId = await employeesAppService.GetIdOrDefaultAsync(this.AbpSession.GetUserId());

        var facingDirectionsEntities = await this
            .Data
            .FacingDirections
            .ToListAsync();

        var estates = new List<Estate>();

        foreach (var estateFromExcel in estatesFromExcelImport)
        {
            var vat = vats.FirstOrDefault(v => v.Name == estateFromExcel.Vats);
            var estateType = estateTypes.FirstOrDefault(et => et.Name == estateFromExcel.TypeName);
            var building = buildings.FirstOrDefault(b => b.Name == estateFromExcel.BuildingName);

            if (vat is null || estateType is null || building is null)
            {
                continue;
            }

            var isDuplicated = duplicatedEstates
                .Any(e => e.BuildingName == building.Name &&
                          e.EntranceNumber == estateFromExcel.EntranceNumber &&
                          e.ApartmentNumber == estateFromExcel.ApartmentNumber);

            if (isDuplicated)
            {
                continue;
            }

            estateFromExcel.CategoryId = estateType.CategoryId;
            estateFromExcel.SubcategoryId = estateType.CategoryParentId;
            estateFromExcel.TypeId = estateType.Id;
            estateFromExcel.ClientId = clientId;
            estateFromExcel.ProjectId = projectId;
            estateFromExcel.EstateGroupId = estateGroupId;
            estateFromExcel.BuildingId = building.Id;
            estateFromExcel.VatId = vat.Id;
            estateFromExcel.OfferStatusId = offerStatusId;
            estateFromExcel.OfferOperationTypeId = offerOperationTypeId;
            estateFromExcel.TenantId = tenantId;
            estateFromExcel.EmployeeId = employeeId;

            var estate = this.ObjectMapper.Map<Estate>(estateFromExcel);

            var estateFacingDirectionNames = estateFromExcel
                .FacingDirectionsAbbreviations
                .Split(abbreviationsSeparator, StringSplitOptions.TrimEntries)
                .Select(abbr => FacingDirectionsAbbreviations.TryGetValue(abbr, out var name) ? name : null)
                .Where(name => name != null)
                .ToList();

            var facingDirectionsForEstate = facingDirectionsEntities
                .Where(fd => estateFacingDirectionNames.Contains(fd.Name))
                .ToList();

            estate.FacingDirections.AddRange(facingDirectionsForEstate);

            estates.Add(estate);
        }

        await this.AddRangeAsync(estates);

        return estates.Count;
    }

    private async Task<int> ImportForHouseFromExcelAsync(
        int clientId,
        int projectId,
        int estateGroupId,
        IFormFile? excelFile)
    {
        if (excelFile is null)
        {
            return 0;
        }

        var fileBytes = await excelFile.ToBytesAsync();

        var estatesFromExcelImport = (await excelService.ImportAsync<EstateExcelHouseImportRequestModel>(fileBytes))
            .Where(e => !string.IsNullOrWhiteSpace(e.TypeName))
            .ToList();

        var vatNames = estatesFromExcelImport.Select(e => e.Vats).Distinct().ToList();
        var estateTypeNames = estatesFromExcelImport.Select(e => e.TypeName).Distinct().ToList();
        var houseTypeNames = estatesFromExcelImport.Select(e => e.HouseTypeName).Distinct().ToList();
        var houseNumbers = estatesFromExcelImport.Select(e => e.HouseNumber).Distinct().ToList();
        var garageNames = estatesFromExcelImport.Select(e => e.GarageName).Distinct().ToList();

        var duplicatedEstates = await this.GetDuplicatedForHouseAsync(
            estateGroupId,
            houseTypeNames,
            houseNumbers);

        var tenantId = this.GetTenantId();
        var vats = await vatsAppService.GetByNamesAsync(vatNames);
        var houseTypes = await houseTypesAppService.GetByNamesAsync(houseTypeNames);
        var estateTypes = await estateTypesAppService.GetWithCategoryByNamesAsync(estateTypeNames);
        var offerStatusId = await offerStatusesAppService.GetIdByNameAsync(DraftOfferStatusName);
        var offerOperationTypeId = await operationTypesAppService.GetIdByNameAsync(SellingOperationTypeName);
        var employeeId = await employeesAppService.GetIdOrDefaultAsync(this.AbpSession.GetUserId());

        var garages = await this
            .Data
            .Garages
            .Where(g => garageNames.Contains(g.Name))
            .ToListAsync();

        var estates = new List<Estate>();

        foreach (var estateFromExcel in estatesFromExcelImport)
        {
            var vat = vats.FirstOrDefault(v => v.Name == estateFromExcel.Vats);
            var estateType = estateTypes.FirstOrDefault(et => et.Name == estateFromExcel.TypeName);
            var garage = garages.FirstOrDefault(g => g.Name == estateFromExcel.GarageName);
            var houseType = houseTypes.FirstOrDefault(ht => ht.Name == estateFromExcel.HouseTypeName);

            if (vat is null || estateType is null || garage is null || houseType is null)
            {
                continue;
            }

            var isDuplicated = duplicatedEstates
                .Any(e => e.HouseTypeName == estateFromExcel.HouseTypeName &&
                          e.HouseNumber == estateFromExcel.HouseNumber);

            if (isDuplicated)
            {
                continue;
            }

            estateFromExcel.CategoryId = estateType.CategoryId;
            estateFromExcel.SubcategoryId = estateType.CategoryParentId;
            estateFromExcel.TypeId = estateType.Id;
            estateFromExcel.ClientId = clientId;
            estateFromExcel.ProjectId = projectId;
            estateFromExcel.EstateGroupId = estateGroupId;
            estateFromExcel.VatId = vat.Id;
            estateFromExcel.OfferStatusId = offerStatusId;
            estateFromExcel.OfferOperationTypeId = offerOperationTypeId;
            estateFromExcel.TenantId = tenantId;
            estateFromExcel.EmployeeId = employeeId;
            estateFromExcel.HouseTypeId = houseType.Id;

            var estate = this.ObjectMapper.Map<Estate>(estateFromExcel);

            estate.Offers.ForEach(o => o.Garages.Add(garage));

            estates.Add(estate);
        }

        await this.AddRangeAsync(estates);

        return estates.Count;
    }

    private async Task<List<FacingDirection>> GetFacingDirectionEntitiesAsync(
        IEnumerable<int> ids)
        => await this
            .Data
            .FacingDirections
            .Where(fd => ids.Contains(fd.Id))
            .ToListAsync();

    private async Task<IEnumerable<EstateDuplicatedForBuildingResponseModel>> GetDuplicatedForBuildingAsync(
        int estateGroupId,
        IEnumerable<string> buildingNames,
        IEnumerable<string> entranceNumbers,
        IEnumerable<string> apartmentNumbers)
        => await this.ObjectMapper
            .ProjectTo<EstateDuplicatedForBuildingResponseModel>(this
                .AllAsNoTracking()
                .Where(e =>
                    e.EstateGroupId == estateGroupId &&
                    buildingNames.Any(b =>
                        e.Building != null &&
                        b.ToLower() == e.Building.Name.ToLower()) &&
                    entranceNumbers.Any(n =>
                        e.Address.EntranceNumber != null &&
                        n.ToLower() == e.Address.EntranceNumber.ToLower()) &&
                    apartmentNumbers.Any(n =>
                        e.Address.ApartmentNumber != null &&
                        n.ToLower() == e.Address.ApartmentNumber.ToLower())))
            .ToListAsync();

    private async Task<IEnumerable<EstateDuplicatedForHouseResponseModel>> GetDuplicatedForHouseAsync(
        int estateGroupId,
        IEnumerable<string> houseTypes,
        IEnumerable<string> houseNumbers)
        => await this.ObjectMapper
            .ProjectTo<EstateDuplicatedForHouseResponseModel>(this
                .AllAsNoTracking()
                .Where(e =>
                    e.EstateGroupId == estateGroupId &&
                    houseTypes.Any(b =>
                        e.EstateDetail.HouseType != null &&
                        b.ToLower() == e.EstateDetail.HouseType.Name.ToLower()) &&
                    houseNumbers.Any(n =>
                        e.Address.ApartmentNumber != null &&
                        n.ToLower() == e.Address.ApartmentNumber.ToLower())))
            .ToListAsync();
}