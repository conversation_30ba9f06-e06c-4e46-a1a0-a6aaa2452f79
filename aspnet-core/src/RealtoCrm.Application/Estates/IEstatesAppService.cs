namespace RealtoCrm.Estates;

using System.Threading.Tasks;
using DataCrudModels;
using Models;

public interface IEstatesAppService : IDataCrudAppService<
    int,
    Estate,
    EstateCreateRequestModel,
    EstateUpdateRequestModel,
    PaginatedRequestModel,
    EstateDetailsResponseModel,
    EstateListingResponseModel>
{
    Task<int> CreateForLinkedProjectAsync(int linkedProjectId, EstateForLinkedProjectRequestModel request);

    Task<int> ImportForProjectFromExcelAsync(EstateFromExcelRequestModel request);
}