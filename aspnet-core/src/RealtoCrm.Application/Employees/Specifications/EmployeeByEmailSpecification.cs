namespace RealtoCrm.Employees.Specifications;

using System;
using System.Linq.Expressions;

public class EmployeeByEmailSpecification(string? email) : Specification<Employee>
{
    protected override bool Include => !string.IsNullOrWhiteSpace(email);

    public override Expression<Func<Employee, bool>> ToExpression()
        => employee => employee.UserAccount.EmailAddress.ToLower()
            .Contains(email!.ToLower());
}