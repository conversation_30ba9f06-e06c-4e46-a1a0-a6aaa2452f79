namespace RealtoCrm.Employees.Specifications;

using System;
using System.Linq.Expressions;

public class EmployeeByNameSpecification(string? name) : Specification<Employee>
{
    protected override bool Include => !string.IsNullOrWhiteSpace(name);

    public override Expression<Func<Employee, bool>> ToExpression()
        => employee => (employee.FirstName + " " + employee.LastName).ToLower()
            .Contains(name!.ToLower());
}