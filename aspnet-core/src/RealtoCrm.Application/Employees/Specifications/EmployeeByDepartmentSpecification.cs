namespace RealtoCrm.Employees.Specifications;

using System;
using System.Linq.Expressions;

public class EmployeeByDepartmentSpecification(string? department) : Specification<Employee>
{
    protected override bool Include => !string.IsNullOrWhiteSpace(department);

    public override Expression<Func<Employee, bool>> ToExpression()
        => employee => employee.Department != null && employee.Department.Name == department;
}