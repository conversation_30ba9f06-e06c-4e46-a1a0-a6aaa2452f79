namespace RealtoCrm.Employees.Specifications;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;

public class EmployeeNotContainedInIdsSpecification(IEnumerable<int>? ids) : Specification<Employee>
{
    protected override bool Include => ids?.Any() ?? false;

    public override Expression<Func<Employee, bool>> ToExpression()
        => employee => !ids!.Contains(employee.Id);
}