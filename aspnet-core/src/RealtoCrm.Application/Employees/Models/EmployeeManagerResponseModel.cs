namespace RealtoCrm.Employees.Models;

using AutoMapper;
using Mapping;

public class EmployeeManagerResponseModel : IMapFrom<Employee>, IMapExplicitly
{
    public string? FirstName { get; init; }

    public string? LastName { get; init; }

    public long UserId { get; init; }

    public string EmailAddress { get; init; } = default!;

    public void RegisterMappings(IProfileExpression mapper)
        => mapper
            .CreateMap<Employee, EmployeeManagerResponseModel>()
            .ForMember(m => m.UserId, cfg => cfg
                .MapFrom(m => m.UserAccount.UserId))
            .ForMember(m => m.EmailAddress, cfg => cfg
                .MapFrom(m => m.UserAccount.EmailAddress));
}