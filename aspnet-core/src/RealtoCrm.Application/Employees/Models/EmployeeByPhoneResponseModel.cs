using AutoMapper;
using RealtoCrm.Mapping;

namespace RealtoCrm.Employees.Models;

public class EmployeeByPhoneResponseModel : IMapFrom<Employee>, IMapExplicitly
{
    public int Id { get; init; }
    
    public int TenantId { get; set; }
    
    public void RegisterMappings(IProfileExpression profile)
    {
        profile.CreateMap<Employee, EmployeeByPhoneResponseModel>()
            .ForMember(dest => dest.TenantId, opt => opt.MapFrom(src => src.UserAccount.TenantId));
    }
}