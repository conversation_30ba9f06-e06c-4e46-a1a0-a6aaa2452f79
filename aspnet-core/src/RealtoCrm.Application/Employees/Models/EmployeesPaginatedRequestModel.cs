namespace RealtoCrm.Employees.Models;

using DataCrudModels;

public class EmployeesPaginatedRequestModel : PaginatedRequestModel
{
    public string? Email { get; init; }
    
    public string? PhoneNumber { get; init; }

    public string? Name { get; init; }
    
    public string? Company { get; init; }
    
    public string? Division { get; init; }
    
    public string? Department { get; init; }
    
    public string? Office { get; init; }
    
    public string? Team { get; init; }
    
    public string? Position { get; init; }

    public bool OnlyActive { get; init; } = false;
}