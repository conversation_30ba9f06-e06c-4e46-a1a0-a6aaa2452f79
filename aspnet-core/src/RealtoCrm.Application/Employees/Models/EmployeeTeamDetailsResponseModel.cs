using AutoMapper;
using RealtoCrm.Searches;

namespace RealtoCrm.Employees.Models;

using Mapping;

public class EmployeeTeamDetailsResponseModel : IMapFrom<Employee>, IMapFrom<Search>, IMapExplicitly
{
    public int Id { get; init; }

    public int? TeamId { get; init; }

    public int? DepartmentId { get; init; }

    public int? DivisionId { get; init; }
    
    public long? EmployeeUserId { get; init; }
    
    public int? TenantId { get; init; }

    public void RegisterMappings(IProfileExpression mapper)
    {
        mapper
            .CreateMap<Employee, EmployeeTeamDetailsResponseModel>()
            .ForMember(m => m.EmployeeUserId, cfg => cfg
                .MapFrom(m => m.UserAccount.UserId));
        
        mapper
            .CreateMap<Search, EmployeeTeamDetailsResponseModel>()
            .ForMember(m => m.EmployeeUserId, cfg => cfg
                .MapFrom(m => m.Employee != null
                    ? m.Employee.UserAccount.UserId
                    : (long?)null));
    }
}