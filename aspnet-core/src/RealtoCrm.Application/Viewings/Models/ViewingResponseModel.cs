namespace RealtoCrm.Viewings.Models;

using System;
using System.Linq;
using AutoMapper;
using Mapping;
using Offers.Models;

public class ViewingResponseModel : IMapFrom<Viewing>, IMapExplicitly
{
    public int Id { get; init; }

    public DateTime StartDate { get; init; }

    public int StatusId { get; init; }

    public string StatusName { get; init; } = default!;

    public int MatchId { get; set; }

    public int SearchId { get; init; }

    public int SearchClientId { get; set; }

    public int? SearchEmployeeId { get; set; }

    public int OfferId { get; init; }

    public int OfferClientId { get; set; }

    public int? OfferEmployeeId { get; set; }

    public DateTime? ClientLastContact { get; init; }

    public OfferListingResponseModel? Offer { get; init; } = default!;
    
    public DateTime CreationTime { get; init; }

    public void RegisterMappings(IProfileExpression mapper)
        => mapper.CreateMap<Viewing, ViewingResponseModel>()
            .ForMember(dest => dest.ClientLastContact, opt
                => opt.MapFrom(src => src.Offer.Client.Calls.Count > 0
                    ? src.Offer.Client.Calls.OrderByDescending(x => x.Start).FirstOrDefault()!.Start
                    : (DateTime?)null));
}