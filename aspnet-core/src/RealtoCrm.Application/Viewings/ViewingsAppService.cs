namespace RealtoCrm.Viewings;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp;
using Abp.EntityFrameworkCore;
using Authorization;
using Comments;
using Common.Attributes;
using DataCrudModels;
using Employees;
using EntityFrameworkCore;
using Expressions;
using Matches;
using Matches.Models;
using Microsoft.EntityFrameworkCore;
using Models;
using Notifications;
using Offers;
using Offers.Models;
using Providers;
using Searches;
using Searches.Models;

public class ViewingsAppService(
    IAppNotifier appNotifier,
    IDateTimeService dateTimeService,
    IMatchesAppService matchesAppService,
    ISearchesAppService searchesAppService,
    IOffersAppService offersAppService,
    IEmployeesAppService employeesAppService,
    IDbContextProvider<RealtoCrmDbContext> dbContextProvider,
    IExpressionsBuilder expressionsBuilder)
    : CommentsAppService<
        int,
        Viewing,
        ViewingComment,
        ViewingRequestModel,
        ViewingRequestModel,
        PaginatedRequestModel,
        ViewingResponseModel,
        ViewingResponseModel>(employeesAppService, dbContextProvider, expressionsBuilder), IViewingsAppService
{
    protected override ActionAttributeConfiguration AttributeConfiguration
        => new ActionAttributeConfiguration()
            .ConfigureAuthorize(
                nameof(this.CreateAsync),
                AppPermissions.ViewingCreate)
            .AddCustomAttribute(nameof(this.CreateAsync), new RequireTenantAttribute())
            .ConfigureAuthorize(nameof(this.UpdateAsync), AppPermissions.ViewingUpdate)
            .AddCustomAttribute(nameof(this.UpdateAsync), new RequireTenantAttribute())
            .ConfigureAuthorize(nameof(this.DeleteAsync), AppPermissions.ViewingUpdate)
            .AddCustomAttribute(nameof(this.DeleteAsync), new RequireTenantAttribute())
            .ConfigureAuthorize(nameof(this.GetDetailsAsync), AppPermissions.ViewingRead);

    public override async Task<int> CreateAsync(ViewingRequestModel request)
    {
        var match = await this.GetMatchBySearchAndOfferAsync(request.SearchId, request.OfferId);

        if (match?.StatusId
            is (int)MatchStatusId.InternallyPresented
            or (int)MatchStatusId.Presented
            or (int)MatchStatusId.Denied)
        {
            match.StatusId = (int)MatchStatusId.ForViewing;
        }

        var isDateInFuture = request.StartDate > dateTimeService.Now;

        var matchId = match?.Id ?? await matchesAppService.CreateAsync(new MatchRequestModel
        {
            SearchId = request.SearchId,
            OfferId = request.OfferId,
            StatusId = isDateInFuture
                ? (int)MatchStatusId.ForViewing
                : (int)MatchStatusId.Viewed,
        });

        var searchClientEmployeeIds = await searchesAppService.GetClientAndEmployeeIdsAsync(request.SearchId);
        var offerClientEmployeeIds = await offersAppService.GetClientAndEmployeeIdsAsync(request.OfferId);

        request.MatchId = matchId;
        request.StatusId = isDateInFuture
            ? (int)ViewingStatusId.Proposed
            : (int)ViewingStatusId.Completed;

        if (searchClientEmployeeIds is not null)
        {
            request.SearchClientId = searchClientEmployeeIds.ClientId;
            request.SearchEmployeeId = searchClientEmployeeIds.EmployeeId;
        }

        if (offerClientEmployeeIds is not null)
        {
            request.OfferClientId = offerClientEmployeeIds.ClientId;
            request.OfferEmployeeId = offerClientEmployeeIds.EmployeeId;
        }

        var viewingId = await base.CreateAsync(request);

        await this.SendViewingCreatedNotificationAsync(
            request,
            offerClientEmployeeIds,
            searchClientEmployeeIds);

        return viewingId;
    }

    public async Task<ICollection<ViewingResponseModel>> GetViewingsByClientIdAsync(int clientId)
        => await this.ObjectMapper.ProjectTo<ViewingResponseModel>(
                this.AllAsNoTracking()
                    .Where(v => v.OfferClient.Id == clientId || v.SearchClient.Id == clientId))
            .ToListAsync();

    private async Task<Match?> GetMatchBySearchAndOfferAsync(int searchId, int offerId)
        => await this
            .Data
            .Matches
            .Where(m => m.SearchId == searchId && m.OfferId == offerId)
            .FirstOrDefaultAsync();

    private async Task SendViewingCreatedNotificationAsync(
        ViewingRequestModel request,
        OfferClientEmployeeResponseModel? offerClientEmployeeIds,
        SearchClientEmployeeResponseModel? searchClientEmployeeIds)
    {
        var notificationEmployeeTenantId = this.AbpSession.TenantId == offerClientEmployeeIds?.EmployeeTenantId
            ? searchClientEmployeeIds?.EmployeeTenantId
            : offerClientEmployeeIds?.EmployeeTenantId;

        var notificationEmployeeUserId = this.AbpSession.UserId == offerClientEmployeeIds?.EmployeeUserId
            ? searchClientEmployeeIds?.EmployeeUserId
            : offerClientEmployeeIds?.EmployeeUserId;

        if (notificationEmployeeUserId is null)
        {
            return;
        }

        var notificationUser = new UserIdentifier(
            notificationEmployeeTenantId,
            notificationEmployeeUserId.Value);

        var userTimeZone = TimeZoneInfo.FindSystemTimeZoneById("Europe/Sofia");
        var localStartDate = TimeZoneInfo.ConvertTimeFromUtc(request.StartDate, userTimeZone);

        await appNotifier.SendEventMessageAsync(
            notificationUser,
            this.L("NewViewingNotificationMessage", request.OfferId, localStartDate.ToString("g")));
    }
}