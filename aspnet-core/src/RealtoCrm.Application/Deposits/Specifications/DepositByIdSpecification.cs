namespace RealtoCrm.Deposits.Specifications;

using System;
using System.Linq.Expressions;

public class DepositByIdSpecification(string? id) : Specification<Deposit>
{
    protected override bool Include => !string.IsNullOrWhiteSpace(id);

    public override Expression<Func<Deposit, bool>> ToExpression()
        => int.TryParse(id, out var parsedId)
            ? client => client.Id == parsedId
            : client => false;
}