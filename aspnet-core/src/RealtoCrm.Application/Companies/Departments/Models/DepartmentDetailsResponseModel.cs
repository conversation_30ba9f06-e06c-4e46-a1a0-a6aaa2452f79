namespace RealtoCrm.Companies.Departments.Models;

using System.Collections.Generic;
using Mapping;

public class DepartmentDetailsResponseModel : IMapFrom<Department>
{
    public int Id { get; init; }

    public string Name { get; init; } = default!;

    public int OfficeId { get; init; } = default!;

    public string OfficeName { get; init; } = default!;

    public int DivisionId { get; init; } = default!;

    public string DivisionName { get; init; } = default!;

    public int CompanyId { get; init; } = default!;

    public string CompanyName { get; init; } = default!;

    public bool IsActive { get; init; }

    public IEnumerable<DepartmentContactDetailResponseModel> ContactDetails { get; init; } = default!;
}