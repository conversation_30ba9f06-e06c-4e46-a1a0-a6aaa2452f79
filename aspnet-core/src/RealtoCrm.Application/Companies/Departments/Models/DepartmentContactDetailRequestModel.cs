namespace RealtoCrm.Companies.Departments.Models;

using System.ComponentModel.DataAnnotations;
using Mapping;
using static RealtoCrm.ModelConstants.Common;

public class DepartmentContactDetailRequestModel : IMapTo<DepartmentContactDetail>
{
    public int? Id { get; init; }

    public int ContactDetailId { get; init; } = default!;

    [Required]
    [MaxLength(MaxUrlLength)]
    public string Value { get; init; } = default!;

    public bool IsDefault { get; init; }
}