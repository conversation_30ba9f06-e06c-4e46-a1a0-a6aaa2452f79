namespace RealtoCrm.Companies.Specifications;

using System;
using System.Linq.Expressions;

public class CompanyOnlyActiveSpecification : Specification<Company>
{
    private readonly bool onlyActive;

    public CompanyOnlyActiveSpecification(bool onlyActive)
        => this.onlyActive = onlyActive;

    public override Expression<Func<Company, bool>> ToExpression()
    {
        if (this.onlyActive)
        {
            return company => company.IsActive;
        }

        return company => true;
    }
}