namespace RealtoCrm.Companies.Teams.Models;

using AutoMapper;
using Employees;
using Mapping;

public class TeamEmployeeResponseModel : IMapFrom<Employee>, IMapExplicitly
{
    public int Id { get; init; }

    public string? FirstName { get; init; }

    public string? LastName { get; init; }

    public string? Department { get; init; }

    public string? Region { get; init; }

    public int ViewingsCount { get; init; }

    public int DocumentsCount { get; init; }

    public int CallsCount { get; init; }

    public decimal DealsPrice { get; init; }

    public decimal Efficiency { get; init; }

    public void RegisterMappings(IProfileExpression mapper)
        => mapper
            .CreateMap<Employee, TeamEmployeeResponseModel>()
            .ForMember(m => m.Department, cfg => cfg
                .MapFrom(m => m.Team == null
                    ? string.Empty
                    : m.Team.Department.Name))
            .ForMember(m => m.Region, cfg => cfg
                .MapFrom(m => m.Office == null
                    ? string.Empty
                    : m.Office.Company.Name));
}