namespace RealtoCrm.Companies.Teams.Models;

using Mapping;

public class TeamDetailsResponseModel : IMapFrom<Team>
{
    public int Id { get; init; }

    public string Name { get; init; } = default!;

    public int? ParentId { get; init; }

    public string? ParentName { get; init; }

    public int TenantId { get; init; }

    public int CompanyId { get; init; }

    public string CompanyName { get; init; } = default!;

    public int? DepartmentId { get; set; }

    public string? DepartmentName { get; init; }

    public bool IsActive { get; init; }
}