namespace RealtoCrm.Companies.Teams.Specifications;

using System;
using System.Linq.Expressions;
using RealtoCrm.Companies;

public class TeamByParentSpecification : Specification<Team>
{
    private readonly int? parentId;

    public TeamByParentSpecification(int? parentId) => this.parentId = parentId;

    protected override bool Include => this.parentId.HasValue;

    public override Expression<Func<Team, bool>> ToExpression()
        => team => team.ParentId == this.parentId;
}