namespace RealtoCrm.Companies.Teams.Specifications;

using System;
using System.Linq.Expressions;
using RealtoCrm.Companies;

public class TeamByNameSpecification : Specification<Team>
{
    private readonly string? name;

    public TeamByNameSpecification(string? name) => this.name = name;

    protected override bool Include => !string.IsNullOrWhiteSpace(this.name);

    public override Expression<Func<Team, bool>> ToExpression()
        => team => team.Name.ToLower()
            .Contains(this.name!.ToLower());
}