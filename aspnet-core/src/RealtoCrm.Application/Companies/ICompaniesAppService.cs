namespace RealtoCrm.Companies;

using System.Collections.Generic;
using System.Threading.Tasks;
using Models;
using MultiTenancy;
using MultiTenancy.Models;
using RealtoCrm.Mapping;

public interface ICompaniesAppService : IDataCrudAppService<
    int,
    Company,
    TenantCreateRequestModel,
    TenantUpdateRequestModel,
    CompaniesPaginatedRequestModel,
    CompanyDetailsResponseModel,
    CompanyListingResponseModel>
{
    Task<TResponseModel> GetCurrentCompanyDetails<TResponseModel>()
        where TResponseModel : IMapFrom<Company>;

    Task<string> GetCompanyNameByTenantAsync(int tenantId);

    Task<Tenant?> GetTenantByIdAsync(int? tenantId);

    Task<IEnumerable<CompanyByTenantsResponseModel>> GetCompaniesByTenantsAsync(IEnumerable<int?> tenantIds);
    
    Task<IEnumerable<CompanyDropdownResponseModel>> GetNameAndTenantIdAsync();

    Task<IEnumerable<CompanyForLinkedProjectResponseModel>> GetAllForLinkedProjectAsync();
}