namespace RealtoCrm.Companies.Offices;

using System.Collections.Generic;
using System.Linq.Expressions;
using Abp.EntityFrameworkCore;
using EntityFrameworkCore;
using Models;
using Expressions;
using Specifications;

public class OfficesAppService(
    IDbContextProvider<RealtoCrmDbContext> dbContextProvider,
    IExpressionsBuilder expressionsBuilder)
    : DataCrudAppService<
        int,
        Office,
        OfficeRequestModel,
        OfficeRequestModel,
        OfficesPaginatedRequestModel,
        OfficeDetailsResponseModel,
        OfficeListingResponseModel>(dbContextProvider, expressionsBuilder), IOfficesAppService
{
    protected override Dictionary<string, FilterExpression> CustomFilters
        => new()
        {
            [nameof(OfficeListingResponseModel.DivisionName)]
                = new FilterExpression(
                    typeof(Division).GetProperty(nameof(Division.Name))!,
                    Expression.PropertyOrField(
                        Expression.PropertyOrField(this.Parameter, nameof(Office.Division)), nameof(Division.Name))),
            [nameof(OfficeListingResponseModel.CompanyName)]
                = expressionsBuilder.BuildForCompanyName<Office>(this.Parameter)
        };

    protected override Specification<Office> GetSpecification(OfficesPaginatedRequestModel request)
        => new OfficeByNameSpecification(request.Name)
            .And(new OfficeByDivisionSpecification(request.DivisionId))
            .And(new OfficeByCompanySpecification(request.CompanyId))
            .And(new OfficeOnlyActiveSpecification(request.OnlyActive));
}