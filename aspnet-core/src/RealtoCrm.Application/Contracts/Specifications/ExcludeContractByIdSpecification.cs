namespace RealtoCrm.Contracts.Specifications;

using System;
using System.Linq.Expressions;

public class ExcludeContractByIdSpecification(int? excludeContractId) : Specification<Contract>
{
    protected override bool Include => excludeContractId.HasValue;

    public override Expression<Func<Contract, bool>> ToExpression()
    {
        return contract => contract.Id != excludeContractId.Value;
    }
}