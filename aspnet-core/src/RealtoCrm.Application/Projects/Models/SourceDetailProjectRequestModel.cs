namespace RealtoCrm.Projects.Models;

using Mapping;
using SourceCategories;

public class SourceDetailProjectRequestModel : IMapTo<SourceDetailProject>
{
    public int CategoryId { get; init; }

    public int? DetailWebsiteId { get; init; }

    public int? DetailSocialMediaId { get; init; }

    public int? DetailMarketingCampaignId { get; init; }

    public int? DetailEmployeeId { get; init; }

    public int? DetailClientId { get; init; }
}