namespace RealtoCrm.Projects.Models;

using System;
using Addresses.Models;
using Buildings;
using Mapping;

public class ProjectBuildingResponseModel : IMapFrom<Building>
{
    public int Id { get; init; }

    public BuildingType Type { get; init; } = BuildingType.Residential;

    public string Name { get; init; } = default!;

    public double? Area { get; init; }

    public double? PlotArea { get; init; }

    public int? Floors { get; init; }

    public DateTime? Act14Date { get; init; }

    public DateTime? Act15Date { get; init; }

    public DateTime? Act16Date { get; init; }

    public double? Latitude { get; init; }

    public double? Longitude { get; init; }

    public AddressResponseModel Address { get; init; } = default!;

    public int? BuildingPurposeId { get; init; }

    public string? BuildingPurposeName { get; init; }

    public int? CompletionLevelId { get; init; }

    public string? CompletionLevelName { get; init; }
}