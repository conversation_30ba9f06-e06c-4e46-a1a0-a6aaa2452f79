namespace RealtoCrm.Buildings.Models;

using Addresses.Models;
using Mapping;

public class BuildingResponseModel : IMapFrom<Building>
{
    public int Id { get; init; }

    public string Name { get; init; } = null!;

    public string Type { get; init; } = null!;

    public double? Latitude { get; init; }

    public double? Longitude { get; init; }

    public double? Area { get; init; }

    public AddressResponseModel Address { get; init; } = null!;
}