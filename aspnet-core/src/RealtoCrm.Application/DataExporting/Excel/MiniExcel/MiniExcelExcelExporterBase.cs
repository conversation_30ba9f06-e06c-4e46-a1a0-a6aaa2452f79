using System.Collections.Generic;
using System.IO;
using Abp.AspNetZeroCore.Net;
using Abp.Dependency;
using MiniExcelLibs;
using RealtoCrm.Dto;
using RealtoCrm.Storage;

namespace RealtoCrm.DataExporting.Excel.MiniExcel;

public abstract class MiniExcelExcelExporterBase(ITempFileCacheManager tempFileCacheManager) : RealtoCrmServiceBase, ITransientDependency
{
    protected FileDto CreateExcelPackage(string fileName, List<Dictionary<string, object>> items)
    {
        var file = new FileDto(fileName, MimeTypeNames.ApplicationVndOpenxmlformatsOfficedocumentSpreadsheetmlSheet);

        this.Save(items, file);

        return file;
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="items"></param>
    /// <param name="file"></param>
    protected virtual void Save(List<Dictionary<string, object>> items, FileDto file)
    {
        using (var stream = new MemoryStream())
        {
            stream.SaveAs(items);
            tempFileCacheManager.SetFile(file.FileToken, stream.ToArray());
        }
    }
}