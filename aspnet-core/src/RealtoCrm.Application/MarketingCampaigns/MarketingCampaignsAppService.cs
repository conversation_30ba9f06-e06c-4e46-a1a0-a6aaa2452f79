using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;

namespace RealtoCrm.MarketingCampaigns;

using Abp.EntityFrameworkCore;
using EntityFrameworkCore;
using Models;
using Expressions;
using SourceCategories;
using System.Threading.Tasks;
using Specifications;

public class MarketingCampaignsAppService(
    IDbContextProvider<RealtoCrmDbContext> dbContextProvider,
    IExpressionsBuilder expressionsBuilder)
    : DataCrudAppService<
        int,
        MarketingCampaign,
        MarketingCampaignRequestModel,
        MarketingCampaignRequestModel,
        MarketingCampaignPaginatedRequestModel,
        MarketingCampaignResponseModel,
        MarketingCampaignResponseModel>(dbContextProvider, expressionsBuilder), IMarketingCampaignsAppService

{
    protected override Specification<MarketingCampaign> GetSpecification(MarketingCampaignPaginatedRequestModel request)
        => new MarketingCampaignByNameSpecification(request.SearchTerm);
    
    public async Task<IEnumerable<MarketingCampaignResponseModel>> GetAllWithoutPagination()
    => await this.ObjectMapper
        .ProjectTo<MarketingCampaignResponseModel>(this
            .AllAsNoTracking())
        .ToListAsync();
}