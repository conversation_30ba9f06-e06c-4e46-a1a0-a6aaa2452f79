namespace RealtoCrm.MarketingCampaigns;

using Models;
using SourceCategories;
using System.Collections.Generic;
using System.Threading.Tasks;

public interface IMarketingCampaignsAppService : IDataCrudAppService<
    int,
    MarketingCampaign,
    MarketingCampaignRequestModel,
    MarketingCampaignRequestModel,
    MarketingCampaignPaginatedRequestModel,
    MarketingCampaignResponseModel,
    MarketingCampaignResponseModel>
{
    Task<IEnumerable<MarketingCampaignResponseModel>> GetAllWithoutPagination();
}