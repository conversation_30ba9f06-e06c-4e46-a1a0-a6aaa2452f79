namespace RealtoCrm.EstateGroups;

using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.EntityFrameworkCore;
using EntityFrameworkCore;
using EstateObjects;
using Expressions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Models;
using Specifications;

public class EstateGroupsAppService(
    IDbContextProvider<RealtoCrmDbContext> dbContextProvider,
    IExpressionsBuilder expressionsBuilder) :
    DataCrudAppService<
        int,
        EstateGroup,
        EstateGroupRequestModel,
        EstateGroupRequestModel,
        EstateGroupPaginatedRequestModel,
        EstateGroupResponseModel,
        EstateGroupResponseModel>(dbContextProvider, expressionsBuilder), IEstateGroupsAppService
{
    protected override Specification<EstateGroup> GetSpecification(EstateGroupPaginatedRequestModel request)
        => new EstateGroupByPartialNameSpecification(request.SearchTerm);

    public async Task<EstateGroupResponseModel?> GetByName(string name)
        => await this.ObjectMapper
            .ProjectTo<EstateGroupResponseModel>(this
                .AllAsNoTracking()
                .Where(new EstateGroupByNameSpecification(name)
                    .And(new EstateGroupByTenantIdSpecification(this.AbpSession.TenantId))))
            .FirstOrDefaultAsync();

    public async Task<ICollection<EstateGroupResponseModel>> GetByNameOrId(string? nameOrId)
        => await this.ObjectMapper
            .ProjectTo<EstateGroupResponseModel>(this
                .AllAsNoTracking()
                .Where(new EstateGroupByIdSpecification(nameOrId)
                    .Or(new EstateGroupByPartialNameSpecification(nameOrId))
                    .And(new EstateGroupByTenantIdSpecification(this.AbpSession.TenantId))))
            .Take(10)
            .ToListAsync();

    [HttpPost]
    public async Task<IEnumerable<EstateGroupForProjectResponseModel>> GetForProjectAsync(
        EstateGroupsForProjectRequestModel request)
        => await this.ObjectMapper
            .ProjectTo<EstateGroupForProjectResponseModel>(this
                .AllAsNoTracking()
                .Where(new EstateGroupByNameSpecification(request.Name))
                .Where(this.BuildTenantSpecification()))
            .Take(request.PageSize)
            .ToListAsync();

    [HttpPost]
    public async Task<IEnumerable<EstateGroupForLinkedProjectResponseModel>> GetForLinkedProjectAsync(
        EstateGroupsForLinkedProjectRequestModel request)
        => await this.ObjectMapper
            .ProjectTo<EstateGroupForLinkedProjectResponseModel>(this
                .AllAsNoTracking()
                .Where(new EstateGroupByNameSpecification(request.Name)
                    .And(new EstateGroupByTenantsSpecification(request.TenantsIds))))
            .ToListAsync();
}