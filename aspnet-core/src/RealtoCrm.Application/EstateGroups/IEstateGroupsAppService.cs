namespace RealtoCrm.EstateGroups;

using System.Collections.Generic;
using System.Threading.Tasks;
using EstateObjects;
using Models;

public interface IEstateGroupsAppService : IDataCrudAppService<
    int,
    EstateGroup,
    EstateGroupRequestModel,
    EstateGroupRequestModel,
    EstateGroupPaginatedRequestModel,
    EstateGroupResponseModel,
    EstateGroupResponseModel>
{
    Task<EstateGroupResponseModel?> GetByName(string name);

    Task<ICollection<EstateGroupResponseModel>> GetByNameOrId(string? nameOrId);

    Task<IEnumerable<EstateGroupForProjectResponseModel>> GetForProjectAsync(
        EstateGroupsForProjectRequestModel request);

    Task<IEnumerable<EstateGroupForLinkedProjectResponseModel>> GetForLinkedProjectAsync(
        EstateGroupsForLinkedProjectRequestModel request);
}