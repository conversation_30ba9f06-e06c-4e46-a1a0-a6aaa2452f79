namespace RealtoCrm.EstateGroups.Specifications;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using EstateObjects;

public class EstateGroupByTenantsSpecification(IEnumerable<int?> tenantsIds) : Specification<EstateGroup>
{
    public override Expression<Func<EstateGroup, bool>> ToExpression()
        => estateGroup => tenantsIds.Contains(estateGroup.TenantId);
}