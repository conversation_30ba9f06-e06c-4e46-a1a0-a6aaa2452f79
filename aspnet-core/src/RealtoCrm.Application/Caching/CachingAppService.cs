using System;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Runtime.Caching;
using Abp.Runtime.Caching.Memory;
using RealtoCrm.Authorization;
using RealtoCrm.Caching.Dto;

namespace RealtoCrm.Caching;

public class CachingAppService(ICacheManager cacheManager) : RealtoCrmAppServiceBase, ICachingAppService
{
    public ListResultDto<CacheDto> GetAllCaches()
    {
        var caches = cacheManager.GetAllCaches()
            .Select(cache => new CacheDto
            {
                Name = cache.Name
            })
            .ToList();

        return new ListResultDto<CacheDto>(caches);
    }

    public async Task ClearCache(EntityDto<string> input)
    {
        var cache = cacheManager.GetCache(input.Id);
        await cache.ClearAsync();
    }

    public async Task ClearPermissionsCache()
    {
        await this.ClearCache(new EntityDto<string> { Id = "AbpZeroUserPermissions" });
        await this.ClearCache(new EntityDto<string> { Id = "AbpZeroRolePermissions" });
        await this.ClearCache(new EntityDto<string> { Id = "AppImpersonationCache" });
    }

    public async Task ClearAllCaches()
    {
        if (!this.CanClearAllCaches())
        {
            throw new ApplicationException("This method can be used only with Memory Cache!");
        }

        var caches = cacheManager.GetAllCaches();
        foreach (var cache in caches)
        {
            await cache.ClearAsync();
        }
    }

    public bool CanClearAllCaches()
    {
        return cacheManager.GetType() == typeof(AbpMemoryCacheManager);
    }
}