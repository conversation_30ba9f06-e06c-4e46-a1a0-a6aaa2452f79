namespace RealtoCrm.Searches.Specifications;

using System;
using System.Linq.Expressions;

public class SearchByPricePerSquareMetres : Specification<Search>
{
    private readonly int? pricePerSquareMetreFrom;
    private readonly int? pricePerSquareMetreTo;

    public SearchByPricePerSquareMetres(int? pricePerSquareMetreFrom, int? pricePerSquareMetreTo)
    {
        this.pricePerSquareMetreFrom = pricePerSquareMetreFrom;
        this.pricePerSquareMetreTo = pricePerSquareMetreTo;
    }

    protected override bool Include => this.pricePerSquareMetreFrom is not null ||
                                       this.pricePerSquareMetreTo is not null || this.pricePerSquareMetreFrom > 0 ||
                                       this.pricePerSquareMetreTo > 0;

    public override Expression<Func<Search, bool>> ToExpression()
        => search => search.SquareMetrePriceTo != null && search.SquareMetrePriceFrom != null &&
                     (pricePerSquareMetreFrom != null && pricePerSquareMetreTo != null
                         ? search.SquareMetrePriceFrom.Amount >= this.pricePerSquareMetreFrom &&
                           search.SquareMetrePriceTo.Amount <= this.pricePerSquareMetreTo &&
                           this.pricePerSquareMetreTo > search.SquareMetrePriceFrom.Amount
                         : pricePerSquareMetreFrom != null && pricePerSquareMetreTo == null
                             ? search.SquareMetrePriceFrom.Amount >= this.pricePerSquareMetreFrom
                             : search.SquareMetrePriceTo.Amount <= this.pricePerSquareMetreTo);
}