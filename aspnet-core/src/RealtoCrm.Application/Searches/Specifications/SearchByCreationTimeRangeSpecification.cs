namespace RealtoCrm.Searches.Specifications;

using System;
using System.Globalization;
using System.Linq.Expressions;

public class SearchByCreationTimeRangeSpecification : Specification<Search>
{
    private readonly string? dateFrom;
    private readonly string? dateTo;

    public SearchByCreationTimeRangeSpecification(string? dateFrom, string? dateTo)
    {
        this.dateFrom = dateFrom;
        this.dateTo = dateTo;
    }

    protected override bool Include => !string.IsNullOrWhiteSpace(this.dateFrom);

    public override Expression<Func<Search, bool>> ToExpression()
    {
        var isValidDateFrom = DateTime.TryParse(this.dateFrom, out var dateTimeFrom);
        var isValidDateTo = DateTime.TryParse(this.dateTo, out var dateTimeTo);
        
        if (!isValidDateFrom)
        {
            dateTimeFrom = DateTime.ParseExact(this.dateFrom, "dd/MM/yyyy", CultureInfo.InvariantCulture);
        }
        
        if (string.IsNullOrWhiteSpace(this.dateTo))
        {
            var endOfDayUtcFrom = dateTimeFrom.AddDays(1);
            return search => search.CreationTime >= dateTimeFrom && search.CreationTime < endOfDayUtcFrom;
        }
        
        if (!isValidDateTo)
        {
            dateTimeTo = DateTime.ParseExact(this.dateTo, "dd/MM/yyyy", CultureInfo.InvariantCulture);
        }
        
        var endOfDayUtcTo = dateTimeTo.AddDays(1);
        
        return search => search.CreationTime >= dateTimeFrom && search.CreationTime < endOfDayUtcTo;
    }
}