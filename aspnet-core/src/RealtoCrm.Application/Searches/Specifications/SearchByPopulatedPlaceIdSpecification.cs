namespace RealtoCrm.Searches.Specifications;

using System;
using System.Linq;
using System.Linq.Expressions;

public class SearchByPopulatedPlaceIdSpecification(int? populatedPlaceId)
    : Specification<Search>
{
    protected override bool Include => populatedPlaceId != null && populatedPlaceId != 0;

    public override Expression<Func<Search, bool>> ToExpression()
    {
        return search => search.SearchesPopulatedPlaces.Any(sc => sc.PopulatedPlaceId == populatedPlaceId);
    }
}