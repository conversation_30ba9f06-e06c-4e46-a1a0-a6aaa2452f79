namespace RealtoCrm.Searches;

using Abp.EntityFrameworkCore;
using EntityFrameworkCore;
using Models;
using RealtoCrm.DataCrudModels;
using Expressions;

public class SearchNomenclatureOptionsAppService(IDbContextProvider<RealtoCrmDbContext> dbContextProvider, IExpressionsBuilder expressionsBuilder) : DataCrudAppService<
    int,
    SearchNomenclatureOption,
    SearchNomenclatureOptionRequestModel,
    SearchNomenclatureOptionRequestModel,
    PaginatedRequestModel,
    SearchNomenclatureOptionResponseModel,
    SearchNomenclatureOptionResponseModel>(dbContextProvider, expressionsBuilder), ISearchNomenclatureOptionsAppService;