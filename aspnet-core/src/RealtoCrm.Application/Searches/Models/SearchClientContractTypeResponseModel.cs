namespace RealtoCrm.Searches.Models;

using System.Linq;
using AutoMapper;
using Mapping;

public class SearchClientContractTypeResponseModel : IMapFrom<Search>, IMapExplicitly
{
    public int ClientId { get; init; }

    public int? EmployeeId { get; init; }

    public int? ContractTypeId { get; init; }

    public void RegisterMappings(IProfileExpression mapper)
        => mapper
            .CreateMap<Search, SearchClientContractTypeResponseModel>()
            .ForMember(m => m.ContractTypeId, cfg => cfg
                .MapFrom(m => m.SearchesContractTypes
                    .Select(sc => (int?)sc.ContractTypeId)
                    .FirstOrDefault()));
}