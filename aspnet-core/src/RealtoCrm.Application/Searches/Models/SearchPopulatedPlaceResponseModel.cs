using AutoMapper;
using RealtoCrm.Nomenclatures;

namespace RealtoCrm.Searches.Models;

using Mapping;

public class SearchPopulatedPlaceResponseModel : IMapFrom<SearchPopulatedPlace>, IMapExplicitly
{
    public int PopulatedPlaceId { get; init; }

    public string PopulatedPlaceName { get; init; } = default!;

    public bool IsTown { get; init; }

    public void RegisterMappings(IProfileExpression mapper)
        => mapper.CreateMap<SearchPopulatedPlace, SearchPopulatedPlaceResponseModel>()
            .ForMember(p => p.IsTown, cfg => cfg
                .MapFrom(sp => sp.PopulatedPlace.Type == PopulatedPlaceType.Town));
}