namespace RealtoCrm.Searches.Models;

using System;
using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using ContactDetails.Models;
using Mapping;
using Money;

public class SearchShortListingResponseModel : IMapFrom<Search>, IMapExplicitly
{
    private const int PerformedViewingId = 3;
    private const int PlannedViewingId = 2;
    
    public int Id { get; init; }

    public string Type { get; init; } = default!;

    public double AreaFrom { get; init; }

    public double AreaTo { get; init; }
    
    public string? PopulatedPlaceName { get; init; }

    public List<string> Districts { get; init; } = default!;
    
    public MoneyResponseModel? MoneyFrom { get; init; }

    public MoneyResponseModel? MoneyTo { get; init; }
    
    public List<string> EstateTypes { get; init; } = default!;
    
    public DateTime? LastModificationTime { get; set; } = default!;
    
    public DateTime? CreationTime { get; set; } = default!;
    
    public DateTime? LastViewingDate { get; set; }

    public DateTime? NextViewingDate { get; set; }

    public int ViewingsCount { get; set; }
    
    public int ClientId { get; init; }

    public void RegisterMappings(IProfileExpression mapper)
        => mapper
            .CreateMap<Search, SearchShortListingResponseModel>()
            .ForMember(slm => slm.PopulatedPlaceName, cfg => cfg
                .MapFrom(s => s.SearchesPopulatedPlaces.Count > 0
                    ? s.SearchesPopulatedPlaces.First().PopulatedPlace.Name
                    : null))
            .ForMember(slm => slm.Districts, cfg => cfg
                .MapFrom(s => s.SearchesDistricts
                    .Select(sd => sd.District.Name)
                    .ToList()))
            .ForMember(slm => slm.EstateTypes, cfg => cfg
                .MapFrom(s => s.SearchesEstateGroups
                    .Select(x => x.EstateGroup.Name)
                    .ToList()))
            .ForMember(slm => slm.LastViewingDate, cfg => cfg
                .MapFrom(s => s.Viewings.Any( v => v.StatusId == PerformedViewingId)
                    ? s.Viewings.OrderByDescending(v => v.CreationTime).First(x => x.StatusId == 3).CreationTime
                    : (DateTime?)null))
            .ForMember(slm => slm.NextViewingDate, cfg => cfg
                .MapFrom(s => s.Viewings.Any( v => v.StatusId == PlannedViewingId)
                    ? s.Viewings.OrderByDescending(v => v.CreationTime).First(x => x.StatusId == 2).CreationTime
                    : (DateTime?)null));
}