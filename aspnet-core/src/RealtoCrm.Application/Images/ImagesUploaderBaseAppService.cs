namespace RealtoCrm.Images;

using System.Collections.Generic;
using System.Threading.Tasks;
using Abp.AspNetZeroCore.Net;
using Abp.EntityFrameworkCore;
using BlobStorage;
using EntityFrameworkCore;
using Extensions;
using Files.Uploader;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Models;
using static CosherConsts.ContentTypes;
using static CosherConsts.ImageThumbnails;
using static CosherConsts.Watermarks;

public abstract class ImagesUploaderBaseAppService(
    IBlobStorageService blobStorageService,
    IFilesUploaderAppService filesUploaderAppService,
    IImagesProcessorAppService imagesProcessorAppService,
    IDbContextProvider<RealtoCrmDbContext> dbContextProvider) : RealtoCrmAppServiceBase
{
    protected RealtoCrmDbContext Data => dbContextProvider.GetDbContext();

    protected abstract string BlobContainerName { get; }

    protected abstract string GetOriginalImageFileName(int tenantId, int entityId, string fileName);

    protected abstract string GetThumbnailFileName(int tenantId, int entityId, int thumbnailSize, string fileName);

    protected async Task<byte[]> GetWatermarkImageBytesAsync(string tenantName)
    {
        var watermarkImageName = string.Format(WatermarkBlobFileName, tenantName);

        return await blobStorageService.DownloadBlobAsync(
            WatermarksBlobContainerName,
            watermarkImageName);
    }

    protected async Task<Image> ProcessAndUploadImageWithThumbnailsAsync(
        IFormFile image,
        byte[] watermarkImageBytes,
        int tenantId,
        int entityId,
        int imageCategoryId)
    {
        await using var imageStream = image.OpenReadStream();
        using var watermarkImageStream = watermarkImageBytes.ToMemoryStream();

        var processedImages = await imagesProcessorAppService.ProcessAsync(imageStream, watermarkImageStream);

        var originalImageFileName = this.GetOriginalImageFileName(
            tenantId,
            entityId,
            image.FileName);

        var normalThumbnailFileName = this.GetThumbnailFileName(
            tenantId,
            entityId,
            NormalThumbnailMaxWidth,
            image.FileName);

        var mediumThumbnailFileName = this.GetThumbnailFileName(
            tenantId,
            entityId,
            MediumThumbnailMaxWidth,
            image.FileName);

        var smallThumbnailFileName = this.GetThumbnailFileName(
            tenantId,
            entityId,
            SmallThumbnailMaxWidth,
            image.FileName);

        var imageThumbnails = await this.CreateThumbnailsAsync(
            tenantId,
            processedImages,
            normalThumbnailFileName,
            mediumThumbnailFileName,
            smallThumbnailFileName);

        return await this.UploadImageAsync(
            tenantId,
            processedImages.OriginalJpeg,
            originalImageFileName,
            image.Length,
            MimeTypeNames.ImageJpeg,
            imageCategoryId,
            imageThumbnails);
    }

    [NonAction]
    public async Task<ImageThumbnail> UploadThumbnailAsync(
        int tenantId,
        byte[] imageBytes,
        string imageFileName,
        long imageFileSize,
        string imageContentType,
        ThumbnailSize thumbnailSize,
        ThumbnailType thumbnailType)
    {
        var thumbnailSource = await filesUploaderAppService.UploadAsync(
            imageBytes,
            this.BlobContainerName,
            imageFileName,
            imageContentType);

        return new ImageThumbnail
        {
            Name = imageFileName,
            FileSize = imageFileSize,
            Source = thumbnailSource,
            Size = thumbnailSize,
            Type = thumbnailType,
            TenantId = tenantId
        };
    }

    private async Task<IEnumerable<ImageThumbnail>> CreateThumbnailsAsync(
        int tenantId,
        ImagesProcessedResponseModel processedImages,
        string normalThumbnailFileName,
        string mediumThumbnailFileName,
        string smallThumbnailFileName)
    {
        var normalJpegThumbnail = await this.UploadThumbnailAsync(
            tenantId,
            processedImages.NormalThumbnailJpeg,
            normalThumbnailFileName,
            processedImages.NormalThumbnailJpeg.Length,
            MimeTypeNames.ImageJpeg,
            ThumbnailSize.Normal,
            ThumbnailType.Jpeg);

        var normalWebpThumbnail = await this.UploadThumbnailAsync(
            tenantId,
            processedImages.NormalThumbnailWebp,
            normalThumbnailFileName,
            processedImages.NormalThumbnailWebp.Length,
            WebpContentType,
            ThumbnailSize.Normal,
            ThumbnailType.Webp);

        var mediumThumbnailWebp = await this.UploadThumbnailAsync(
            tenantId,
            processedImages.MediumThumbnailWebp,
            mediumThumbnailFileName,
            processedImages.MediumThumbnailWebp.Length,
            WebpContentType,
            ThumbnailSize.Medium,
            ThumbnailType.Webp);

        var smallThumbnailWebp = await this.UploadThumbnailAsync(
            tenantId,
            processedImages.SmallThumbnailWebp,
            smallThumbnailFileName,
            processedImages.SmallThumbnailWebp.Length,
            WebpContentType,
            ThumbnailSize.Small,
            ThumbnailType.Webp);

        return new List<ImageThumbnail>
        {
            normalJpegThumbnail,
            normalWebpThumbnail,
            mediumThumbnailWebp,
            smallThumbnailWebp
        };
    }

    private async Task<Image> UploadImageAsync(
        int tenantId,
        byte[] imageBytes,
        string imageFileName,
        long imageSize,
        string imageContentType,
        int estateImageCategoryId,
        IEnumerable<ImageThumbnail> thumbnails)
    {
        var imageSource = await filesUploaderAppService.UploadAsync(
            imageBytes,
            this.BlobContainerName,
            imageFileName,
            imageContentType);

        var image = new Image
        {
            Name = imageFileName,
            FileSize = imageSize,
            Source = imageSource,
            CategoryId = estateImageCategoryId,
            TenantId = tenantId,
        };

        image.Thumbnails.AddRange(thumbnails);

        return image;
    }
}