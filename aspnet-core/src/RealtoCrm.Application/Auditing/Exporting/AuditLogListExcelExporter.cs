using System.Collections.Generic;
using Abp.Extensions;
using Abp.Runtime.Session;
using Abp.Timing.Timezone;
using RealtoCrm.Auditing.Dto;
using RealtoCrm.DataExporting.Excel.MiniExcel;
using RealtoCrm.Dto;
using RealtoCrm.Storage;

namespace RealtoCrm.Auditing.Exporting;

public class AuditLogListExcelExporter(
    ITimeZoneConverter timeZoneConverter,
    IAbpSession abpSession,
    ITempFileCacheManager tempFileCacheManager) : MiniExcelExcelExporterBase(tempFileCacheManager), IAuditLogListExcelExporter
{
    public FileDto ExportToFile(List<AuditLogListDto> auditLogList)
    {
        var items = new List<Dictionary<string, object>>();

        foreach (var auditLog in auditLogList)
        {
            items.Add(new Dictionary<string, object>()
            {
                { this.L("Time"), timeZoneConverter.Convert(auditLog.ExecutionTime, abpSession.TenantId, abpSession.GetUserId())},
                { this.L("UserName"), auditLog.UserName},
                { this.L("Service"), auditLog.ServiceName},
                { this.L("Action"), auditLog.MethodName},
                { this.L("Parameters"), auditLog.Parameters},
                { this.L("Duration"), auditLog.ExecutionDuration},
                { this.L("IpAddress"), auditLog.ClientIpAddress},
                { this.L("Client"), auditLog.ClientName},
                { this.L("Browser"), auditLog.BrowserInfo},
                { this.L("ErrorState"), auditLog.Exception.IsNullOrEmpty() ? this.L("Success") : auditLog.Exception},
            });
        }

        return this.CreateExcelPackage("AuditLogs.xlsx", items);
    }

    public FileDto ExportToFile(List<EntityChangeListDto> entityChangeList)
    {
        var items = new List<Dictionary<string, object>>();

        foreach (var entityChange in entityChangeList)
        {
            items.Add(new Dictionary<string, object>()
            {
                { this.L("Action"), entityChange.ChangeType.ToString()},
                { this.L("Object"), entityChange.EntityTypeFullName},
                { this.L("UserName"), entityChange.UserName},
                { this.L("Time"), timeZoneConverter.Convert(entityChange.ChangeTime, abpSession.TenantId, abpSession.GetUserId())},
            });
        }

        return this.CreateExcelPackage("DetailedLogs.xlsx", items);
    }
}