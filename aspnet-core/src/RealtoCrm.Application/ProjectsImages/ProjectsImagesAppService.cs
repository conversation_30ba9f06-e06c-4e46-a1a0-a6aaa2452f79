namespace RealtoCrm.ProjectsImages;

using System.Linq;
using System.Threading.Tasks;
using Abp.EntityFrameworkCore;
using BlobStorage;
using EntityFrameworkCore;
using Extensions;
using Files.Uploader;
using Images;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MultiTenancy.Models;
using Projects;
using static CosherConsts.ProjectImages;

public class ProjectsImagesAppService(
    IBlobStorageService blobStorageService,
    IFilesUploaderAppService filesUploaderAppService,
    IImagesProcessorAppService imagesProcessorAppService,
    IDbContextProvider<RealtoCrmDbContext> dbContextProvider)
    : ImagesUploaderBaseAppService(
        blobStorageService,
        filesUploaderAppService,
        imagesProcessorAppService,
        dbContextProvider), IProjectsImagesAppService
{
    protected override string BlobContainerName => ProjectsBlobContainerName;

    protected override string GetOriginalImageFileName(int tenantId, int entityId, string fileName)
        => string.Format(ProjectBlobFileName, tenantId, entityId, fileName);

    protected override string GetThumbnailFileName(int tenantId, int entityId, int thumbnailSize, string fileName)
        => string.Format(ProjectBlobFileNameWithSize, tenantId, entityId, thumbnailSize, fileName);

    [NonAction]
    public async Task UploadImagesAsync(int projectId, int tenantId, int imageCategoryId, IFormFileCollection images)
    {
        var projectTenant = await this.ObjectMapper
            .ProjectTo<TenantIdAndNameResponseModel>(this
                .Data
                .Projects
                .AsNoTracking()
                .Where(p => p.Id == projectId)
                .Select(p => p.Tenant))
            .FirstAsync();

        var hasFeatured = await this
            .Data
            .ProjectsImages
            .AsNoTracking()
            .Where(pi => pi.ProjectId == projectId && !pi.IsDeleted)
            .AnyAsync(pi => pi.IsFeatured);

        var lastImageOrder = await this
            .Data
            .ProjectsImages
            .AsNoTracking()
            .Where(pi => pi.ProjectId == projectId && !pi.IsDeleted)
            .OrderByDescending(pi => pi.Order)
            .Select(pi => pi.Order)
            .FirstOrDefaultAsync();

        var watermarkImageBytes = await this.GetWatermarkImageBytesAsync(projectTenant.Name);

        await images.ForEachAsync(async (image, index) =>
        {
            var imageEntity = await this.ProcessAndUploadImageWithThumbnailsAsync(
                image,
                watermarkImageBytes,
                tenantId,
                projectId,
                imageCategoryId);

            await this.Data.ProjectsImages.AddAsync(new ProjectImage
            {
                Order = ++lastImageOrder,
                ProjectId = projectId,
                IsActive = true,
                IsFeatured = !hasFeatured && index == 0,
                Image = imageEntity,
            });
        });

        await this.Data.SaveChangesAsync();
    }
}