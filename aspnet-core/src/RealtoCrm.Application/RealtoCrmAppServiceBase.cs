using System;
using System.Threading.Tasks;
using Abp.Application.Services;
using Abp.IdentityFramework;
using Abp.MultiTenancy;
using Abp.Runtime.Session;
using Abp.Threading;
using Microsoft.AspNetCore.Identity;
using RealtoCrm.Authorization.Users;
using RealtoCrm.MultiTenancy;

namespace RealtoCrm;

using Abp.Dependency;
using AutoMapper;

/// <summary>
/// Derive your application services from this class.
/// </summary>
public abstract class RealtoCrmAppServiceBase : ApplicationService
{
    public TenantManager TenantManager { get; set; }

    public UserManager UserManager { get; set; }
    
    public new IMapper ObjectMapper => IocManager.Instance.Resolve<IMapper>();

    protected RealtoCrmAppServiceBase()
    {
        this.LocalizationSourceName = RealtoCrmConsts.LocalizationSourceName;
    }

    protected virtual async Task<User> GetCurrentUserAsync()
    {
        var user = await this.UserManager.FindByIdAsync(this.AbpSession.GetUserId().ToString());
        if (user == null)
        {
            throw new Exception("There is no current user!");
        }

        return user;
    }

    protected virtual User GetCurrentUser()
    {
        return AsyncHelper.RunSync(this.GetCurrentUserAsync);
    }

    protected virtual Task<Tenant> GetCurrentTenantAsync()
    {
        using (this.CurrentUnitOfWork.SetTenantId(null))
        {
            return this.TenantManager.GetByIdAsync(this.AbpSession.GetTenantId());
        }
    }

    protected virtual Tenant GetCurrentTenant()
    {
        using (this.CurrentUnitOfWork.SetTenantId(null))
        {
            return this.TenantManager.GetById(this.AbpSession.GetTenantId());
        }
    }

    protected virtual void CheckErrors(IdentityResult identityResult)
    {
        identityResult.CheckErrors(this.LocalizationManager);
    }
}