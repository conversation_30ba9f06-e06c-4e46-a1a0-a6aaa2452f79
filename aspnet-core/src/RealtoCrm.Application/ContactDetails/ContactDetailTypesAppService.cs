namespace RealtoCrm.ContactDetails;

using Abp.EntityFrameworkCore;
using EntityFrameworkCore;
using Models;
using RealtoCrm.DataCrudModels;
using Expressions;

public class ContactDetailTypesAppService(IDbContextProvider<RealtoCrmDbContext> dbContextProvider, IExpressionsBuilder expressionsBuilder) : DataCrudAppService<
    int,
    ContactDetailType,
    ContactDetailTypeRequestModel,
    ContactDetailTypeRequestModel,
    PaginatedRequestModel,
    ContactDetailTypeResponseModel,
    ContactDetailTypeResponseModel>(dbContextProvider, expressionsBuilder), IContactDetailTypesAppService;