using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Threading.Tasks;
using Abp;
using Abp.BackgroundJobs;
using Abp.Configuration;
using Abp.Dependency;
using Abp.Domain.Uow;
using Abp.Localization;
using RealtoCrm.Dto;
using RealtoCrm.Localization;
using RealtoCrm.Notifications;
using RealtoCrm.Storage;

namespace RealtoCrm.Gdpr;

public class UserCollectedDataPrepareJob(
    IBinaryObjectManager binaryObjectManager,
    ITempFileCacheManager tempFileCacheManager,
    IAppNotifier appNotifier,
    ISettingManager settingManager,
    IUnitOfWorkManager unitOfWorkManager)
    : AsyncBackgroundJob<UserIdentifier>, ITransientDependency
{
    public override async Task ExecuteAsync(UserIdentifier args)
    {
        await unitOfWorkManager.WithUnitOfWorkAsync(async () =>
        {
            using (this.UnitOfWorkManager.Current.SetTenantId(args.TenantId))
            {
                var userLanguage = await settingManager.GetSettingValueForUserAsync(
                    LocalizationSettingNames.DefaultLanguage,
                    args.TenantId,
                    args.UserId
                );
                
                var culture = CultureHelper.GetCultureInfoByChecking(userLanguage);

                using (CultureInfoHelper.Use(culture))
                {
                    var files = new List<FileDto>();

                    using (var scope = IocManager.Instance.CreateScope())
                    {
                        var providers = scope.ResolveAll<IUserCollectedDataProvider>();
                        foreach (var provider in providers)
                        {
                            var providerFiles = await provider.GetFiles(args);
                            if (providerFiles.Any())
                            {
                                files.AddRange(providerFiles);
                            }
                        }
                    }

                    var zipFile = new BinaryObject
                    (
                        args.TenantId, this.CompressFiles(files),
                        $"{args.UserId} {DateTime.UtcNow} UserCollectedDataPrepareJob result"
                    );

                    // Save zip file to object manager.
                    await binaryObjectManager.SaveAsync(zipFile);

                    // Send notification to user.
                    await appNotifier.GdprDataPrepared(args, zipFile.Id);
                }
            }
        });
    }

    private byte[] CompressFiles(List<FileDto> files)
    {
        using var outputZipFileStream = new MemoryStream();
        using var zipStream = new ZipArchive(outputZipFileStream, ZipArchiveMode.Create);

        var streams = files
            .Select(file =>
            {
                var fileBytes = tempFileCacheManager.GetFile(file.FileToken);
                var entry = zipStream.CreateEntry(file.FileName);
                    

                using var originalFileStream = new MemoryStream(fileBytes);
                using var zipEntryStream = entry.Open();
            
                return (originalFileStream, zipEntryStream);
            })
            .ToList();

        streams
            .ForEach(streams =>
            {
                var (originalFileStream, zipEntryStream) = streams;

                originalFileStream.CopyTo(zipEntryStream);
            });

        return outputZipFileStream.ToArray();
    }
}