namespace RealtoCrm.Clients.Specifications;

using System;
using System.Globalization;
using System.Linq.Expressions;

public class ClientByCreationTimeSpecification : Specification<Client>
{
    private readonly string? date;

    public ClientByCreationTimeSpecification(string? date) => this.date = date;

    protected override bool Include => !string.IsNullOrWhiteSpace(this.date);

    public override Expression<Func<Client, bool>> ToExpression()
    {
        var isValidDate = DateTime.TryParse(this.date, out var dateTime);

        if (!isValidDate)
        {
            dateTime = DateTime.ParseExact(this.date, "dd/MM/yyyy", CultureInfo.InvariantCulture);
        }

        var endOfDayUtc = dateTime.AddDays(1);
        return client => client.CreationTime >= dateTime && client.CreationTime < endOfDayUtc;
    }
}