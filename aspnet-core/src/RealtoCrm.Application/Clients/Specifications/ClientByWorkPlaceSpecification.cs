using System.Linq;

namespace RealtoCrm.Clients.Specifications;

using System;
using System.Linq.Expressions;

public class ClientByWorkPlaceSpecification(int? workplaceId) : Specification<Client>
{
    protected override bool Include => workplaceId != null;
    
    public override Expression<Func<Client, bool>> ToExpression()
        => client => client.ClientsWorkplaces.Any(w => w.WorkplaceId == workplaceId);
}