namespace RealtoCrm.Clients.Specifications;

using System;
using System.Linq;
using System.Linq.Expressions;
using Expressions;
using static CosherConsts.ContactDetails;

public class ClientByEmailOrPhoneSpecification(string? phoneNumberOrEmail) : Specification<Client>
{
    protected override bool Include => !string.IsNullOrWhiteSpace(phoneNumberOrEmail);

    public override Expression<Func<Client, bool>> ToExpression()
        => phoneNumberOrEmail!.Contains('@')
            ? client => client.ContactDetails
                .Where(ccd => ccd.ContactDetail.Name == EmailContactDetailName)
                .Any(ccd => ccd.Value.ToLower() == phoneNumberOrEmail!.Trim().ToLower())
            : client => client.ContactDetails
                .Where(ccd => ccd.ContactDetail.Name == PhoneContactDetailName)
                .Any(ccd => ExpressionsBuilder.GetPhoneComparisonVariants(phoneNumberOrEmail!).Contains(ccd.Value));
}