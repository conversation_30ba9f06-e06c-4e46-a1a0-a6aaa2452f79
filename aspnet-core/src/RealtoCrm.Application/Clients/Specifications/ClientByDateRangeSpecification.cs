using RealtoCrm.Common.SpecificationHelpers;

namespace RealtoCrm.Clients.Specifications;

using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using Expressions;

public class ClientByDateRangeSpecification(DateTime? startDate, DateTime? endDate)
    : Specification<Client>
{
    protected override bool Include => startDate is not null;

    public override Expression<Func<Client, bool>> ToExpression()
    {
        var predicates = DateSpecificationHelper.BuildDateRangePredicates<Client>(
            startDate, endDate, client => client.CreationTime);

        return ExpressionsHelper.And(predicates.ToArray());
    }
}