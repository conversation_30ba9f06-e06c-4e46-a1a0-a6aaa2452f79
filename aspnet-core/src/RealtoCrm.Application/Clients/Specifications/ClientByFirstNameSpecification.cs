namespace RealtoCrm.Clients.Specifications;

using System;
using System.Linq.Expressions;

public class ClientByFirstNameSpecification : Specification<Client>
{
    private readonly string? name;

    public ClientByFirstNameSpecification(string? name) => this.name = name;

    protected override bool Include => !string.IsNullOrWhiteSpace(this.name);

    public override Expression<Func<Client, bool>> ToExpression()
        => client => client.PersonalData!.FirstName.ToLower() == this.name!.ToLower().Trim();
}