namespace RealtoCrm.Clients.Specifications;

using System;
using System.Linq;
using System.Linq.Expressions;

public class ClientByClientOwnershipSpecification(
    ClientOwnership? clientOwnership,
    long? userId,
    int? departmentId,
    int? teamId)
    : Specification<Client>
{
    protected override bool Include => clientOwnership is not null && userId != null;

    public override Expression<Func<Client, bool>> ToExpression()
    {
        return clientOwnership switch
        {
            ClientOwnership.My => IsOwnedByUser(userId),
            ClientOwnership.Team => IsOwnedByTeamOrDepartment(teamId, departmentId),
            ClientOwnership.All => client => true,
            _ => client => false
        };
    }

    private static Expression<Func<Client, bool>> IsOwnedByUser(long? userId)
        => client => userId != null &&
                     client.ClientsSourceCategories
                         .Any(x => x.EmployeeId != null && x.Employee!.UserAccountId == userId);

    private static Expression<Func<Client, bool>> IsOwnedByTeamOrDepartment(int? teamId, int? departmentId)
    {
        if (teamId != null)
        {
            return client => client.ClientsSourceCategories
                .Any(x => x.EmployeeId != null && x.Employee!.TeamId == teamId);
        }

        if (departmentId != null)
        {
            return client => client.ClientsSourceCategories
                .Any(x => x.EmployeeId != null && x.Employee!.DepartmentId == departmentId);
        }

        return client => false;
    }
}