namespace RealtoCrm.Clients.Specifications;

using System;
using System.Linq.Expressions;

public class ClientByLastNameSpecification: Specification<Client>
{
    private readonly string? name;

    public ClientByLastNameSpecification(string? name) => this.name = name;

    protected override bool Include => !string.IsNullOrWhiteSpace(this.name);

    public override Expression<Func<Client, bool>> ToExpression()
        => client => client.PersonalData!.LastName.ToLower() == this.name!.ToLower().Trim();
}