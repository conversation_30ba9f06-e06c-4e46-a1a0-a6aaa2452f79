namespace RealtoCrm.Clients.Specifications;

using System;
using System.Linq;
using System.Linq.Expressions;


public class ClientByMaritalStatusSpecification(int? maritalStatusId) : Specification<Client>
{
    protected override bool Include => maritalStatusId != null;
    
    public override Expression<Func<Client, bool>> ToExpression()
        => client => client.PersonalData != null && client.PersonalData.MaritalStatusId == maritalStatusId;
}