namespace RealtoCrm.Clients.Specifications;

using System;
using System.Linq.Expressions;

public class ClientByCompanyNameSpecification(string? companyName) : Specification<Client>
{
    protected override bool Include => !string.IsNullOrWhiteSpace(companyName);

    public override Expression<Func<Client, bool>> ToExpression()
        => client => client.LegalEntity != null && 
                     client.LegalEntity.Name!.ToLower() == companyName!.ToLower();
}