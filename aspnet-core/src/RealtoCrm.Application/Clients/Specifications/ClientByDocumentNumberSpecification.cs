namespace RealtoCrm.Clients.Specifications;

using System;
using System.Linq.Expressions;

public class ClientByDocumentNumberSpecification(string? documentNumber) : Specification<Client>
{
    protected override bool Include => !string.IsNullOrWhiteSpace(documentNumber);

    public override Expression<Func<Client, bool>> ToExpression()
        => client => client.PersonalData != null && client.PersonalData.DocumentNumber == documentNumber;
}