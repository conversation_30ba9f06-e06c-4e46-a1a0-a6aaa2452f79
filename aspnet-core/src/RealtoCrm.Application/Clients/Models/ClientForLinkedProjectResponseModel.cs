namespace RealtoCrm.Clients.Models;

using System.Linq;
using AutoMapper;
using Mapping;
using static CosherConsts.ContactDetails;

public class ClientForLinkedProjectResponseModel : IMapFrom<Client>, IMapExplicitly
{
    public int Id { get; init; }

    public string Name { get; init; } = default!;

    public string PhoneNumber { get; init; } = default!;

    public string Email { get; init; } = default!;

    public int TenantId { get; init; }

    public string TenantName { get; init; } = default!;

    public void RegisterMappings(IProfileExpression mapper)
        => mapper
            .CreateMap<Client, ClientForLinkedProjectResponseModel>()
            .ForMember(m => m.Name, cfg => cfg
                .MapFrom(m => m.LegalEntity!.Name))
            .ForMember(m => m.PhoneNumber, cfg => cfg
                .MapFrom(m => m.ContactDetails
                    .Where(cd => cd.ContactDetail.Name == PhoneContactDetailName)
                    .Select(cd => cd.Value)
                    .FirstOrDefault()))
            .ForMember(m => m.Email, cfg => cfg
                .MapFrom(m => m.ContactDetails
                    .Where(cd => cd.ContactDetail.Name == EmailContactDetailName)
                    .Select(cd => cd.Value)
                    .FirstOrDefault()));
}