namespace RealtoCrm.Clients.Models;

using System;

public class ContactAdvancedFiltersFilteringModel
{
    public int? Gender { get; set; }
    
    public int? Title { get; set; }
    
    public int? Nationality { get; set; }
    
    public int? CardType { get; set; }
    
    public int[]? Preference { get; set; }
    
    public int? JobPosition { get; set; }
    
    public int? Workplace { get; set; }
    
    public int? MaritalStatus { get; set; }
    
    public int[]? Sectors { get; set; }

    public string? IdentificationNumber { get; set; }
    
    public string? DocumentNumber { get; set; }

    public DateTime? BirthDateFrom { get; set; }
    
    public DateTime? BirthDateTo { get; set; }
    
    public DateTime? DocumentIssueDateFrom { get; set; }
    
    public DateTime? DocumentIssueDateTo { get; set; }
}