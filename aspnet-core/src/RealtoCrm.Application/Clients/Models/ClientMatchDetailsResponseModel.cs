namespace RealtoCrm.Clients.Models;

using System.Linq;
using AutoMapper;
using ContactDetails.Models;
using Mapping;

public class ClientMatchDetailsResponseModel : IMapFrom<Client>, IMapExplicitly
{
    public int Id { get; init; }

    public string FirstName { get; init; } = default!;

    public string LastName { get; init; } = default!;

    public string Email { get; init; } = default!;

    public string PhoneNumber { get; init; } = default!;

    public bool IsRelatedWithMe { get; set; }

    public void RegisterMappings(IProfileExpression mapper)
        => mapper
            .CreateMap<Client, ClientMatchDetailsResponseModel>()
            .ForMember(m => m.FirstName, cfg => cfg
                .MapFrom(m => m.PersonalData != null
                    ? m.PersonalData.FirstName
                    : string.Empty))
            .ForMember(m => m.LastName, cfg => cfg
                .MapFrom(m => m.PersonalData != null
                    ? m.PersonalData.LastName
                    : string.Empty))
            .ForMember(m => m.Email, cfg => cfg
                .MapFrom(m =>
                    m.ContactDetails.FirstOrDefault(cd => cd.ContactDetailId == (int)ContactDetailId.Email) != null
                        ? m.ContactDetails.First(cd => cd.ContactDetailId == (int)ContactDetailId.Email).Value
                        : string.Empty))
            .ForMember(m => m.PhoneNumber, cfg => cfg
                .MapFrom(m =>
                    m.ContactDetails.FirstOrDefault(cd => cd.ContactDetailId == (int)ContactDetailId.Phone) != null
                        ? m.ContactDetails.First(cd => cd.ContactDetailId == (int)ContactDetailId.Phone).Value
                        : string.Empty));
}