using RealtoCrm.SourceCategories.Models;

namespace RealtoCrm.Clients.Models;

using DataCrudModels;
using System;

public class ClientsPaginatedRequestModel : PaginatedRequestModel
{
    public string? SearchTerm { get; init; }
    
    public string? SearchPhoneOrEmail { get; init; }
    
    public string? UnifiedIdentificationCode { get; init; }
    
    public string? FirstName { get; init; }
    
    public string? LastName { get; init; }
    
    public string? CompanyName { get; init; }
    
    public string? CompanyEic { get; init; }
    
    public bool? IsIndividual { get; init; }
    
    public int? Source { get; init; }
    
    public SourceDetailTypeRequestModel? SourceDetail { get; init; }
    
    public bool? HasSource { get; init; }
    
    public string? Phone { get; init; }
    
    public string? Email { get; init; }
    
    public string? CreationTime { get; init; }

    public string? Type { get; set; }

    public ClientStatus? ClientStatus { get; set; }
    
    public ClientOwnership? ClientOwnership { get; set; }
    
    public DateTime? DateFrom { get; init; }
    
    public DateTime? DateTo { get; init; }
    
    public int? DepartmentId { get; init; }
    
    public ContactAdvancedFiltersFilteringModel? AdvancedFilters { get; set; } = new();
}