namespace RealtoCrm.Clients.Models;

using System;
using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using ContactDetails.Models;
using Mapping;
using Meetings.Models;
using Offers.Models;
using Searches.Models;
using Tenants.Dashboard;
using static CosherConsts.ClientTypes;

public class ClientListingResponseModel : IMapFrom<Client>, IMapExplicitly
{
    public int Id { get; init; }

    public string FirstName { get; init; } = default!;

    public string LastName { get; init; } = default!;

    public string Name { get; init; } = default!;

    public OfferInListResponseModel? Offer { get; set; }

    public byte CommissionPercent => 3;

    public byte Potential => (byte)DashboardRandomDataGenerator.GetRandomInt(0, 10);

    public string ExpiringRights => "След един ден";

    public string Status => "Потенциал";

    public string PhoneNumber { get; init; } = default!;

    public string Email { get; init; } = default!;

    public ICollection<ClientSourceCategoryResponseModel> ClientsSourceCategories { get; init; } =
        new List<ClientSourceCategoryResponseModel>();

    public DateTime CreationTime { get; init; }

    public DateTime? LastContact { get; init; }

    public ICollection<ClientSourceDetailResponseModel> SourceDetails { get; init; } =
        new List<ClientSourceDetailResponseModel>();

    public SearchShortListingResponseModel? Search { get; set; }

    public MeetingResponseModel Meeting { get; set; } = default!;

    public string ClientTypeName { get; init; } = default!;

    public long? CreatorUserId { get; init; }


    public void RegisterMappings(IProfileExpression mapper)
        => mapper.CreateMap<Client, ClientListingResponseModel>()
            .ForMember(clrm => clrm.FirstName, opt
                => opt.MapFrom(c => c.PersonalData!.FirstName))
            .ForMember(clrm => clrm.LastName, opt
                => opt.MapFrom(c => c.PersonalData!.LastName))
            .ForMember(dest => dest.PhoneNumber,
                opt
                    => opt.MapFrom(src =>
                        src.ContactDetails.FirstOrDefault(x => x.ContactDetailId == (int)ContactDetailId.Phone)!
                            .Value))
            .ForMember(dest => dest.Email,
                opt
                    => opt.MapFrom(src =>
                        src.ContactDetails.FirstOrDefault(x => x.ContactDetailId == (int)ContactDetailId.Email)!
                            .Value))
            .ForMember(dest => dest.LastContact, opt
                => opt.MapFrom(src =>
                    src.Calls.Any()
                        ? (DateTime?)src.Calls.OrderByDescending(c => c.CreationTime).FirstOrDefault()!.CreationTime
                        : null))
            .ForMember(dest => dest.Meeting, opt => opt.MapFrom(src
                => src.Meetings.Where(m => m.MeetingStatusId == 2)
                    .OrderByDescending(m => m.Date).FirstOrDefault()))
            .ForMember(dest => dest.Name, opt
                => opt.MapFrom(src => src.Type.Name == LegalClientTypeName
                    ? src.LegalEntity!.Name
                    : null))
            .ForMember(dest => dest.ClientTypeName, opt
                => opt.MapFrom(src => src.Type.Name));
}