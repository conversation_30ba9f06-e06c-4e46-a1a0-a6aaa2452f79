namespace RealtoCrm.Clients.Models;

using System.ComponentModel.DataAnnotations;
using Mapping;
using static RealtoCrm.ModelConstants.Common;

public class ClientContactDetailRequestModel : IMapTo<ClientContactDetail>
{
    public int? Id { get; init; }

    public int ContactDetailId { get; init; } = default!;

    [MaxLength(MaxUrlLength)]
    public string Value { get; init; } = string.Empty;

    public bool IsDefault { get; init; }
}