namespace RealtoCrm.Clients.Models;

using AutoMapper;
using RealtoCrm.Mapping;

public class ClientIdAndEikResponseModel : IMapFrom<Client>, IMapExplicitly
{
    public int Id { get; init; }

    public string? UnifiedIdentificationCode { get; init; } = null!;

    public void RegisterMappings(IProfileExpression mapper)
        => mapper.CreateMap<Client, ClientIdAndEikResponseModel>()
            .ForMember(dest => dest.UnifiedIdentificationCode, opt
                => opt.MapFrom(src =>
                    src.LegalEntity != null && src.LegalEntity.UnifiedIdentificationCode != null
                        ? src.LegalEntity.UnifiedIdentificationCode
                        : null));
}