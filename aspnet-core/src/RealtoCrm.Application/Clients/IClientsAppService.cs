namespace RealtoCrm.Clients;

using System.Threading.Tasks;
using CommentsTasks;
using Models;
using System.Collections.Generic;
using Comments.Models;
using Tasks.Models;

public interface IClientsAppService : ICommentsTasksAppService<
    int,
    Client,
    ClientComment,
    ClientTask,
    ClientRequestModel,
    ClientRequestModel,
    ClientsPaginatedRequestModel,
    ClientDetailsResponseModel,
    ClientListingResponseModel>
{
    Task<ICollection<TaskResponseModel>> GetTasksAsync(int clientId);

    Task<ICollection<CommentResponseModel>> GetCommentsAsync(int clientId);

    Task<ClientMatchDetailsResponseModel?> GetMatchDetailsByPhoneOrEmailAsync(string phoneNumberOrEmail);

    Task AddClientSourcesAsync(ClientSourcesRequestModel request);

    Task UpdateRelatedClientsAsync(int id, IEnumerable<ClientRelatedClientsRequestModel> request);

    Task<ClientByPhoneResponseModel> FindByPhoneAsync(string phone);

    Task<IEnumerable<ClientLegalEntityResponseModel>> GetLegalEntitiesByUnifiedIdentificationCode(
        string? unifiedIdentificationCode, int pageSize);

    Task<ClientShortDetailsResponseModel?> GetDetailsByPhoneOrEmailOrUnifiedIdentificationCodeAsync(
        string phoneNumberOrEmailOrUnifiedIdentificationCode);

    Task<int?> UpdateClientIdentificationNumberOrUnifiedIdentificationCode(ClientProjectUpdateModel model);

    Task<IEnumerable<ContactDetailsExistenceResponseModel>> GetPhoneOrEmailExistenceAsync(IEnumerable<string> phones, int clientId);

    Task<IEnumerable<ClientForRelatedClientResponseModel>> GetListingForRelatedClientAsync(string? phoneNumberOrEmail);

    Task<ClientIdentificationDetailsResponseModel?> GetIdentificationDetailsAsync(int id);

    Task<IEnumerable<ClientForLinkedProjectResponseModel>> GetForLinkedProjectAsync(
        ClientsForLinkedProjectRequestModel request);

    Task<IEnumerable<ClientListingResponseModel>> GetEmployeePersonalClientsByPhoneOrEmail(string? phoneOrEmail);

    Task<IEnumerable<ClientSourceDetailDataResponseModel>> GetAllWithoutPagination();
    
    Task<string?> GetClientTypeByClientIdNameAsync (int clientId);
    
    Task<RelatedCompanyResponseModel> CreateRelatedCompanyAsync(ClientRelatedCompaniesRequestModel request, int clientId, int employeeId);
    
    Task<IEnumerable<ClientIdAndEikResponseModel>> UpdateAndGetCompaniesEikAsync(int id, ClientRequestModel request);
}