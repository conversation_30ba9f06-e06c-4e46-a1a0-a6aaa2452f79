namespace RealtoCrm.Files.Uploader;

using System.Threading.Tasks;
using Abp.UI;
using Azure.Storage.Blobs.Models;
using BlobStorage;
using Extensions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

public class FilesUploaderAppService(IBlobStorageService blobStorageService)
    : RealtoCrmAppServiceBase, IFilesUploaderAppService
{
    private const int MaxFileUploadBytes = 1024 * 1024 * 15;

    [NonAction]
    public async Task<string> UploadAsync(
        IFormFile file,
        string containerName = "files",
        string? fileName = null)
    {
        if (file.Length == 0)
        {
            throw new UserFriendlyException(this.L("File_Empty_Error"));
        }

        if (file.Length > MaxFileUploadBytes)
        {
            throw new UserFriendlyException(this.L("File_SizeLimit_Error"));
        }

        await using var fileStream = file.OpenReadStream();

        return await blobStorageService.UploadFileAsync(
            fileStream,
            fileName ?? file.FileName,
            containerName,
            new BlobHttpHeaders
            {
                ContentType = file.ContentType
            });
    }

    [NonAction]
    public async Task<string> UploadAsync(
        byte[] file,
        string containerName,
        string fileName,
        string contentType)
    {
        if (file.Length == 0)
        {
            throw new UserFriendlyException(this.L("File_Empty_Error"));
        }

        if (file.Length > MaxFileUploadBytes)
        {
            throw new UserFriendlyException(this.L("File_SizeLimit_Error"));
        }

        await using var fileStream = file.ToMemoryStream();

        return await blobStorageService.UploadFileAsync(
            fileStream,
            fileName,
            containerName,
            new BlobHttpHeaders
            {
                ContentType = contentType
            });
    }
}