namespace RealtoCrm.Offers.Specifications;

using System;
using System.Linq.Expressions;
using Common.Models;
using Expressions;

public class OfferByOwnershipStatusSpecification(
    OwnershipStatus? ownershipStatus,
    long? userId,
    int? currentUserEmployeeId = null) : Specification<Offer>
{
    protected override bool Include => ownershipStatus is not null && userId.HasValue;

    public override Expression<Func<Offer, bool>> ToExpression()
        => ownershipStatus switch
        {
            OwnershipStatus.All => IsActiveOrPotentialOffer(),
            OwnershipStatus.Mine => IsMineAndCorrectOffer(userId!.Value),
            OwnershipStatus.Team => IsMyTeamsCorrectOffer(userId!.Value, currentUserEmployeeId),
            OwnershipStatus.Archive => IsArchivedOffer(),
            OwnershipStatus.Free => IsFreeOffer(),
            _ => offer => false
        };

    private static Expression<Func<Offer, bool>> IsActiveOrPotentialOffer()
        => offer => offer.OfferStatusId == (int)OfferStatuses.Potential ||
                    offer.OfferStatusId == (int)OfferStatuses.Active;

    private static Expression<Func<Offer, bool>> IsMineOffer(long userId)
        => offer => offer.Employee!.UserAccount.UserId == userId;

    private static Expression<Func<Offer, bool>> IsDraftOffer()
        => offer => offer.OfferStatusId == (int)OfferStatuses.Draft;

    private static Expression<Func<Offer, bool>> IsMineAndCorrectOffer(long userId)
        => ExpressionsHelper.And(IsMineOffer(userId),
            ExpressionsHelper.Or(IsActiveOrPotentialOffer(),
                IsDraftOffer()));

    private static Expression<Func<Offer, bool>> IsMyTeamsOffer(long userId, int? currentEmployeeId)
        => offer => currentEmployeeId != null &&
                    offer.Employee != null &&
                    offer.Employee.ManagerId != null &&
                    offer.Employee.ManagerId == currentEmployeeId;

    private static Expression<Func<Offer, bool>> IsMyTeamsCorrectOffer(long userId, int? currentEmployeeId)
        => ExpressionsHelper.And(IsMyTeamsOffer(userId, currentEmployeeId),
            ExpressionsHelper.Or(IsActiveOrPotentialOffer(),
                IsDraftOffer()));

    private static Expression<Func<Offer, bool>> IsArchivedOffer()
        => offer => offer.OfferStatusId == (int)OfferStatuses.Archive ||
                    offer.OfferStatusId == (int)OfferStatuses.Deal;

    private static Expression<Func<Offer, bool>> IsFreeOffer()
        => offer => offer.OfferStatusId == (int)OfferStatuses.Potential ||
                    (offer.OfferStatusId == (int)OfferStatuses.Active && offer.ContractTypeId == null);
}