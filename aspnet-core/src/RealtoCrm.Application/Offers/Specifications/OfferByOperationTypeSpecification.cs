namespace RealtoCrm.Offers.Specifications;

using System;
using System.Linq.Expressions;

public class OfferByOperationTypeSpecification(string? type) : Specification<Offer>
{
    protected override bool Include => !string.IsNullOrWhiteSpace(type);

    public override Expression<Func<Offer, bool>> ToExpression()
        => offer => type != null &&
                    offer.OperationType.Name.ToLower() == type.ToLower();
}