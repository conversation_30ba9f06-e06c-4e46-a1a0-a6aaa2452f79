namespace RealtoCrm.Offers.Specifications;

using System;
using System.Linq;
using System.Linq.Expressions;

public class OfferByEstateFacingDirectionSpecification(int? facingDirectionId) : Specification<Offer>
{
    protected override bool Include => facingDirectionId.HasValue;

    public override Expression<Func<Offer, bool>> ToExpression()
        => offer => offer.Estate.FacingDirections.Any(fd => fd.Id == facingDirectionId);
}