namespace RealtoCrm.Offers.Specifications;

using System;
using System.Linq.Expressions;

public class OfferByPriceSpecification(decimal? priceFrom, decimal? priceTo) : Specification<Offer>
{
    protected override bool Include
        => priceFrom is not null || priceTo is not null || priceFrom > 0 || priceTo > 0;

    public override Expression<Func<Offer, bool>> ToExpression()
        => offer => priceFrom != null && priceTo != null
            ? offer.Price.Amount >= priceFrom && offer.Price.Amount <= priceTo && priceTo > offer.Price.Amount
            : priceFrom != null && priceTo == null
                ? offer.Price.Amount >= priceFrom
                : offer.Price.Amount <= priceTo;
}