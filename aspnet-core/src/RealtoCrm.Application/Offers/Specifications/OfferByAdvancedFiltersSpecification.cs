namespace RealtoCrm.Offers.Specifications;

using System;
using System.Linq;
using System.Linq.Expressions;
using Expressions;
using Models;

public class OfferByAdvancedFiltersSpecification(OfferAdvancedFiltersFilteringModel? filters) : Specification<Offer>
{
    protected override bool Include => filters is not null;

    public override Expression<Func<Offer, bool>> ToExpression() =>
        ExpressionsHelper.And(this.FilterByBedroomsRange(), this.FilterByBathroomsRange(), this.FilterByRoomsRange(),
            this.FilterByTerracesRange(), this.FilterByRegistrationDateRange(), this.FilterByArchiveDateRange(),
            this.FilterByDepartment(), this.FilterByKey(), this.FilterByViewings(), this.FilterByDeposits(),
            this.FilterByDeals(), this.FilterByCondition(), this.FilterByEstateGroup(), this.FilterByConstructionType(),
            this.FilterByCompletionLevel(), this.FilterByLifestyle(), this.FilterByFurniture(), this.FilterByGarages(),
            this.FilterByContractDateRange(), this.FilterByHideOffersInProjects()
        );

    private Expression<Func<Offer, bool>> FilterByBedroomsRange() =>
        offer => (filters.BedroomsFrom == null || offer.Estate.BedroomsCount >= filters.BedroomsFrom) &&
                 (filters.BedroomsTo == null || offer.Estate.BedroomsCount <= filters.BedroomsTo);

    private Expression<Func<Offer, bool>> FilterByBathroomsRange() =>
        offer => (filters.BathroomsFrom == null || offer.Estate.BathroomsCount >= filters.BathroomsFrom) &&
                 (filters.BathroomsTo == null || offer.Estate.BathroomsCount <= filters.BathroomsTo);

    private Expression<Func<Offer, bool>> FilterByRoomsRange() =>
        offer => (filters.RoomsFrom == null || offer.Estate.RoomsCount >= filters.RoomsFrom) &&
                 (filters.RoomsTo == null || offer.Estate.RoomsCount <= filters.RoomsTo);

    private Expression<Func<Offer, bool>> FilterByTerracesRange() =>
        offer => (filters.TerracesCountFrom == null || offer.Estate.TerracesCount >= filters.TerracesCountFrom) &&
                 (filters.TerracesCountTo == null || offer.Estate.TerracesCount <= filters.TerracesCountTo);

    private Expression<Func<Offer, bool>> FilterByDepartment() =>
        offer => filters.DepartmentId == null ||
                 (offer.Employee != null && offer.Employee.DepartmentId == filters.DepartmentId);

    private Expression<Func<Offer, bool>> FilterByKey() =>
        offer => filters.HasKey == null || offer.HasKey == filters.HasKey;

    private Expression<Func<Offer, bool>> FilterByViewings() =>
        offer => filters.HasViewings == null ||
                 (filters.HasViewings == true && offer.Viewings.Any()) ||
                 (filters.HasViewings == false && !offer.Viewings.Any());

    private Expression<Func<Offer, bool>> FilterByDeposits() =>
        offer => filters.HasDeposits == null ||
                 (filters.HasDeposits == true && offer.Deposits.Any()) ||
                 (filters.HasDeposits == false && !offer.Deposits.Any());

    private Expression<Func<Offer, bool>> FilterByDeals() =>
        offer => filters.HasDeal == null ||
                 (filters.HasDeal == true && offer.Deals.Any()) ||
                 (filters.HasDeal == false && !offer.Deals.Any());

    private Expression<Func<Offer, bool>> FilterByCondition() =>
        offer => filters.ConditionIds == null || filters.ConditionIds.Length == 0 ||
                 (offer.Estate.ConditionId != null && filters.ConditionIds.Contains(offer.Estate.ConditionId.Value));

    private Expression<Func<Offer, bool>> FilterByEstateGroup() =>
        offer => filters.EstateGroupIds == null || filters.EstateGroupIds.Length == 0 ||
                 (offer.Estate.EstateGroupId != null &&
                  filters.EstateGroupIds.Contains(offer.Estate.EstateGroupId.Value));

    private Expression<Func<Offer, bool>> FilterByConstructionType() =>
        offer => filters.ConstructionTypeIds == null || filters.ConstructionTypeIds.Length == 0 ||
                 (offer.Estate.ConstructionTypeId != null &&
                  filters.ConstructionTypeIds.Contains(offer.Estate.ConstructionTypeId.Value));

    private Expression<Func<Offer, bool>> FilterByCompletionLevel() =>
        offer => filters.CompletionLevelIds == null || filters.CompletionLevelIds.Length == 0 ||
                 (offer.Estate.CompletionLevelId != null &&
                  filters.CompletionLevelIds.Contains(offer.Estate.CompletionLevelId.Value));

    private Expression<Func<Offer, bool>> FilterByLifestyle() =>
        offer => filters.LifestyleIds == null || filters.LifestyleIds.Length == 0 ||
                 offer.OfferLifestyles.Any(ol => filters.LifestyleIds.Contains(ol.LifestyleId));

    private Expression<Func<Offer, bool>> FilterByFurniture() =>
        offer => filters.FurnitureIds == null || filters.FurnitureIds.Length == 0 ||
                 (offer.FurnitureId != null && filters.FurnitureIds.Contains(offer.FurnitureId.Value));

    private Expression<Func<Offer, bool>> FilterByGarages() =>
        offer => filters.GarageIds == null || filters.GarageIds.Length == 0 ||
                 offer.Garages.Any(g => filters.GarageIds.Contains(g.Id));

    private Expression<Func<Offer, bool>> FilterByContractDateRange() =>
        offer => (filters.ContractDateFrom == null && filters.ContractDateTo == null) ||
                 offer.Contracts.Any(c =>
                     c.SignDate != null &&
                     (filters.ContractDateFrom == null || 
                      (filters.ContractDateTo == null 
                          ? c.SignDate >= filters.ContractDateFrom && c.SignDate < filters.ContractDateFrom.Value.AddDays(1)
                          : c.SignDate >= filters.ContractDateFrom && c.SignDate <= filters.ContractDateTo)));

    private Expression<Func<Offer, bool>> FilterByRegistrationDateRange() =>
        offer => filters.RegistrationDateFrom == null || 
                 (filters.RegistrationDateTo == null 
                     ? offer.CreationTime >= filters.RegistrationDateFrom && offer.CreationTime < filters.RegistrationDateFrom.Value.AddDays(1)
                     : offer.CreationTime >= filters.RegistrationDateFrom && offer.CreationTime <= filters.RegistrationDateTo);

    private Expression<Func<Offer, bool>> FilterByArchiveDateRange() =>
        offer => filters.ArchiveDateFrom == null || 
                 (filters.ArchiveDateTo == null 
                     ? offer.ArchiveDate >= filters.ArchiveDateFrom && offer.ArchiveDate < filters.ArchiveDateFrom.Value.AddDays(1)
                     : offer.ArchiveDate >= filters.ArchiveDateFrom && offer.ArchiveDate <= filters.ArchiveDateTo);
    
    private Expression<Func<Offer, bool>> FilterByHideOffersInProjects() =>
        offer => filters.HideOffersInProjects != true || offer.ProjectId == null;
}