namespace RealtoCrm.Offers.Models;

using System.Linq;
using Addresses.Models;
using AutoMapper;
using Mapping;
using Money;

public class OffersForDealResponseModel : IMapFrom<Offer>, IMapExplicitly
{
    public int Id { get; init; }

    public MoneyResponseModel Price { get; init; } = default!;

    public int OperationTypeId { get; init; }

    public string OperationTypeName { get; init; } = default!;

    public string EstateTypeName { get; init; } = default!;

    public AddressResponseModel EstateAddress { get; init; } = default!;

    public int ClientId { get; init; }

    public string ClientName { get; init; } = default!;

    public int EmployeeId { get; init; }

    public string EmployeeFirstName { get; init; } = default!;

    public string EmployeeLastName { get; init; } = default!;

    public string EmployeePhoneNumber { get; init; } = default!;

    public long? EmployeeUserId { get; init; }

    public string? ImageSource { get; init; }

    public void RegisterMappings(IProfileExpression mapper)
        => mapper
            .CreateMap<Offer, OffersForDealResponseModel>()
            .ForMember(m => m.ClientName, cfg => cfg
                .MapFrom(s => s.Client.PersonalData != null
                    ? $"{s.Client.PersonalData!.FirstName} {s.Client.PersonalData.LastName}"
                    : s.Client.LegalEntity != null
                        ? s.Client.LegalEntity.Name
                        : string.Empty))
            .ForMember(m => m.EmployeeUserId, cfg => cfg
                .MapFrom(m => m.Employee != null
                    ? m.Employee.UserAccount.UserId
                    : (long?)null))
            .ForMember(m => m.ImageSource, cfg => cfg
                .MapFrom(m => m.OffersImages
                    .Where(oi => oi.IsFeatured)
                    .SelectMany(oi => oi.Image.Thumbnails)
                    .Select(t => t.Source)
                    .FirstOrDefault()));
}