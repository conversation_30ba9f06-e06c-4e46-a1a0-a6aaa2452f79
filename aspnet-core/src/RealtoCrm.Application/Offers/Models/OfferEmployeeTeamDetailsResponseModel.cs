namespace RealtoCrm.Offers.Models;

using AutoMapper;
using Mapping;

public class OfferEmployeeTeamDetailsResponseModel : IMapFrom<Offer>, IMapExplicitly
{
    public int? EmployeeTeamId { get; init; }

    public int? EmployeeDepartmentId { get; init; }

    public int? EmployeeDivisionId { get; init; }

    public long? EmployeeUserId { get; init; }

    public void RegisterMappings(IProfileExpression mapper)
        => mapper
            .CreateMap<Offer, OfferEmployeeTeamDetailsResponseModel>()
            .ForMember(m => m.EmployeeUserId, cfg => cfg
                .MapFrom(m => m.Employee != null
                    ? m.Employee.UserAccount.UserId
                    : (long?)null));
}