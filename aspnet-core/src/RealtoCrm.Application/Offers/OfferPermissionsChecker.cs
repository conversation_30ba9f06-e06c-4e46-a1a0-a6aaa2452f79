namespace RealtoCrm.Offers;

using System.Linq;
using System.Threading.Tasks;
using Abp.EntityFrameworkCore;
using Authorization;
using Employees.Models;
using EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Models;

public class OfferPermissionsChecker(IDbContextProvider<RealtoCrmDbContext> dbContextProvider)
    : RealtoCrmAppServiceBase, IOfferPermissionsChecker
{
    private RealtoCrmDbContext Data => dbContextProvider.GetDbContext();

    public async Task<bool> IsEditGrantedAsync(int offerId, long userId)
    {
        var hasAdministrationFullAccessPermission = await this.PermissionChecker.IsGrantedAsync(
            AppPermissions.AdministrationFullAccess);

        if (hasAdministrationFullAccessPermission)
        {
            return true;
        }

        var offer = await this.GetOfferEmployee(offerId);
        var employee = await this.GetEmployee(userId);

        var hasOfferDivisionEditPermission =
            offer?.EmployeeDivisionId is not null &&
            await this.PermissionChecker.IsGrantedAsync(AppPermissions.OfferDivisionEdit) &&
            offer.EmployeeDivisionId == employee?.DivisionId;

        if (hasOfferDivisionEditPermission)
        {
            return true;
        }

        var hasOfferDepartmentEditPermission =
            offer?.EmployeeDepartmentId is not null &&
            await this.PermissionChecker.IsGrantedAsync(AppPermissions.OfferDepartmentEdit) &&
            offer.EmployeeDepartmentId == employee?.DepartmentId;

        if (hasOfferDepartmentEditPermission)
        {
            return true;
        }

        var hasOfferTeamEditPermission =
            offer?.EmployeeTeamId is not null &&
            await this.PermissionChecker.IsGrantedAsync(AppPermissions.OfferTeamEdit) &&
            offer.EmployeeTeamId == employee?.TeamId;

        if (hasOfferTeamEditPermission)
        {
            return true;
        }

        var hasOfferUpdatePermission = await this.PermissionChecker.IsGrantedAsync(AppPermissions.OfferUpdate);

        var isEntityEmployeeLoggedInUser =
            offer?.EmployeeUserId is null ||
            offer.EmployeeUserId == this.AbpSession.UserId;

        return hasOfferUpdatePermission && isEntityEmployeeLoggedInUser;
    }

    private async Task<EmployeeTeamDetailsResponseModel?> GetEmployee(long userId)
        => await this.ObjectMapper
            .ProjectTo<EmployeeTeamDetailsResponseModel>(this
                .Data
                .Employees
                .AsNoTracking()
                .Where(e => e.UserAccount.UserId == userId))
            .FirstOrDefaultAsync();

    private async Task<OfferEmployeeTeamDetailsResponseModel?> GetOfferEmployee(int offerId)
        => await this.ObjectMapper
            .ProjectTo<OfferEmployeeTeamDetailsResponseModel>(this
                .Data
                .Offers
                .AsNoTracking()
                .Where(o => o.Id == offerId))
            .FirstOrDefaultAsync();
}