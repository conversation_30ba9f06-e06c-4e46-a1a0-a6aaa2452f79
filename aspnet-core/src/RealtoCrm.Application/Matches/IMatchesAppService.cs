namespace RealtoCrm.Matches;

using System.Threading.Tasks;
using DataCrudModels;
using Microsoft.AspNetCore.Mvc;
using Models;

public interface IMatchesAppService : IDataCrudAppService<
    int,
    Match,
    MatchRequestModel,
    MatchRequestModel,
    PaginatedRequestModel,
    MatchResponseModel,
    MatchResponseModel>
{
    Task<MatchBySearchAndOfferResponseModel?> GetBySearchAndOfferAsync(int searchId, int offerId);

    Task<FileContentResult> DownloadGuaranteeContractAsync(int id);
}