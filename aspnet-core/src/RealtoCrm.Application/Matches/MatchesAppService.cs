namespace RealtoCrm.Matches;

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Abp.Domain.Entities;
using Abp.EntityFrameworkCore;
using DataCrudModels;
using EntityFrameworkCore;
using Expressions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Models;
using Providers;
using Word;

public class MatchesAppService(
    IDateTimeService dateTimeService,
    IWordDocumentService wordDocumentService,
    IDbContextProvider<RealtoCrmDbContext> dbContextProvider,
    IExpressionsBuilder expressionsBuilder)
    : DataCrudAppService<
        int,
        Match,
        MatchRequestModel,
        MatchRequestModel,
        PaginatedRequestModel,
        MatchResponseModel,
        MatchResponseModel>(dbContextProvider, expressionsBuilder), IMatchesAppService
{
    private const string GuaranteeDocumentTemplatePath = "Templates/Договор за Гаранция.docx";

    public async Task<MatchBySearchAndOfferResponseModel?> GetBySearchAndOfferAsync(int searchId, int offerId)
        => await this.ObjectMapper
            .ProjectTo<MatchBySearchAndOfferResponseModel>(this
                .AllAsNoTracking()
                .Where(m => m.SearchId == searchId && m.OfferId == offerId))
            .FirstOrDefaultAsync();

    public async Task<FileContentResult> DownloadGuaranteeContractAsync(int id)
    {
        var match = await this.GetGuaranteeContractDetailsAsync(id);

        if (match is null)
        {
            throw new EntityNotFoundException($"Match with id '{id}' is not found.");
        }

        var replacements = new Dictionary<string, string>
        {
            { "{{DateTime}}", dateTimeService.Now.ToString("dd.MM.yyyy") },
            { "{{ClientName}}", match.SearchClientName },
            { "{{ClientIdentificationNumber}}", match.SearchClientIdentificationNumber },
            { "{{ClientAddress}}", match.SearchClientAddress },
            { "{{EmployeeName}}", match.SearchEmployeeName },
            { "{{EmployeeIdentificationNumber}}", match.SearchEmployeeIdentificationNumber },
            { "{{DepositAmount}}", match.DepositAmount },
        };

        var templatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, GuaranteeDocumentTemplatePath);

        var file = await wordDocumentService.FillTemplateAsync(templatePath, replacements);

        return new FileContentResult(file.Content, file.ContentType)
        {
            FileDownloadName = file.FileName
        };
    }

    private async Task<MatchGuaranteeContractResponseModel?> GetGuaranteeContractDetailsAsync(int id)
        => await this.ObjectMapper
            .ProjectTo<MatchGuaranteeContractResponseModel>(this
                .AllAsNoTracking()
                .IgnoreQueryFilters()
                .Where(m => m.Id == id))
            .FirstOrDefaultAsync();
}