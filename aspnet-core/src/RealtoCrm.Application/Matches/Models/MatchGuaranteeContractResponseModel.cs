namespace RealtoCrm.Matches.Models;

using System.Linq;
using AutoMapper;
using Mapping;
using static CosherConsts.Clients;
using static CosherConsts.DepositStatuses;

public class MatchGuaranteeContractResponseModel : IMapFrom<Match>, IMapExplicitly
{
    public string SearchClientName { get; init; } = default!;

    public string SearchClientIdentificationNumber { get; init; } = default!;

    public string SearchClientAddress { get; init; } = default!;

    public string SearchEmployeeName { get; init; } = default!;

    public string SearchEmployeeIdentificationNumber { get; init; } = default!;

    public string DepositAmount { get; init; } = default!;

    public void RegisterMappings(IProfileExpression mapper)
        => mapper
            .CreateMap<Match, MatchGuaranteeContractResponseModel>()
            .ForMember(m => m.SearchClientName, cfg => cfg
                .MapFrom(m => $"{m.Search.Client.PersonalData!.FirstName} {m.Search.Client.PersonalData.LastName}"))
            .ForMember(m => m.SearchClientName, cfg => cfg
                .MapFrom(m => m.Search.Client.TypeId == PersonalClientId && m.Search.Client.PersonalData != null
                    ? $"{m.Search.Client.PersonalData.FirstName} {m.Search.Client.PersonalData.MiddleName} {m.Search.Client.PersonalData.LastName}"
                    : m.Search.Client.LegalEntity != null
                        ? m.Search.Client.LegalEntity.Name
                        : string.Empty))
            .ForMember(m => m.SearchClientIdentificationNumber, cfg => cfg
                .MapFrom(m => m.Search.Client.TypeId == PersonalClientId && m.Search.Client.PersonalData != null
                    ? m.Search.Client.PersonalData.IdentificationNumber
                    : m.Search.Client.LegalEntity != null
                        ? m.Search.Client.LegalEntity.UnifiedIdentificationCode
                        : string.Empty))
            .ForMember(m => m.SearchClientAddress, cfg => cfg
                .MapFrom(m => m.Search.Client.ClientAddresses.Any()
                    ? m.Search.Client.ClientAddresses
                        .Select(ca =>
                            $"{ca.Address.PopulatedPlace.Name}, {ca.Address.Province.Name}, {ca.Address.Municipality.Name}, {ca.Address.District.Name}, {ca.Address.Street.Name}, {ca.Address.StreetNumber}, {ca.Address.BlockNumber}, {ca.Address.EntranceNumber}, {ca.Address.FloorNumber}, {ca.Address.ApartmentNumber}")
                        .FirstOrDefault()
                    : string.Empty))
            .ForMember(m => m.SearchEmployeeName, cfg => cfg
                .MapFrom(m => m.Search.Employee != null
                    ? $"{m.Search.Employee.FirstName} {m.Search.Employee.MiddleName} {m.Search.Employee.LastName}"
                    : string.Empty))
            .ForMember(m => m.SearchEmployeeIdentificationNumber, cfg => cfg
                .MapFrom(m => m.Search.Employee != null
                    ? m.Search.Employee.IdentificationNumber
                    : string.Empty))
            .ForMember(m => m.DepositAmount, cfg => cfg
                .MapFrom(m => m.Deposits
                    .OrderByDescending(d => d.Id)
                    .Where(d => d.DepositStatus.Name == ActiveDepositStatusName)
                    .Select(d => $"{d.Amount.Amount:0.00} {d.Amount.Currency}")
                    .FirstOrDefault()));
}