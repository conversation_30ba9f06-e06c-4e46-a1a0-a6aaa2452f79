using System;
using System.Collections.Generic;

namespace RealtoCrm.Localization;

public static class FamFamFamFlagsHelper
{
    public static List<string> FlagClassNames = new List<string>
    {
        "famfamfam-flags zw",
        "famfamfam-flags zm",
        "famfamfam-flags za",
        "famfamfam-flags yt",
        "famfamfam-flags ye",
        "famfamfam-flags ws",
        "famfamfam-flags wf",
        "famfamfam-flags wales",
        "famfamfam-flags vu",
        "famfamfam-flags vn",
        "famfamfam-flags vi",
        "famfamfam-flags vg",
        "famfamfam-flags ve",
        "famfamfam-flags vc",
        "famfamfam-flags va",
        "famfamfam-flags uz",
        "famfamfam-flags uy",
        "famfamfam-flags us",
        "famfamfam-flags um",
        "famfamfam-flags ug",
        "famfamfam-flags ua",
        "famfamfam-flags tz",
        "famfamfam-flags tw",
        "famfamfam-flags tv",
        "famfamfam-flags tt",
        "famfamfam-flags tr",
        "famfamfam-flags to",
        "famfamfam-flags tn",
        "famfamfam-flags tm",
        "famfamfam-flags tl",
        "famfamfam-flags tk",
        "famfamfam-flags tj",
        "famfamfam-flags th",
        "famfamfam-flags tg",
        "famfamfam-flags tf",
        "famfamfam-flags td",
        "famfamfam-flags tc",
        "famfamfam-flags sz",
        "famfamfam-flags sy",
        "famfamfam-flags sx",
        "famfamfam-flags sv",
        "famfamfam-flags st",
        "famfamfam-flags ss",
        "famfamfam-flags sr",
        "famfamfam-flags so",
        "famfamfam-flags sn",
        "famfamfam-flags sm",
        "famfamfam-flags sl",
        "famfamfam-flags sk",
        "famfamfam-flags si",
        "famfamfam-flags sh",
        "famfamfam-flags sg",
        "famfamfam-flags se",
        "famfamfam-flags sd",
        "famfamfam-flags scotland",
        "famfamfam-flags sc",
        "famfamfam-flags sb",
        "famfamfam-flags sa",
        "famfamfam-flags rw",
        "famfamfam-flags ru",
        "famfamfam-flags rs",
        "famfamfam-flags ro",
        "famfamfam-flags qa",
        "famfamfam-flags py",
        "famfamfam-flags pw",
        "famfamfam-flags pt",
        "famfamfam-flags ps",
        "famfamfam-flags pr",
        "famfamfam-flags pn",
        "famfamfam-flags pm",
        "famfamfam-flags pl",
        "famfamfam-flags pk",
        "famfamfam-flags ph",
        "famfamfam-flags pg",
        "famfamfam-flags pf",
        "famfamfam-flags pe",
        "famfamfam-flags pa",
        "famfamfam-flags om",
        "famfamfam-flags nz",
        "famfamfam-flags nu",
        "famfamfam-flags nr",
        "famfamfam-flags no",
        "famfamfam-flags bv",
        "famfamfam-flags sj",
        "famfamfam-flags nl",
        "famfamfam-flags ni",
        "famfamfam-flags ng",
        "famfamfam-flags nf",
        "famfamfam-flags ne",
        "famfamfam-flags nc",
        "famfamfam-flags na",
        "famfamfam-flags mz",
        "famfamfam-flags my",
        "famfamfam-flags mx",
        "famfamfam-flags mw",
        "famfamfam-flags mv",
        "famfamfam-flags mu",
        "famfamfam-flags mt",
        "famfamfam-flags ms",
        "famfamfam-flags mr",
        "famfamfam-flags mq",
        "famfamfam-flags mp",
        "famfamfam-flags mo",
        "famfamfam-flags mn",
        "famfamfam-flags mm",
        "famfamfam-flags ml",
        "famfamfam-flags mk",
        "famfamfam-flags mh",
        "famfamfam-flags mg",
        "famfamfam-flags me",
        "famfamfam-flags md",
        "famfamfam-flags mc",
        "famfamfam-flags ma",
        "famfamfam-flags ly",
        "famfamfam-flags lv",
        "famfamfam-flags lu",
        "famfamfam-flags lt",
        "famfamfam-flags ls",
        "famfamfam-flags lr",
        "famfamfam-flags lk",
        "famfamfam-flags li",
        "famfamfam-flags lc",
        "famfamfam-flags lb",
        "famfamfam-flags la",
        "famfamfam-flags kz",
        "famfamfam-flags ky",
        "famfamfam-flags kw",
        "famfamfam-flags kr",
        "famfamfam-flags kp",
        "famfamfam-flags kn",
        "famfamfam-flags km",
        "famfamfam-flags ki",
        "famfamfam-flags kh",
        "famfamfam-flags kg",
        "famfamfam-flags ke",
        "famfamfam-flags jp",
        "famfamfam-flags jo",
        "famfamfam-flags jm",
        "famfamfam-flags je",
        "famfamfam-flags it",
        "famfamfam-flags is",
        "famfamfam-flags ir",
        "famfamfam-flags iq",
        "famfamfam-flags io",
        "famfamfam-flags in",
        "famfamfam-flags im",
        "famfamfam-flags il",
        "famfamfam-flags ie",
        "famfamfam-flags id",
        "famfamfam-flags hu",
        "famfamfam-flags ht",
        "famfamfam-flags hr",
        "famfamfam-flags hn",
        "famfamfam-flags hk",
        "famfamfam-flags gy",
        "famfamfam-flags gw",
        "famfamfam-flags gu",
        "famfamfam-flags gt",
        "famfamfam-flags gs",
        "famfamfam-flags gr",
        "famfamfam-flags gq",
        "famfamfam-flags gp",
        "famfamfam-flags gn",
        "famfamfam-flags gm",
        "famfamfam-flags gl",
        "famfamfam-flags gi",
        "famfamfam-flags gh",
        "famfamfam-flags gg",
        "famfamfam-flags ge",
        "famfamfam-flags gd",
        "famfamfam-flags gb",
        "famfamfam-flags ga",
        "famfamfam-flags fr",
        "famfamfam-flags gf",
        "famfamfam-flags re",
        "famfamfam-flags mf",
        "famfamfam-flags bl",
        "famfamfam-flags fo",
        "famfamfam-flags fm",
        "famfamfam-flags fk",
        "famfamfam-flags fj",
        "famfamfam-flags fi",
        "famfamfam-flags fam",
        "famfamfam-flags eu",
        "famfamfam-flags et",
        "famfamfam-flags es",
        "famfamfam-flags er",
        "famfamfam-flags england",
        "famfamfam-flags eh",
        "famfamfam-flags eg",
        "famfamfam-flags ee",
        "famfamfam-flags ec",
        "famfamfam-flags dz",
        "famfamfam-flags do",
        "famfamfam-flags dm",
        "famfamfam-flags dk",
        "famfamfam-flags dj",
        "famfamfam-flags de",
        "famfamfam-flags cz",
        "famfamfam-flags cy",
        "famfamfam-flags cx",
        "famfamfam-flags cw",
        "famfamfam-flags cv",
        "famfamfam-flags cu",
        "famfamfam-flags cs",
        "famfamfam-flags cr",
        "famfamfam-flags co",
        "famfamfam-flags cn",
        "famfamfam-flags cm",
        "famfamfam-flags cl",
        "famfamfam-flags ck",
        "famfamfam-flags ci",
        "famfamfam-flags cg",
        "famfamfam-flags cf",
        "famfamfam-flags cd",
        "famfamfam-flags cc",
        "famfamfam-flags catalonia",
        "famfamfam-flags ca",
        "famfamfam-flags bz",
        "famfamfam-flags by",
        "famfamfam-flags bw",
        "famfamfam-flags bt",
        "famfamfam-flags bs",
        "famfamfam-flags br",
        "famfamfam-flags bq",
        "famfamfam-flags bo",
        "famfamfam-flags bn",
        "famfamfam-flags bm",
        "famfamfam-flags bj",
        "famfamfam-flags bi",
        "famfamfam-flags bh",
        "famfamfam-flags bg",
        "famfamfam-flags bf",
        "famfamfam-flags be",
        "famfamfam-flags bd",
        "famfamfam-flags bb",
        "famfamfam-flags ba",
        "famfamfam-flags az",
        "famfamfam-flags ax",
        "famfamfam-flags aw",
        "famfamfam-flags au",
        "famfamfam-flags hm",
        "famfamfam-flags at",
        "famfamfam-flags as",
        "famfamfam-flags ar",
        "famfamfam-flags ao",
        "famfamfam-flags an",
        "famfamfam-flags am",
        "famfamfam-flags al",
        "famfamfam-flags ai",
        "famfamfam-flags ag",
        "famfamfam-flags af",
        "famfamfam-flags ae",
        "famfamfam-flags ad",
        "famfamfam-flags np",
        "famfamfam-flags ch"
    };

    public static string GetCountryCode(string famfamFlagName)
    {
        return famfamFlagName.Substring(
            famfamFlagName.LastIndexOf(
                " ",
                StringComparison.OrdinalIgnoreCase) + 1
        );
    }
}