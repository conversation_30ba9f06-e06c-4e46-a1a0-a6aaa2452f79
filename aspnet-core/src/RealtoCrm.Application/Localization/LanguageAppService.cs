using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Extensions;
using Abp.Localization;
using Abp.MultiTenancy;
using Abp.UI;
using RealtoCrm.Authorization;
using RealtoCrm.Localization.Dto;

namespace RealtoCrm.Localization;

[AbpAuthorize]
public class LanguageAppService(
    IApplicationLanguageManager applicationLanguageManager,
    IApplicationLanguageTextManager applicationLanguageTextManager,
    IRepository<ApplicationLanguage> languageRepository,
    IApplicationCulturesProvider applicationCulturesProvider)
    : RealtoCrmAppServiceBase, ILanguageAppService
{
    public async Task<GetLanguagesOutput> GetLanguages()
    {
        var languages =
            (await applicationLanguageManager.GetLanguagesAsync(this.AbpSession.TenantId)).OrderBy(l => l.DisplayName);
        var defaultLanguage = await applicationLanguageManager.GetDefaultLanguageOrNullAsync(this.AbpSession.TenantId);

        return new GetLanguagesOutput(this.ObjectMapper.Map<List<ApplicationLanguageListDto>>(languages),
            defaultLanguage?.Name
        );
    }

    [AbpAuthorize]
    public async Task<GetLanguageForEditOutput> GetLanguageForEdit(NullableIdDto input)
    {
        ApplicationLanguage language = null;
        if (input.Id.HasValue)
        {
            language = await languageRepository.GetAsync(input.Id.Value);
        }

        var output = new GetLanguageForEditOutput();

        //Language
        output.Language = language != null
            ? this.ObjectMapper.Map<ApplicationLanguageEditDto>(language)
            : new ApplicationLanguageEditDto();

        //Language names
        output.LanguageNames = applicationCulturesProvider
            .GetAllCultures()
            .Select(c => new ComboboxItemDto(c.Name, c.EnglishName + " (" + c.Name + ")")
                {IsSelected = output.Language.Name == c.Name})
            .ToList();

        //Flags
        output.Flags = FamFamFamFlagsHelper
            .FlagClassNames
            .OrderBy(f => f)
            .Select(f => new ComboboxItemDto(f, FamFamFamFlagsHelper.GetCountryCode(f))
                {IsSelected = output.Language.Icon == f})
            .ToList();

        return output;
    }

    public async Task CreateOrUpdateLanguage(CreateOrUpdateLanguageInput input)
    {
        if (input.Language.Id.HasValue)
        {
            await this.UpdateLanguageAsync(input);
        }
        else
        {
            await this.CreateLanguageAsync(input);
        }
    }

    public async Task DeleteLanguage(EntityDto input)
    {
        var language = await languageRepository.GetAsync(input.Id);
        await applicationLanguageManager.RemoveAsync(this.AbpSession.TenantId, language.Name);
    }

    [AbpAuthorize]
    public async Task SetDefaultLanguage(SetDefaultLanguageInput input)
    {
        await applicationLanguageManager.SetDefaultLanguageAsync(this.AbpSession.TenantId,
            CultureHelper.GetCultureInfoByChecking(input.Name).Name
        );
    }

    [AbpAuthorize]
    public async Task<PagedResultDto<LanguageTextListDto>> GetLanguageTexts(GetLanguageTextsInput input)
    {
        /* Note: This method is used by SPA without paging, MPA with paging.
         * So, it can both usable with paging or not */

        //Normalize base language name
        if (input.BaseLanguageName.IsNullOrEmpty())
        {
            var defaultLanguage =
                await applicationLanguageManager.GetDefaultLanguageOrNullAsync(this.AbpSession.TenantId);
            if (defaultLanguage == null)
            {
                defaultLanguage = (await applicationLanguageManager.GetLanguagesAsync(this.AbpSession.TenantId))
                    .FirstOrDefault();
                if (defaultLanguage == null)
                {
                    throw new Exception("No language found in the application!");
                }
            }

            input.BaseLanguageName = defaultLanguage.Name;
        }

        var source = this.LocalizationManager.GetSource(input.SourceName);
        var baseCulture = CultureInfo.GetCultureInfo(input.BaseLanguageName);
        var targetCulture = CultureInfo.GetCultureInfo(input.TargetLanguageName);

        var allStrings = source.GetAllStrings();
        var baseValues = applicationLanguageTextManager.GetStringsOrNull(this.AbpSession.TenantId,
            source.Name,
            baseCulture,
            allStrings.Select(x => x.Name).ToList()
        );

        var targetValues = applicationLanguageTextManager.GetStringsOrNull(this.AbpSession.TenantId,
            source.Name,
            targetCulture,
            allStrings.Select(x => x.Name).ToList()
        );

        var languageTexts = allStrings.Select((t, i) => new LanguageTextListDto
        {
            Key = t.Name,
            BaseValue = this.GetValueOrNull(baseValues, i),
            TargetValue = this.GetValueOrNull(targetValues, i) ?? this.GetValueOrNull(baseValues, i) 
        }).AsQueryable();

        //Filters
        if (input.TargetValueFilter == "EMPTY")
        {
            languageTexts = languageTexts.Where(s => s.TargetValue.IsNullOrEmpty());
        }

        if (!input.FilterText.IsNullOrEmpty())
        {
            languageTexts = languageTexts.Where(
                l => (l.Key != null &&
                      l.Key.IndexOf(input.FilterText, StringComparison.CurrentCultureIgnoreCase) >= 0) ||
                     (l.BaseValue != null &&
                      l.BaseValue.IndexOf(input.FilterText, StringComparison.CurrentCultureIgnoreCase) >= 0) ||
                     (l.TargetValue != null &&
                      l.TargetValue.IndexOf(input.FilterText, StringComparison.CurrentCultureIgnoreCase) >= 0)
            );
        }

        var totalCount = languageTexts.Count();

        //Ordering
        if (!input.Sorting.IsNullOrEmpty())
        {
            languageTexts = languageTexts.OrderBy(input.Sorting);
        }

        //Paging
        if (input.SkipCount > 0)
        {
            languageTexts = languageTexts.Skip(input.SkipCount);
        }

        if (input.MaxResultCount > 0)
        {
            languageTexts = languageTexts.Take(input.MaxResultCount);
        }

        return new PagedResultDto<LanguageTextListDto>(
            totalCount,
            languageTexts.ToList()
        );
    }

    [AbpAuthorize]
    public async Task UpdateLanguageText(UpdateLanguageTextInput input)
    {
        var culture = CultureHelper.GetCultureInfoByChecking(input.LanguageName);
        var source = this.LocalizationManager.GetSource(input.SourceName);
        await applicationLanguageTextManager.UpdateStringAsync(this.AbpSession.TenantId,
            source.Name,
            culture,
            input.Key,
            input.Value
        );
    }

    [AbpAuthorize]
    protected virtual async Task CreateLanguageAsync(CreateOrUpdateLanguageInput input)
    {
        if (this.AbpSession.MultiTenancySide != MultiTenancySides.Host)
        {
            throw new UserFriendlyException(this.L("TenantsCannotCreateLanguage"));
        }

        var culture = CultureHelper.GetCultureInfoByChecking(input.Language.Name);

        await this.CheckLanguageIfAlreadyExists(culture.Name);

        await applicationLanguageManager.AddAsync(
            new ApplicationLanguage(this.AbpSession.TenantId,
                culture.Name,
                culture.DisplayName,
                input.Language.Icon
            )
            {
                IsDisabled = !input.Language.IsEnabled
            }
        );
    }

    [AbpAuthorize]
    protected virtual async Task UpdateLanguageAsync(CreateOrUpdateLanguageInput input)
    {
        Debug.Assert(input.Language.Id != null, "input.Language.Id != null");

        var culture = CultureHelper.GetCultureInfoByChecking(input.Language.Name);

        await this.CheckLanguageIfAlreadyExists(culture.Name, input.Language.Id.Value);

        var language = await languageRepository.GetAsync(input.Language.Id.Value);

        language.Name = culture.Name;
        language.DisplayName = culture.DisplayName;
        language.Icon = input.Language.Icon;
        language.IsDisabled = !input.Language.IsEnabled;

        await applicationLanguageManager.UpdateAsync(this.AbpSession.TenantId, language);
    }

    private async Task CheckLanguageIfAlreadyExists(string languageName, int? expectedId = null)
    {
        var existingLanguage = (await applicationLanguageManager.GetLanguagesAsync(this.AbpSession.TenantId))
            .FirstOrDefault(l => l.Name == languageName);

        if (existingLanguage == null)
        {
            return;
        }

        if (expectedId != null && existingLanguage.Id == expectedId.Value)
        {
            return;
        }

        throw new UserFriendlyException(this.L("ThisLanguageAlreadyExists"));
    }

    private string GetValueOrNull(List<string> items, int index)
    {
        return items.Count > index ? items[index] : null;
    }
}