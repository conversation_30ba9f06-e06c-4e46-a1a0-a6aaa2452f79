namespace RealtoCrm.Tags;

using System.Collections.Generic;
using System.Threading.Tasks;
using DataCrudModels;
using Models;

public interface ITagsAppService : IDataCrudAppService<
    int,
    Tag,
    TagsRequestModel,
    TagsRequestModel,
    PaginatedRequestModel,
    TagsDetailsResponseModel,
    TagsListingResponseModel>
{
    Task<IEnumerable<TagResponseModel>> GetAllByNamesAndCategories(
        IEnumerable<string> names,
        IEnumerable<int> categoriesIds);
}