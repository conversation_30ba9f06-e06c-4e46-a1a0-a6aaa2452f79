using System;
using System.Threading.Tasks;
using Abp.Application.Services;
using Abp.EntityFrameworkCore;
using Microsoft.AspNetCore.Http;
using RealtoCrm.EmployeeCallsLog.Models;
using RealtoCrm.Calls;
using RealtoCrm.Clients;
using RealtoCrm.Employees;
using RealtoCrm.EntityFrameworkCore;

namespace RealtoCrm.EmployeeCallsLog
{
    public class EmployeeCallsLogAppService(
        IDbContextProvider<RealtoCrmDbContext> dbContextProvider,
        IEmployeesAppService employeesAppService,
        IClientsAppService clientsAppService,
        IHttpContextAccessor accessor
    ) : RealtoCrmAppServiceBase, IApplicationService
    {
        private RealtoCrmDbContext Data => dbContextProvider.GetDbContext();

        public async Task ImportAsync(EmployeeCallsLogRequestModel requestModel)
        {
            //accessor.HttpContext.Request.Headers.TryGetValue("Authorization", out var token); <== check bearer token when implemented here
            
            var employee = await employeesAppService.FindByPhoneAsync(requestModel.UserPhone);
            
            if (employee == null)
            {
                throw new Exception($"Employee with phone number {requestModel.UserPhone} not found.");
            }

            var call = this.ObjectMapper.Map<Call>(requestModel);

            call.ConsultantId = employee.Id;
            
            call.TenantId = employee.TenantId;
            
            call.Category = GetCallCategory(call);
            
            var client = await clientsAppService.FindByPhoneAsync(requestModel.FormattedNumber);

            call.ClientId = client?.Id;
            
            this.Data.Set<Call>().Add(call);
            
            await this.Data.SaveChangesAsync();
        }

        private static CallCategory GetCallCategory(Call call)
        {
            if (call.Inbound && call.Answered)
            {
                return CallCategory.InboundAnswered;
            }
            
            if (call.Inbound && !call.Answered)
            {
                return CallCategory.InboundUnanswered;
            }
            
            if (!call.Inbound && call.Answered)
            {
                return CallCategory.OutboundAnswered;
            }
            
            return CallCategory.OutboundUnanswered;
        }
    }
}