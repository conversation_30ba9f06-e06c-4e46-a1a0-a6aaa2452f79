namespace RealtoCrm.Meetings.Models;

using System;
using AutoMapper;
using Mapping;

public class MeetingResponseModel : IMapFrom<Meeting>, IMapExplicitly
{
    public int Id { get; init; }

    public DateTime Date { get; init; }

    public string Location { get; init; } = default!;

    public int ClientId { get; init; }

    public string ClientFirstName { get; init; } = default!;

    public string ClientLastName { get; init; } = default!;

    public int? MeetingStatusId { get; init; }

    public string? MeetingStatusName { get; init; }

    public int? EmployeeId { get; init; }

    public string? EmployeeFirstName { get; init; }

    public string? EmployeeLastName { get; init; }

    public void RegisterMappings(IProfileExpression mapper)
        => mapper
            .CreateMap<Meeting, MeetingResponseModel>()
            .ForMember(m => m.ClientFirstName, cfg => cfg
                .MapFrom(m => m.Client.PersonalData!.FirstName))
            .ForMember(m => m.ClientLastName, cfg => cfg
                .MapFrom(m => m.Client.PersonalData!.LastName));
}