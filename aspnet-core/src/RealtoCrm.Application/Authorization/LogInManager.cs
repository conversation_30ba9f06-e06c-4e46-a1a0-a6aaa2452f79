using Abp.Authorization;
using Abp.Authorization.Users;
using Abp.Configuration;
using Abp.Configuration.Startup;
using Abp.Dependency;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.Zero.Configuration;
using Microsoft.AspNetCore.Identity;
using RealtoCrm.Authorization.Roles;
using RealtoCrm.Authorization.Users;
using RealtoCrm.MultiTenancy;

namespace RealtoCrm.Authorization;

public class LogInManager(
    UserManager userManager,
    IMultiTenancyConfig multiTenancyConfig,
    IRepository<Tenant> tenantRepository,
    IUnitOfWorkManager unitOfWorkManager,
    ISettingManager settingManager,
    IRepository<UserLoginAttempt, long> userLoginAttemptRepository,
    IUserManagementConfig userManagementConfig,
    IIocResolver iocResolver,
    RoleManager roleManager,
    IPasswordHasher<User> passwordHasher,
    UserClaimsPrincipalFactory claimsPrincipalFactory)
    : AbpLogInManager<Tenant, Role, User>(userManager,
        multiTenancyConfig,
        tenantRepository,
        unitOfWorkManager,
        settingManager,
        userLoginAttemptRepository,
        userManagementConfig,
        iocResolver,
        passwordHasher,
        roleManager,
        claimsPrincipalFactory);