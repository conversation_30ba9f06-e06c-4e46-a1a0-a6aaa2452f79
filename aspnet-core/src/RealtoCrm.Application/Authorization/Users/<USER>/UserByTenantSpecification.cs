namespace RealtoCrm.Authorization.Users.Specifications;

using System;
using System.Linq.Expressions;

public class UserByTenantSpecification(bool onlyInCurrentTenant, bool isAdmin, int? tenantId) : Specification<User>
{
    protected override bool Include => onlyInCurrentTenant;

    public override Expression<Func<User, bool>> ToExpression()
        => user => (isAdmin && tenantId == null) || user.TenantId == tenantId;
}