namespace RealtoCrm.Authorization.Users;

using System.Collections.Generic;
using Dto;
using Employees;
using MultiTenancy;
using MultiTenancy.Models;
using System.Threading.Tasks;
using Models;
using Profile.Dto;

public interface IUserAppService : IDataCrudAppService<
    long,
    User,
    UserCreateRequestModel,
    UserUpdateRequestModel,
    UsersPaginatedRequestModel,
    UserDetailsResponseModel,
    UserListingResponseModel>
{
    Task<IEnumerable<UserActiveDetailsResponseModel>> GetUsersActiveDetailsAsync(IEnumerable<long> userIds);

    Task ResetUserSpecificPermissions(long userId);

    Task UpdateUserPermissions(UpdateUserPermissionsInput input);

    Task DeleteAllCustomPermissions(long userId, int? tenantId);

    Task CreateOrUpdateUser(CreateOrUpdateUserInput input);

    Task UnlockUser(long userId);

    Task ChangeProfilePictureUrlAsync(long userId, string profilePictureUrl);

    Task<User> CreateUserAsync(Tenant tenant, UserCreateRequestModel request);

    Task<User> CreateUserForTenantAsync(int? tenantId, UserCreateRequestModel request);

    Task<(User, Employee?)> CreateWithEmployeeForTenantAsync(
        Tenant? tenant,
        UserCreateRequestModel userRequest,
        EmployeeRequestModel employeeRequest);

    Task<User> UpdateUserAsync(
        int? tenantId,
        long userId,
        UserUpdateRequestModel userRequest);

    Task<IEnumerable<UserProfilePictureResponseModel>> GetProfilePicturesByUsersAsync(IEnumerable<long> userIds);

    Task<long> UpdateUserForProfileAsync(long userId, CurrentUserProfileEditDto editModel);
}