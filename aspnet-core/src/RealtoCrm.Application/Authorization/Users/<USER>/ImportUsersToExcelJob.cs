using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Authorization.Users;
using Abp.BackgroundJobs;
using Abp.Dependency;
using Abp.Domain.Uow;
using Abp.Extensions;
using Abp.IdentityFramework;
using Abp.Localization;
using Abp.ObjectMapping;
using Abp.UI;
using Microsoft.AspNetCore.Identity;
using RealtoCrm.Authorization.Roles;
using RealtoCrm.Authorization.Users.Dto;
using RealtoCrm.Authorization.Users.Importing.Dto;
using RealtoCrm.Notifications;
using RealtoCrm.Storage;

namespace RealtoCrm.Authorization.Users.Importing;

public class ImportUsersToExcelJob(
    RoleManager roleManager,
    IUserListExcelDataReader userListExcelDataReader,
    IInvalidUserExporter invalidUserExporter,
    IUserPolicy userPolicy,
    IEnumerable<IPasswordValidator<User>> passwordValidators,
    IPasswordHasher<User> passwordHasher,
    IAppNotifier appNotifier,
    IBinaryObjectManager binaryObjectManager,
    IObjectMapper objectMapper,
    IUnitOfWorkManager unitOfWorkManager)
    : AsyncBackgroundJob<ImportUsersFromExcelJobArgs>, ITransientDependency
{
    public UserManager UserManager { get; set; }

    public override async Task ExecuteAsync(ImportUsersFromExcelJobArgs args)
    {
        var users = await this.GetUserListFromExcelOrNullAsync(args);
        if (users == null || !users.Any())
        {
            await this.SendInvalidExcelNotificationAsync(args);
            return;
        }

        await this.CreateUsersAsync(args, users);
    }

    private async Task<List<ImportUserDto>> GetUserListFromExcelOrNullAsync(ImportUsersFromExcelJobArgs args)
    {
        using (var uow = unitOfWorkManager.Begin())
        {
            using (this.CurrentUnitOfWork.SetTenantId(args.TenantId))
            {
                try
                {
                    var file = await binaryObjectManager.GetOrNullAsync(args.BinaryObjectId);
                    return userListExcelDataReader.GetUsersFromExcel(file.Bytes);
                }
                catch (Exception)
                {
                    return null;
                }
                finally
                {
                    await uow.CompleteAsync();
                }
            }
        }
    }

    private async Task CreateUsersAsync(ImportUsersFromExcelJobArgs args, List<ImportUserDto> users)
    {
        var invalidUsers = new List<ImportUserDto>();

        foreach (var user in users)
        {
            using (var uow = unitOfWorkManager.Begin())
            {
                using (this.CurrentUnitOfWork.SetTenantId(args.TenantId))
                {
                    if (user.CanBeImported())
                    {
                        try
                        {
                            await this.CreateUserAsync(user);
                        }
                        catch (UserFriendlyException exception)
                        {
                            user.Exception = exception.Message;
                            invalidUsers.Add(user);
                        }
                        catch (Exception exception)
                        {
                            user.Exception = exception.ToString();
                            invalidUsers.Add(user);
                        }
                    }
                    else
                    {
                        invalidUsers.Add(user);
                    }
                }

                await uow.CompleteAsync();
            }
        }

        using (var uow = unitOfWorkManager.Begin())
        {
            using (this.CurrentUnitOfWork.SetTenantId(args.TenantId))
            {
                await this.ProcessImportUsersResultAsync(args, invalidUsers);
            }

            await uow.CompleteAsync();
        }
    }

    private async Task CreateUserAsync(ImportUserDto input)
    {
        var tenantId = this.CurrentUnitOfWork.GetTenantId();

        if (tenantId.HasValue)
        {
            await userPolicy.CheckMaxUserCountAsync(tenantId.Value);
        }

        var user = objectMapper.Map<User>(input); //Passwords is not mapped (see mapping configuration)
        user.Password = input.Password;
        user.TenantId = tenantId;

        if (!input.Password.IsNullOrEmpty())
        {
            await this.UserManager.InitializeOptionsAsync(tenantId);
            foreach (var validator in passwordValidators)
            {
                (await validator.ValidateAsync(this.UserManager, user, input.Password)).CheckErrors();
            }

            user.Password = passwordHasher.HashPassword(user, input.Password);
        }

        user.Roles = new List<UserRole>();
        var roleList = roleManager.Roles.ToList();

        foreach (var roleName in input.AssignedRoleNames)
        {
            var correspondingRoleName = this.GetRoleNameFromDisplayName(roleName, roleList);
            var role = await roleManager.GetRoleByNameAsync(correspondingRoleName);
            user.Roles.Add(new UserRole(tenantId, user.Id, role.Id));
        }

        (await this.UserManager.CreateAsync(user)).CheckErrors();
    }

    private async Task ProcessImportUsersResultAsync(ImportUsersFromExcelJobArgs args,
        List<ImportUserDto> invalidUsers)
    {
        if (invalidUsers.Any())
        {
            var file = invalidUserExporter.ExportToFile(invalidUsers);
            await appNotifier.SomeUsersCouldntBeImported(args.User, file.FileToken, file.FileType, file.FileName);
        }
        else
        {
            await appNotifier.SendMessageAsync(
                args.User,
                new LocalizableString("AllUsersSuccessfullyImportedFromExcel",
                    RealtoCrmConsts.LocalizationSourceName),
                null,
                Abp.Notifications.NotificationSeverity.Success);
        }
    }

    private async Task SendInvalidExcelNotificationAsync(ImportUsersFromExcelJobArgs args)
    {
        using (var uow = unitOfWorkManager.Begin())
        {
            using (this.CurrentUnitOfWork.SetTenantId(args.TenantId))
            {
                await appNotifier.SendMessageAsync(
                    args.User,
                    new LocalizableString(
                        "FileCantBeConvertedToUserList",
                        RealtoCrmConsts.LocalizationSourceName
                    ),
                    null,
                    Abp.Notifications.NotificationSeverity.Warn);
            }

            await uow.CompleteAsync();
        }
    }

    private string GetRoleNameFromDisplayName(string displayName, List<Role> roleList)
    {
        return roleList.FirstOrDefault(
            r => r.DisplayName?.ToLowerInvariant() == displayName?.ToLowerInvariant()
        )?.Name;
    }
}