namespace RealtoCrm.Application.CMA;

using System.Threading.Tasks;
using RealtoCrm.Application.DocumentRenderers;
using System.Collections.Generic;
using System.Linq;
using Abp.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using RealtoCrm.Cma;
using RealtoCrm.CMA.Models.Offers;
using RealtoCrm.EntityFrameworkCore;
using RealtoCrm.Offers;
using RealtoCrm.Offers.Models;
using RealtoCrm.Searches;
using RealtoCrm.Searches.Models;

public class CmaWrapper()
{
    public IEnumerable<OfferForCmaPdfRenderingListingResponseModel> ActiveOffers { get; set; }

    public IEnumerable<OfferForCmaPdfRenderingListingResponseModel> UnfulfilledOffers { get; set; }

    public IEnumerable<OfferForCmaPdfRenderingListingResponseModel> OffersWithDeals { get; set; }
    public IEnumerable<SearchResponseModel> Searches { get; set; }

    public OfferForCmaPdfRenderingDetailsResponseModel BaseOffer { get; set; }
}

public class CmaPdfAppService(
    IPdfRenderer<CmaWrapper> pdfRenderer,
    IDbContextProvider<RealtoCrmDbContext> dbContextProvider)
    : RealtoCrmAppServiceBase
{
    private RealtoCrmDbContext? internalDbContext;

    private DbContext DbContext => this.internalDbContext ??= dbContextProvider.GetDbContext();

    private static IQueryable<Offer> ApplyIncludes(IQueryable<Offer> queryable)
        => queryable
            .Include(x => x.Estate)
            .ThenInclude(x => x.Address)
            .ThenInclude(x => x.Country)
            .Include(x => x.Estate)
            .ThenInclude(x => x.Address)
            .ThenInclude(x => x.PopulatedPlace)
            .Include(x => x.Estate)
            .ThenInclude(x => x.Address)
            .ThenInclude(x => x.District)
            .Include(x => x.Estate)
            .ThenInclude(x => x.Address)
            .ThenInclude(x => x.Street)
            .Include(x => x.OffersImages)
            .ThenInclude(oi => oi.Image);

    public async Task<string> RenderCmaOffersPdf(int cmaAnalysisId)
    {
        // TODO: Think on how to do this in one query
        var cmaAnalysis = await this.DbContext.Set<CmaAnalysis>()
            .Include(x => x.SelectedOffers)
            .Include(x => x.SelectedOffersWithDeals)
            .Include(x => x.SelectedUnfulfilledOffers)
            .Select(x => new
            {
                x.Id,
                x.BaseOfferId,
                SelectedOfferIds = x.SelectedOffers.Select(y => y.Id),
                SelectedOfferIdsWithDeals = x.SelectedOffersWithDeals.Select(y => y.Id),
                SelectedUnfulfilledOfferIds = x.SelectedUnfulfilledOffers.Select(y => y.Id),
                SelectedSearchIds = x.SelectedSearches.Select(y => y.Id),
            })
            .FirstOrDefaultAsync(x => x.Id == cmaAnalysisId);

        var offerIds = cmaAnalysis.SelectedOfferIds
            .Concat(cmaAnalysis.SelectedOfferIdsWithDeals)
            .Concat(cmaAnalysis.SelectedUnfulfilledOfferIds)
            .ToHashSet();

        var offers =
            this.ObjectMapper.Map<List<OfferForCmaPdfRenderingListingResponseModel>>(
                ApplyIncludes(
                        this.DbContext.Set<Offer>()
                            .Where(x => offerIds.Contains(x.Id)))
                    .OrderBy(x => x.Id)
            );

        var searches = this.ObjectMapper.Map<List<SearchResponseModel>>(
            this.DbContext.Set<Search>()
                .Where(x => cmaAnalysis.SelectedSearchIds.Contains(x.Id)));

        var activeOffers = offers.Where(x => cmaAnalysis.SelectedOfferIds.Contains(x.Id));
        var offersWithDeals = offers.Where(x => cmaAnalysis.SelectedOfferIdsWithDeals.Contains(x.Id));
        var unfulfilledOffers = offers.Where(x => cmaAnalysis.SelectedUnfulfilledOfferIds.Contains(x.Id));
        
        var baseOffer = this.ObjectMapper.Map<IEnumerable<OfferForCmaPdfRenderingDetailsResponseModel>>(
                ApplyIncludes(
                        this.DbContext.Set<Offer>())
                    .Where(x => x.Id == cmaAnalysis.BaseOfferId))
            .FirstOrDefault();

        var model = new CmaWrapper
        {
            BaseOffer = baseOffer,
            ActiveOffers = activeOffers,
            UnfulfilledOffers = unfulfilledOffers,
            OffersWithDeals = offersWithDeals,
            Searches = searches,
        };

        return await pdfRenderer.Render(model, "Pdfs/Cma");
    }
}