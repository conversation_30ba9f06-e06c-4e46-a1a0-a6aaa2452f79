namespace RealtoCrm.CMA.ValueResolvers;

using System.Linq;
using AutoMapper;
using RealtoCrm.CMA.Models.Searches;
using RealtoCrm.Nomenclatures;
using RealtoCrm.Searches;

public class SearchDistrictByBaseOfferValueResolver : IValueResolver<Search, CmaSearchResponseModel, LocationForSearchResponseModel>
{
    public const string DistrictIdItemsKey = nameof(SearchDistrictByBaseOfferValueResolver);

    public LocationForSearchResponseModel Resolve(Search source, CmaSearchResponseModel destination, LocationForSearchResponseModel destMember, ResolutionContext context)
    {
        var districtId = context.Items.TryGetValue(DistrictIdItemsKey, out var districtIdValue)
            ? (int)districtIdValue
            : throw new System.ArgumentException("DistrictId not found in context items.");

        var district = source.SearchesDistricts.Where(sd => sd.DistrictId == districtId)
            .Select(sd => sd.District)
            .FirstOrDefault();

        return context.Mapper.Map<LocationForSearchResponseModel>(district);
    }
}