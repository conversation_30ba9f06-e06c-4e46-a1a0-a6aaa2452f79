namespace RealtoCrm.CMA.Specifications;

using System;
using System.Linq.Expressions;
using RealtoCrm.Estates;
using RealtoCrm.Offers;

public class OffersByEstateTypeAndCategoriesSpecification(Estate estate) : RealtoCrm.Specification<Offer>
{
    public override Expression<Func<Offer, bool>> ToExpression()
        => o => o.Estate.CategoryId == estate.CategoryId
                && o.Estate.SubcategoryId == estate.SubcategoryId
                && o.Estate.TypeId == estate.TypeId;
}