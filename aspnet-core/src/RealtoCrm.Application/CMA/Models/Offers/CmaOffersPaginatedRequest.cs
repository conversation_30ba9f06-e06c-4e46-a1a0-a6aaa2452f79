namespace RealtoCrm.CMA.Models;

using RealtoCrm.DataCrudModels;

public class CmaOffersPaginatedRequest : PaginatedRequestModel
{
    public int OfferId { get; set; }

    public CmaStep Step { get; set; } = CmaStep.AllOffers;

    // public decimal? MinPrice { get; set; } = null;
    //
    // public decimal? MaxPrice { get; set; } = null;
    //
    // public double? MinArea { get; set; } = null;
    //
    // public double? MaxArea { get; set; } = null;
}