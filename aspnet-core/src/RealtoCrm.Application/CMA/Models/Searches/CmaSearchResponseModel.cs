namespace RealtoCrm.CMA.Models.Searches;

using System;
using AutoMapper;
using RealtoCrm.CMA.ValueResolvers;
using RealtoCrm.Employees.Models;
using RealtoCrm.Mapping;
using RealtoCrm.Money;
using RealtoCrm.Searches;
using RealtoCrm.Searches.Models;
using RealtoCrm.SourceCategories.Models;

public class CmaSearchResponseModel : IMapFrom<Search>, IMapExplicitly
{
    public int Id { get; init; }

    public DateTime CreationTime { get; init; }

    public string Type { get; init; } = default!;

    public double AreaFrom { get; init; }

    public double AreaTo { get; init; }

    public MoneyResponseModel MoneyFrom { get; init; } = default!;

    public MoneyResponseModel MoneyTo { get; init; } = default!;

    public SearchDetailResponseModel? Detail { get; init; }

    public SourceCategoryResponseModel? SourceCategory { get; init; }

    public SourceDetailSearchResponseModel? SourceDetailSearch { get; init; }

    public int? EmployeeId { get; init; }

    public EmployeeForSearchResponseModel? Employee { get; init; }

    public int TenantId { get; init; }

    public EstateTypeForSearchForCmaResponseModel EstateType { get; init; }

    public EstateCategoryForSearchForCmaResponseModel EstateCategory { get; init; }

    public LocationForSearchResponseModel? Location { get; init; }

    public virtual void RegisterMappings(IProfileExpression mapper)
        => mapper.CreateMap<Search, CmaSearchResponseModel>()
            .ForMember(dest => dest.EstateType,
                opt => opt.MapFrom<SearchEstateTypeByBaseOfferValueResolver>())
            .ForMember(dest => dest.EstateCategory,
                opt => opt.MapFrom<SearchEstateCategoryByBaseOfferValueResolver>())
            .ForMember(
                dest => dest.Location,
                opt => opt.MapFrom<SearchDistrictByBaseOfferValueResolver>());
}