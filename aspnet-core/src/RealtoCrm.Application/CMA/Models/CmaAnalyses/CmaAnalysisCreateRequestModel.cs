namespace RealtoCrm.CMA.Models.CmaAnalyses;

using System.Collections.Generic;
using AutoMapper;
using RealtoCrm.Cma;
using RealtoCrm.CMA.ValueResolvers;
using RealtoCrm.Mapping;
using RealtoCrm.Money;

public class CmaCommentForCmaAnalysisCreateRequestModel: IMapTo<CmaComment>
{
    public string Content { get; set; } = default!;
    public CmaCommentType Type { get; set; } = CmaCommentType.Advantage;
}

public class CmaAnalysisCreateRequestModel : IMapTo<CmaAnalysis>, IMapExplicitly
{
    public int? Id { get; set; } = null;

    public CmaAnalysisState State { get; set; } = CmaAnalysisState.Draft;

    public int BaseOfferId { get; set; }

    public IEnumerable<int> SelectedOffersIds { get; set; } = default!;

    public IEnumerable<int> SelectedOffersWithDealsIds { get; set; } = default!;

    public IEnumerable<int> SelectedUnfulfilledOffersIds { get; set; } = default!;

    public IEnumerable<int> SelectedSearchesIds { get; set; } = default!;
    
    public Money CmaPrice { get; set; } = default!;

    public Money PessimisticPrice { get; set; } = default!;

    public Money OptimisticPrice { get; set; } = default!;

    public IEnumerable<CmaCommentForCmaAnalysisCreateRequestModel> Comments { get; set; } = default!;
    
    public void RegisterMappings(IProfileExpression mapper)
        => mapper.CreateMap<CmaAnalysisCreateRequestModel, CmaAnalysis>()
            .ForMember(dest => dest.SelectedOffers,
                opt =>
                    opt.MapFrom(new OffersByIdsValueResolver(nameof(this.SelectedOffersIds))))
            .ForMember(dest => dest.SelectedOffersWithDeals,
                opt =>
                    opt.MapFrom(new OffersByIdsValueResolver(nameof(this.SelectedOffersWithDealsIds))))
            .ForMember(dest => dest.SelectedUnfulfilledOffers,
                opt =>
                    opt.MapFrom(new OffersByIdsValueResolver(nameof(this.SelectedUnfulfilledOffersIds))))
            .ForMember(dest => dest.SelectedSearches,
                opt =>
                    opt.MapFrom<SearchesByIdsValueResolver>());
}