using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Abp.Application.Services;
using Abp.UI;
using RealtoCrm.Authorization.Users;
using RealtoCrm.Editions;
using RealtoCrm.MultiTenancy.Payments.Dto;
using RealtoCrm.MultiTenancy.Payments.Stripe;
using RealtoCrm.MultiTenancy.Payments.Stripe.Dto;
using Stripe;
using Stripe.Checkout;

namespace RealtoCrm.MultiTenancy.Payments;

public class StripePaymentAppService(
    StripeGatewayManager stripeGatewayManager,
    StripePaymentGatewayConfiguration stripePaymentGatewayConfiguration,
    ISubscriptionPaymentRepository subscriptionPaymentRepository,
    ISubscriptionPaymentExtensionDataRepository subscriptionPaymentExtensionDataRepository,
    IPaymentAppService paymentAppService)
    : RealtoCrmAppServiceBase, IStripePaymentAppService
{
    [RemoteService(false)]
    public async Task ConfirmPayment(StripeConfirmPaymentInput input)
    {
        var paymentId = await subscriptionPaymentExtensionDataRepository.GetPaymentIdOrNullAsync(
            StripeGatewayManager.StripeSessionIdSubscriptionPaymentExtensionDataKey,
            input.StripeSessionId
        );

        if (!paymentId.HasValue)
        {
            throw new ApplicationException($"Cannot find any payment with sessionId {input.StripeSessionId}");
        }

        var payment = await subscriptionPaymentRepository.GetAsync(paymentId.Value);

        if (payment.Status != SubscriptionPaymentStatus.NotPaid)
        {
            throw new ApplicationException(
                $"Invalid payment status {payment.Status}, cannot create a charge on stripe !"
            );
        }

        payment.Gateway = SubscriptionPaymentGatewayType.Stripe;

        var newEditionId = payment.EditionId;

        var service = new SessionService();
        var session = await service.GetAsync(input.StripeSessionId);

        if (session.Mode == "payment")
        {
            payment.ExternalPaymentId = session.PaymentIntentId;
        }
        else if (session.Mode == "subscription")
        {
            payment.ExternalPaymentId = session.SubscriptionId;
        }
        else
        {
            throw new ApplicationException(
                $"Unexpected session mode {session.Mode}. 'payment' or 'subscription' expected");
        }

        payment.SetAsPaid();
            
        var tenant = await this.TenantManager.GetByIdAsync(payment.TenantId);
        if (tenant != null && payment.IsRecurring)
        {
            await this.TenantManager.UpdateAsync(tenant);
        }
            
        await this.CurrentUnitOfWork.SaveChangesAsync();

        if (payment.IsProrationPayment())
        {
            await this.ConfirmSubscriptionUpgradeProrationPayment(newEditionId, payment.TenantId);
        }

        await this.CompletePayment(paymentId.Value);
    }

    private async Task ConfirmSubscriptionUpgradeProrationPayment(int newEditionId, int tenantId)
    {
        await stripeGatewayManager.UpdateSubscription(newEditionId, tenantId, true);
    }

    private async Task CompletePayment(long paymentId)
    {
        var payment = await subscriptionPaymentRepository.GetAsync(paymentId);

        switch (payment.EditionPaymentType)
        {
            case EditionPaymentType.BuyNow:
                await paymentAppService.BuyNowSucceed(paymentId);
                break;
            case EditionPaymentType.Upgrade:
                await paymentAppService.UpgradeSucceed(paymentId);
                break;
            case EditionPaymentType.Extend:
                await paymentAppService.ExtendSucceed(paymentId);
                break;
            case EditionPaymentType.NewRegistration:
                await paymentAppService.NewRegistrationSucceed(paymentId);
                break;
            default:
                throw new ApplicationException(
                    $"Unhandled payment type: {payment.EditionPaymentType}. payment(id: {paymentId}) could not be completed.");
        }
    }

    public StripeConfigurationDto GetConfiguration()
    {
        return new StripeConfigurationDto
        {
            PublishableKey = stripePaymentGatewayConfiguration.PublishableKey
        };
    }

    public async Task<SubscriptionPaymentDto> GetPaymentAsync(StripeGetPaymentInput input)
    {
        var paymentId = await subscriptionPaymentExtensionDataRepository.GetPaymentIdOrNullAsync(
            StripeGatewayManager.StripeSessionIdSubscriptionPaymentExtensionDataKey,
            input.StripeSessionId
        );
            
        if (!paymentId.HasValue)
        {
            throw new ApplicationException($"Cannot find any payment with sessionId {input.StripeSessionId}");
        }

        return this.ObjectMapper.Map<SubscriptionPaymentDto>(
            await subscriptionPaymentRepository.GetAsync(paymentId.Value)
        );
    }

    public async Task<string> CreatePaymentSession(StripeCreatePaymentSessionInput input)
    {
        var payment = await subscriptionPaymentRepository.GetAsync(input.PaymentId);

        var paymentTypes = stripePaymentGatewayConfiguration.PaymentMethodTypes;
        var sessionCreateOptions = new SessionCreateOptions
        {
            PaymentMethodTypes = paymentTypes,
            SuccessUrl = input.SuccessUrl + (input.SuccessUrl.Contains("?") ? "&" : "?") +
                         "sessionId={CHECKOUT_SESSION_ID}",
            CancelUrl = input.CancelUrl
        };
            
        if (payment.IsRecurring && !payment.IsProrationPayment())
        {
            var plan = await stripeGatewayManager.GetOrCreatePlanForPayment(input.PaymentId);
                
            sessionCreateOptions.Mode = "subscription";
                
            sessionCreateOptions.LineItems = new List<SessionLineItemOptions>
            {
                new()
                {
                    Price = plan.Id,
                    Quantity = 1
                }
            };
        }
        else
        {
            sessionCreateOptions.Mode = "payment";

            sessionCreateOptions.LineItems = new List<SessionLineItemOptions>
            {
                    
                new()
                {
                    PriceData = new SessionLineItemPriceDataOptions()
                    {
                        UnitAmount = stripeGatewayManager.ConvertToStripePrice(payment.Amount),
                        Currency = RealtoCrmConsts.Currency,
                        ProductData = new SessionLineItemPriceDataProductDataOptions()
                        {
                            Description = payment.Description,
                            Name = StripeGatewayManager.ProductName
                        }
                    },
                    Quantity = 1
                }
            };
        }

        var service = new SessionService();
        var session = await service.CreateAsync(sessionCreateOptions);

        await subscriptionPaymentExtensionDataRepository.SetExtensionDataAsync(
            payment.Id,
            StripeGatewayManager.StripeSessionIdSubscriptionPaymentExtensionDataKey,
            session.Id
        );

        return session.Id;
    }

    public async Task<StripePaymentResultOutput> GetPaymentResult(StripePaymentResultInput input)
    {
        var payment = await subscriptionPaymentRepository.GetAsync(input.PaymentId);
        var sessionId = await subscriptionPaymentExtensionDataRepository.GetExtensionDataAsync(input.PaymentId,
            StripeGatewayManager.StripeSessionIdSubscriptionPaymentExtensionDataKey);

        if (string.IsNullOrEmpty(sessionId))
        {
            throw new UserFriendlyException(this.L("ThereIsNoStripeSessionIdOnPayment", input.PaymentId));
        }

        using (this.CurrentUnitOfWork.SetTenantId(null))
        {
            var tenant = await this.TenantManager.GetByIdAsync(payment.TenantId);
            await stripeGatewayManager.UpdateCustomerDescriptionAsync(sessionId, tenant.TenancyName);
        }
            
        if (payment.Status == SubscriptionPaymentStatus.Completed)
        {
            return new StripePaymentResultOutput
            {
                PaymentDone = true
            };
        }

        return new StripePaymentResultOutput
        {
            PaymentDone = false
        };
    }
}