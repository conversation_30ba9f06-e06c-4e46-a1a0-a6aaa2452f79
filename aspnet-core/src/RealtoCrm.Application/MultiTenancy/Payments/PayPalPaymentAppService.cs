using System.Threading.Tasks;
using RealtoCrm.MultiTenancy.Payments.Paypal;
using RealtoCrm.MultiTenancy.Payments.PayPal;
using RealtoCrm.MultiTenancy.Payments.PayPal.Dto;

namespace RealtoCrm.MultiTenancy.Payments;

public class PayPalPaymentAppService(
    PayPalGatewayManager payPalGatewayManager,
    ISubscriptionPaymentRepository subscriptionPaymentRepository,
    PayPalPaymentGatewayConfiguration payPalPaymentGatewayConfiguration)
    : RealtoCrmAppServiceBase, IPayPalPaymentAppService
{
    public async Task ConfirmPayment(long paymentId, string paypalOrderId)
    {
        var payment = await subscriptionPaymentRepository.GetAsync(paymentId);

        await payPalGatewayManager.CaptureOrderAsync(
            new PayPalCaptureOrderRequestInput(paypalOrderId)
        );

        payment.Gateway = SubscriptionPaymentGatewayType.Paypal;
        payment.ExternalPaymentId = paypalOrderId;
        payment.SetAsPaid();
    }

    public PayPalConfigurationDto GetConfiguration()
    {
        return new PayPalConfigurationDto
        {
            ClientId = payPalPaymentGatewayConfiguration.ClientId,
            DemoUsername = payPalPaymentGatewayConfiguration.DemoUsername,
            DemoPassword = payPalPaymentGatewayConfiguration.DemoPassword,
            DisabledFundings = payPalPaymentGatewayConfiguration.DisabledFundings
        };
    }
}