namespace RealtoCrm.EstateTypes.Specifications;

using System;
using System.Linq.Expressions;
using Estates;

public class EstateTypeByEstateCategorySpecification(int? categoryId) : Specification<EstateType>
{
    protected override bool Include => categoryId is not null or 0;

    public override Expression<Func<EstateType, bool>> ToExpression()
        => estateType => estateType.CategoryId == categoryId;
}