using System;
using System.Collections.Generic;
using Abp.EntityFrameworkCore;
using RealtoCrm.EntityFrameworkCore;
using RealtoCrm.EntityFrameworkCore.Repositories;
using System.Linq;
using Z.EntityFramework.Plus;

namespace RealtoCrm.Authorization.Users;

public class UserRepository(IDbContextProvider<RealtoCrmDbContext> dbContextProvider) : RealtoCrmRepositoryBase<User, long>(dbContextProvider), IUserRepository
{
    public List<long> GetPasswordExpiredUserIds(DateTime passwordExpireDate)
    {
        var context = this.GetContext();

        return (
            from user in this.GetAll()
            let lastRecentPasswordOfUser = context.RecentPasswords
                .Where(rp => rp.UserId == user.Id && rp.TenantId == user.TenantId)
                .OrderByDescending(rp => rp.CreationTime).FirstOrDefault()
            where user.IsActive && !user.ShouldChangePasswordOnNextLogin &&
                  (
                      (lastRecentPasswordOfUser != null &&
                       lastRecentPasswordOfUser.CreationTime <= passwordExpireDate) ||
                      (lastRecentPasswordOfUser == null && user.CreationTime <= passwordExpireDate)
                  )
            select user.Id
        ).Distinct().ToList();
    }

    public void UpdateUsersToChangePasswordOnNextLogin(List<long> userIdsToUpdate)
    {
        this.GetAll()
            .Where(user =>
                user.IsActive &&
                !user.ShouldChangePasswordOnNextLogin &&
                userIdsToUpdate.Contains(user.Id)
            )
            .Update(x => new User { ShouldChangePasswordOnNextLogin = true });
    }
}