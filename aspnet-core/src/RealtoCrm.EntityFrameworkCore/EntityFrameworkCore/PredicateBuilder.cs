using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq.Expressions;
using System.Runtime.CompilerServices;
using JetBrains.Annotations;

namespace RealtoCrm.EntityFrameworkCore;
// Codes below are taken from https://github.com/scottksmith95/LINQKit project.

/// <summary> The Predicate Operator </summary>
public enum PredicateOperator
{
    /// <summary> The "Or" </summary>
    Or,

    /// <summary> The "And" </summary>
    And
}

/// <summary>
/// See http://www.albahari.com/expressions for information and examples.
/// </summary>
public static class PredicateBuilder
{
    private class RebindParameterVisitor(ParameterExpression oldParameter, ParameterExpression newParameter) : ExpressionVisitor
    {
        protected override Expression VisitParameter(ParameterExpression node)
        {
            if (node == oldParameter)
            {
                return newParameter;
            }

            return base.VisitParameter(node);
        }
    }

    /// <summary> Start an expression </summary>
    public static ExpressionStarter<T> New<T>(Expression<Func<T, bool>> expr = null) { return new ExpressionStarter<T>(expr); }

    /// <summary> Create an expression with a stub expression true or false to use when the expression is not yet started. </summary>
    public static ExpressionStarter<T> New<T>(bool defaultExpression) { return new ExpressionStarter<T>(defaultExpression); }

    /// <summary> Always true </summary>
    [Obsolete("Use PredicateBuilder.New() instead.")]
    public static Expression<Func<T, bool>> True<T>() { return new ExpressionStarter<T>(true); }

    /// <summary> Always false </summary>
    [Obsolete("Use PredicateBuilder.New() instead.")]
    public static Expression<Func<T, bool>> False<T>() { return new ExpressionStarter<T>(false); }

    /// <summary> OR </summary>
    public static Expression<Func<T, bool>> Or<T>([NotNull] this Expression<Func<T, bool>> expr1, [NotNull] Expression<Func<T, bool>> expr2)
    {
        var expr2Body = new RebindParameterVisitor(expr2.Parameters[0], expr1.Parameters[0]).Visit(expr2.Body);
        return Expression.Lambda<Func<T, bool>>(Expression.OrElse(expr1.Body, expr2Body), expr1.Parameters);
    }

    /// <summary> AND </summary>
    public static Expression<Func<T, bool>> And<T>([NotNull] this Expression<Func<T, bool>> expr1, [NotNull] Expression<Func<T, bool>> expr2)
    {
        var expr2Body = new RebindParameterVisitor(expr2.Parameters[0], expr1.Parameters[0]).Visit(expr2.Body);
        return Expression.Lambda<Func<T, bool>>(Expression.AndAlso(expr1.Body, expr2Body), expr1.Parameters);
    }

    /// <summary>
    /// Extends the specified source Predicate with another Predicate and the specified PredicateOperator.
    /// </summary>
    /// <typeparam name="T">The type</typeparam>
    /// <param name="first">The source Predicate.</param>
    /// <param name="second">The second Predicate.</param>
    /// <param name="operator">The Operator (can be "And" or "Or").</param>
    /// <returns>Expression{Func{T, bool}}</returns>
    public static Expression<Func<T, bool>> Extend<T>([NotNull] this Expression<Func<T, bool>> first, [NotNull] Expression<Func<T, bool>> second, PredicateOperator @operator = PredicateOperator.Or)
    {
        return @operator == PredicateOperator.Or ? first.Or(second) : first.And(second);
    }

    /// <summary>
    /// Extends the specified source Predicate with another Predicate and the specified PredicateOperator.
    /// </summary>
    /// <typeparam name="T">The type</typeparam>
    /// <param name="first">The source Predicate.</param>
    /// <param name="second">The second Predicate.</param>
    /// <param name="operator">The Operator (can be "And" or "Or").</param>
    /// <returns>Expression{Func{T, bool}}</returns>
    public static Expression<Func<T, bool>> Extend<T>([NotNull] this ExpressionStarter<T> first, [NotNull] Expression<Func<T, bool>> second, PredicateOperator @operator = PredicateOperator.Or)
    {
        return @operator == PredicateOperator.Or ? first.Or(second) : first.And(second);
    }
}

/// <summary>
/// ExpressionStarter{T} which eliminates the default 1=0 or 1=1 stub expressions
/// </summary>
/// <typeparam name="T">The type</typeparam>
public class ExpressionStarter<T>
{
    internal ExpressionStarter() : this(false) { }

    internal ExpressionStarter(bool defaultExpression)
    {
        if (defaultExpression)
            this.DefaultExpression = f => true;
        else
            this.DefaultExpression = f => false;
    }

    internal ExpressionStarter(Expression<Func<T, bool>> exp) : this(false)
    {
        this.predicate = exp;
    }

    /// <summary>The actual Predicate. It can only be set by calling Start.</summary>
    private Expression<Func<T, bool>> Predicate => (this.IsStarted || !this.UseDefaultExpression) ? this.predicate : this.DefaultExpression;

    private Expression<Func<T, bool>> predicate;

    /// <summary>Determines if the predicate is started.</summary>
    public bool IsStarted => this.predicate != null;

    /// <summary> A default expression to use only when the expression is null </summary>
    public bool UseDefaultExpression => this.DefaultExpression != null;

    /// <summary>The default expression</summary>
    public Expression<Func<T, bool>> DefaultExpression { get; set; }

    /// <summary>Set the Expression predicate</summary>
    /// <param name="exp">The first expression</param>
    public Expression<Func<T, bool>> Start(Expression<Func<T, bool>> exp)
    {
        if (this.IsStarted)
            throw new Exception("Predicate cannot be started again.");

        return this.predicate = exp;
    }

    /// <summary>Or</summary>
    public Expression<Func<T, bool>> Or([NotNull] Expression<Func<T, bool>> expr2)
    {
        return (this.IsStarted) ? this.predicate = this.Predicate.Or(expr2) : this.Start(expr2);
    }

    /// <summary>And</summary>
    public Expression<Func<T, bool>> And([NotNull] Expression<Func<T, bool>> expr2)
    {
        return (this.IsStarted) ? this.predicate = this.Predicate.And(expr2) : this.Start(expr2);
    }

    /// <summary> Show predicate string </summary>
    public override string ToString()
    {
        return this.Predicate == null ? null : this.Predicate.ToString();
    }

    #region Implicit Operators
    /// <summary>
    /// Allows this object to be implicitely converted to an Expression{Func{T, bool}}.
    /// </summary>
    /// <param name="right"></param>
    public static implicit operator Expression<Func<T, bool>>(ExpressionStarter<T> right)
    {
        return right == null ? null : right.Predicate;
    }

    /// <summary>
    /// Allows this object to be implicitely converted to an Expression{Func{T, bool}}.
    /// </summary>
    /// <param name="right"></param>
    public static implicit operator Func<T, bool>(ExpressionStarter<T> right)
    {
        return right == null ? null : (right.IsStarted || right.UseDefaultExpression) ? right.Predicate.Compile() : null;
    }

    /// <summary>
    /// Allows this object to be implicitely converted to an Expression{Func{T, bool}}.
    /// </summary>
    /// <param name="right"></param>
    public static implicit operator ExpressionStarter<T>(Expression<Func<T, bool>> right)
    {
        return right == null ? null : new ExpressionStarter<T>(right);
    }
    #endregion

    #region Implement Expression<TDelagate> methods and properties
#if !(NET35)

    /// <summary></summary>
    public Func<T, bool> Compile() { return this.Predicate.Compile(); }
#endif

#if !(NET35 || WINDOWS_APP || NETSTANDARD || PORTABLE || PORTABLE40 || UAP)
    /// <summary></summary>
    public Func<T, bool> Compile(DebugInfoGenerator debugInfoGenerator) { return this.Predicate.Compile(debugInfoGenerator); }

    /// <summary></summary>
    public Expression<Func<T, bool>> Update(Expression body, IEnumerable<ParameterExpression> parameters) { return this.Predicate.Update(body, parameters); }
#endif
    #endregion

    #region Implement LamdaExpression methods and properties

    /// <summary></summary>
    public Expression Body => this.Predicate.Body;


    /// <summary></summary>
    public ExpressionType NodeType => this.Predicate.NodeType;

    /// <summary></summary>
    public ReadOnlyCollection<ParameterExpression> Parameters => this.Predicate.Parameters;

    /// <summary></summary>
    public Type Type => this.Predicate.Type;

#if !(NET35)
    /// <summary></summary>
    public string Name => this.Predicate.Name;

    /// <summary></summary>
    public Type ReturnType => this.Predicate.ReturnType;

    /// <summary></summary>
    public bool TailCall => this.Predicate.TailCall;
#endif

    #endregion

    #region Implement Expression methods and properties
#if !(NET35)
    /// <summary></summary>
    public virtual bool CanReduce => this.Predicate.CanReduce;
#endif
    #endregion
}