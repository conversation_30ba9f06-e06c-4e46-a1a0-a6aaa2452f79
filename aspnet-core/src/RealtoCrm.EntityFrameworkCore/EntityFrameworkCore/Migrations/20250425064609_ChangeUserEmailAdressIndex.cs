using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace RealtoCrm.EntityFrameworkCore.Migrations
{
    /// <inheritdoc />
    public partial class ChangeUserEmailAdressIndex : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_AbpUsers_EmailAddress",
                table: "AbpUsers");

            migrationBuilder.CreateIndex(
                name: "IX_AbpUsers_EmailAddress_TenantId",
                table: "AbpUsers",
                columns: new[] { "EmailAddress", "TenantId" },
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_AbpUsers_EmailAddress_TenantId",
                table: "AbpUsers");

            migrationBuilder.CreateIndex(
                name: "IX_AbpUsers_EmailAddress",
                table: "AbpUsers",
                column: "EmailAddress",
                unique: true);
        }
    }
}
