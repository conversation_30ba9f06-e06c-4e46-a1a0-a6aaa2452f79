using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace RealtoCrm.EntityFrameworkCore.Migrations
{
    /// <inheritdoc />
    public partial class AddedCMA : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "CmaAnalysis",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    State = table.Column<int>(type: "integer", nullable: false),
                    BaseOfferId = table.Column<int>(type: "integer", nullable: false),
                    CreationTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatorUserId = table.Column<long>(type: "bigint", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifierUserId = table.Column<long>(type: "bigint", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    DeleterUserId = table.Column<long>(type: "bigint", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CmaAnalysis", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CmaAnalysis_Offers_BaseOfferId",
                        column: x => x.BaseOfferId,
                        principalTable: "Offers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CmaAnalysesOffers",
                columns: table => new
                {
                    CmaAnalysesForOffersId = table.Column<int>(type: "integer", nullable: false),
                    SelectedOffersId = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CmaAnalysesOffers", x => new { x.CmaAnalysesForOffersId, x.SelectedOffersId });
                    table.ForeignKey(
                        name: "FK_CmaAnalysesOffers_CmaAnalysis_CmaAnalysesForOffersId",
                        column: x => x.CmaAnalysesForOffersId,
                        principalTable: "CmaAnalysis",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CmaAnalysesOffers_Offers_SelectedOffersId",
                        column: x => x.SelectedOffersId,
                        principalTable: "Offers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CmaAnalysesOffersWithDeals",
                columns: table => new
                {
                    CmaAnalysesForOffersWithDealsId = table.Column<int>(type: "integer", nullable: false),
                    SelectedOffersWithDealsId = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CmaAnalysesOffersWithDeals", x => new { x.CmaAnalysesForOffersWithDealsId, x.SelectedOffersWithDealsId });
                    table.ForeignKey(
                        name: "FK_CmaAnalysesOffersWithDeals_CmaAnalysis_CmaAnalysesForOffers~",
                        column: x => x.CmaAnalysesForOffersWithDealsId,
                        principalTable: "CmaAnalysis",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CmaAnalysesOffersWithDeals_Offers_SelectedOffersWithDealsId",
                        column: x => x.SelectedOffersWithDealsId,
                        principalTable: "Offers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CmaAnalysesSearches",
                columns: table => new
                {
                    CmaAnalysesId = table.Column<int>(type: "integer", nullable: false),
                    SelectedSearchesId = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CmaAnalysesSearches", x => new { x.CmaAnalysesId, x.SelectedSearchesId });
                    table.ForeignKey(
                        name: "FK_CmaAnalysesSearches_CmaAnalysis_CmaAnalysesId",
                        column: x => x.CmaAnalysesId,
                        principalTable: "CmaAnalysis",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CmaAnalysesSearches_Searches_SelectedSearchesId",
                        column: x => x.SelectedSearchesId,
                        principalTable: "Searches",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CmaAnalysesUnfulfilledOffers",
                columns: table => new
                {
                    CmaAnalysesForUnfulfilledOffersId = table.Column<int>(type: "integer", nullable: false),
                    SelectedUnfulfilledOffersId = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CmaAnalysesUnfulfilledOffers", x => new { x.CmaAnalysesForUnfulfilledOffersId, x.SelectedUnfulfilledOffersId });
                    table.ForeignKey(
                        name: "FK_CmaAnalysesUnfulfilledOffers_CmaAnalysis_CmaAnalysesForUnfu~",
                        column: x => x.CmaAnalysesForUnfulfilledOffersId,
                        principalTable: "CmaAnalysis",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CmaAnalysesUnfulfilledOffers_Offers_SelectedUnfulfilledOffe~",
                        column: x => x.SelectedUnfulfilledOffersId,
                        principalTable: "Offers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_CmaAnalysesOffers_SelectedOffersId",
                table: "CmaAnalysesOffers",
                column: "SelectedOffersId");

            migrationBuilder.CreateIndex(
                name: "IX_CmaAnalysesOffersWithDeals_SelectedOffersWithDealsId",
                table: "CmaAnalysesOffersWithDeals",
                column: "SelectedOffersWithDealsId");

            migrationBuilder.CreateIndex(
                name: "IX_CmaAnalysesSearches_SelectedSearchesId",
                table: "CmaAnalysesSearches",
                column: "SelectedSearchesId");

            migrationBuilder.CreateIndex(
                name: "IX_CmaAnalysesUnfulfilledOffers_SelectedUnfulfilledOffersId",
                table: "CmaAnalysesUnfulfilledOffers",
                column: "SelectedUnfulfilledOffersId");

            migrationBuilder.CreateIndex(
                name: "IX_CmaAnalysis_BaseOfferId",
                table: "CmaAnalysis",
                column: "BaseOfferId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CmaAnalysesOffers");

            migrationBuilder.DropTable(
                name: "CmaAnalysesOffersWithDeals");

            migrationBuilder.DropTable(
                name: "CmaAnalysesSearches");

            migrationBuilder.DropTable(
                name: "CmaAnalysesUnfulfilledOffers");

            migrationBuilder.DropTable(
                name: "CmaAnalysis");
        }
    }
}
