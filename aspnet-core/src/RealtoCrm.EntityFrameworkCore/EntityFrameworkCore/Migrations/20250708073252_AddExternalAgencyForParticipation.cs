using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace RealtoCrm.EntityFrameworkCore.Migrations
{
    /// <inheritdoc />
    public partial class AddExternalAgencyForParticipation : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "ExternalAgencyId",
                table: "DealParticipations",
                type: "integer",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_DealParticipations_ExternalAgencyId",
                table: "DealParticipations",
                column: "ExternalAgencyId");

            migrationBuilder.AddForeignKey(
                name: "FK_DealParticipations_ExternalAgencies_ExternalAgencyId",
                table: "DealParticipations",
                column: "ExternalAgencyId",
                principalTable: "ExternalAgencies",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_DealParticipations_ExternalAgencies_ExternalAgencyId",
                table: "DealParticipations");

            migrationBuilder.DropIndex(
                name: "IX_DealParticipations_ExternalAgencyId",
                table: "DealParticipations");

            migrationBuilder.DropColumn(
                name: "ExternalAgencyId",
                table: "DealParticipations");
        }
    }
}
