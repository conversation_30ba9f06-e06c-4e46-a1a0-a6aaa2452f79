using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace RealtoCrm.EntityFrameworkCore.Migrations
{
    /// <inheritdoc />
    public partial class AddOfferLifestyles : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Offers_Lifestyles_LifestyleId",
                table: "Offers");

            migrationBuilder.DropIndex(
                name: "IX_Offers_LifestyleId",
                table: "Offers");

            migrationBuilder.DropColumn(
                name: "LifestyleId",
                table: "Offers");

            migrationBuilder.CreateTable(
                name: "OfferLifestyles",
                columns: table => new
                {
                    OfferId = table.Column<int>(type: "integer", nullable: false),
                    LifestyleId = table.Column<int>(type: "integer", nullable: false),
                    CreationTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatorUserId = table.Column<long>(type: "bigint", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifierUserId = table.Column<long>(type: "bigint", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    DeletionTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DeleterUserId = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_OfferLifestyles", x => new { x.OfferId, x.LifestyleId });
                    table.ForeignKey(
                        name: "FK_OfferLifestyles_Lifestyles_LifestyleId",
                        column: x => x.LifestyleId,
                        principalTable: "Lifestyles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_OfferLifestyles_Offers_OfferId",
                        column: x => x.OfferId,
                        principalTable: "Offers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_OfferLifestyles_LifestyleId",
                table: "OfferLifestyles",
                column: "LifestyleId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "OfferLifestyles");

            migrationBuilder.AddColumn<int>(
                name: "LifestyleId",
                table: "Offers",
                type: "integer",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Offers_LifestyleId",
                table: "Offers",
                column: "LifestyleId");

            migrationBuilder.AddForeignKey(
                name: "FK_Offers_Lifestyles_LifestyleId",
                table: "Offers",
                column: "LifestyleId",
                principalTable: "Lifestyles",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
