#nullable disable
namespace RealtoCrm.EntityFrameworkCore.Migrations;

using Microsoft.EntityFrameworkCore.Migrations;

/// <inheritdoc />
public partial class ProjectEstateGroupDetails : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.AddColumn<double>(
            name: "AtelierUnits",
            table: "Projects",
            type: "double precision",
            nullable: true);

        migrationBuilder.AddColumn<double>(
            name: "FourRoomsUnits",
            table: "Projects",
            type: "double precision",
            nullable: true);

        migrationBuilder.AddColumn<double>(
            name: "GarageUnits",
            table: "Projects",
            type: "double precision",
            nullable: true);

        migrationBuilder.AddColumn<double>(
            name: "MaisonetteUnits",
            table: "Projects",
            type: "double precision",
            nullable: true);

        migrationBuilder.AddColumn<double>(
            name: "MultiRoomsUnits",
            table: "Projects",
            type: "double precision",
            nullable: true);

        migrationBuilder.AddColumn<double>(
            name: "OfficeUnits",
            table: "Projects",
            type: "double precision",
            nullable: true);

        migrationBuilder.AddColumn<double>(
            name: "OneRoomUnits",
            table: "Projects",
            type: "double precision",
            nullable: true);

        migrationBuilder.AddColumn<double>(
            name: "ParkingSpaceUnits",
            table: "Projects",
            type: "double precision",
            nullable: true);

        migrationBuilder.AddColumn<double>(
            name: "RestaurantUnits",
            table: "Projects",
            type: "double precision",
            nullable: true);

        migrationBuilder.AddColumn<double>(
            name: "ShopUnits",
            table: "Projects",
            type: "double precision",
            nullable: true);

        migrationBuilder.AddColumn<double>(
            name: "ThreeRoomsUnits",
            table: "Projects",
            type: "double precision",
            nullable: true);

        migrationBuilder.AddColumn<double>(
            name: "TwoRoomsUnits",
            table: "Projects",
            type: "double precision",
            nullable: true);

        migrationBuilder.AlterColumn<bool>(
            name: "ZeroCommission",
            table: "ProjectDetails",
            type: "boolean",
            nullable: true,
            oldClrType: typeof(bool),
            oldType: "boolean");

        migrationBuilder.AlterColumn<bool>(
            name: "IsSuitableForInvestment",
            table: "ProjectDetails",
            type: "boolean",
            nullable: true,
            oldClrType: typeof(bool),
            oldType: "boolean");

        migrationBuilder.AlterColumn<bool>(
            name: "IsGuarded",
            table: "ProjectDetails",
            type: "boolean",
            nullable: true,
            oldClrType: typeof(bool),
            oldType: "boolean");

        migrationBuilder.AlterColumn<bool>(
            name: "IsGatedComplex",
            table: "ProjectDetails",
            type: "boolean",
            nullable: true,
            oldClrType: typeof(bool),
            oldType: "boolean");

        migrationBuilder.AlterColumn<bool>(
            name: "IsEnergyEfficient",
            table: "ProjectDetails",
            type: "boolean",
            nullable: true,
            oldClrType: typeof(bool),
            oldType: "boolean");

        migrationBuilder.AlterColumn<bool>(
            name: "IsConfidential",
            table: "ProjectDetails",
            type: "boolean",
            nullable: true,
            oldClrType: typeof(bool),
            oldType: "boolean");

        migrationBuilder.AlterColumn<bool>(
            name: "IsApprovedProject",
            table: "ProjectDetails",
            type: "boolean",
            nullable: true,
            oldClrType: typeof(bool),
            oldType: "boolean");

        migrationBuilder.AlterColumn<bool>(
            name: "HasWater",
            table: "ProjectDetails",
            type: "boolean",
            nullable: true,
            oldClrType: typeof(bool),
            oldType: "boolean");

        migrationBuilder.AlterColumn<bool>(
            name: "HasView",
            table: "ProjectDetails",
            type: "boolean",
            nullable: true,
            oldClrType: typeof(bool),
            oldType: "boolean");

        migrationBuilder.AlterColumn<bool>(
            name: "HasSpecialPrice",
            table: "ProjectDetails",
            type: "boolean",
            nullable: true,
            oldClrType: typeof(bool),
            oldType: "boolean");

        migrationBuilder.AlterColumn<bool>(
            name: "HasSolarPanels",
            table: "ProjectDetails",
            type: "boolean",
            nullable: true,
            oldClrType: typeof(bool),
            oldType: "boolean");

        migrationBuilder.AlterColumn<bool>(
            name: "HasSewage",
            table: "ProjectDetails",
            type: "boolean",
            nullable: true,
            oldClrType: typeof(bool),
            oldType: "boolean");

        migrationBuilder.AlterColumn<bool>(
            name: "HasRestaurant",
            table: "ProjectDetails",
            type: "boolean",
            nullable: true,
            oldClrType: typeof(bool),
            oldType: "boolean");

        migrationBuilder.AlterColumn<bool>(
            name: "HasRailwayAccess",
            table: "ProjectDetails",
            type: "boolean",
            nullable: true,
            oldClrType: typeof(bool),
            oldType: "boolean");

        migrationBuilder.AlterColumn<bool>(
            name: "HasPublicTransportNearby",
            table: "ProjectDetails",
            type: "boolean",
            nullable: true,
            oldClrType: typeof(bool),
            oldType: "boolean");

        migrationBuilder.AlterColumn<bool>(
            name: "HasPool",
            table: "ProjectDetails",
            type: "boolean",
            nullable: true,
            oldClrType: typeof(bool),
            oldType: "boolean");

        migrationBuilder.AlterColumn<bool>(
            name: "HasPhone",
            table: "ProjectDetails",
            type: "boolean",
            nullable: true,
            oldClrType: typeof(bool),
            oldType: "boolean");

        migrationBuilder.AlterColumn<bool>(
            name: "HasParking",
            table: "ProjectDetails",
            type: "boolean",
            nullable: true,
            oldClrType: typeof(bool),
            oldType: "boolean");

        migrationBuilder.AlterColumn<bool>(
            name: "HasInternet",
            table: "ProjectDetails",
            type: "boolean",
            nullable: true,
            oldClrType: typeof(bool),
            oldType: "boolean");

        migrationBuilder.AlterColumn<bool>(
            name: "HasGas",
            table: "ProjectDetails",
            type: "boolean",
            nullable: true,
            oldClrType: typeof(bool),
            oldType: "boolean");

        migrationBuilder.AlterColumn<bool>(
            name: "HasGarden",
            table: "ProjectDetails",
            type: "boolean",
            nullable: true,
            oldClrType: typeof(bool),
            oldType: "boolean");

        migrationBuilder.AlterColumn<bool>(
            name: "HasFitness",
            table: "ProjectDetails",
            type: "boolean",
            nullable: true,
            oldClrType: typeof(bool),
            oldType: "boolean");

        migrationBuilder.AlterColumn<bool>(
            name: "HasElevator",
            table: "ProjectDetails",
            type: "boolean",
            nullable: true,
            oldClrType: typeof(bool),
            oldType: "boolean");

        migrationBuilder.AlterColumn<bool>(
            name: "HasElectricity",
            table: "ProjectDetails",
            type: "boolean",
            nullable: true,
            oldClrType: typeof(bool),
            oldType: "boolean");

        migrationBuilder.AlterColumn<bool>(
            name: "HasCableTv",
            table: "ProjectDetails",
            type: "boolean",
            nullable: true,
            oldClrType: typeof(bool),
            oldType: "boolean");

        migrationBuilder.AlterColumn<bool>(
            name: "HasBasement",
            table: "ProjectDetails",
            type: "boolean",
            nullable: true,
            oldClrType: typeof(bool),
            oldType: "boolean");

        migrationBuilder.AlterColumn<bool>(
            name: "HasAttic",
            table: "ProjectDetails",
            type: "boolean",
            nullable: true,
            oldClrType: typeof(bool),
            oldType: "boolean");

        migrationBuilder.AlterColumn<bool>(
            name: "HasAirConditionSystem",
            table: "ProjectDetails",
            type: "boolean",
            nullable: true,
            oldClrType: typeof(bool),
            oldType: "boolean");

        migrationBuilder.AlterColumn<bool>(
            name: "AdvertisementBanner",
            table: "ProjectDetails",
            type: "boolean",
            nullable: true,
            oldClrType: typeof(bool),
            oldType: "boolean");
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropColumn(
            name: "AtelierUnits",
            table: "Projects");

        migrationBuilder.DropColumn(
            name: "FourRoomsUnits",
            table: "Projects");

        migrationBuilder.DropColumn(
            name: "GarageUnits",
            table: "Projects");

        migrationBuilder.DropColumn(
            name: "MaisonetteUnits",
            table: "Projects");

        migrationBuilder.DropColumn(
            name: "MultiRoomsUnits",
            table: "Projects");

        migrationBuilder.DropColumn(
            name: "OfficeUnits",
            table: "Projects");

        migrationBuilder.DropColumn(
            name: "OneRoomUnits",
            table: "Projects");

        migrationBuilder.DropColumn(
            name: "ParkingSpaceUnits",
            table: "Projects");

        migrationBuilder.DropColumn(
            name: "RestaurantUnits",
            table: "Projects");

        migrationBuilder.DropColumn(
            name: "ShopUnits",
            table: "Projects");

        migrationBuilder.DropColumn(
            name: "ThreeRoomsUnits",
            table: "Projects");

        migrationBuilder.DropColumn(
            name: "TwoRoomsUnits",
            table: "Projects");

        migrationBuilder.AlterColumn<bool>(
            name: "ZeroCommission",
            table: "ProjectDetails",
            type: "boolean",
            nullable: false,
            defaultValue: false,
            oldClrType: typeof(bool),
            oldType: "boolean",
            oldNullable: true);

        migrationBuilder.AlterColumn<bool>(
            name: "IsSuitableForInvestment",
            table: "ProjectDetails",
            type: "boolean",
            nullable: false,
            defaultValue: false,
            oldClrType: typeof(bool),
            oldType: "boolean",
            oldNullable: true);

        migrationBuilder.AlterColumn<bool>(
            name: "IsGuarded",
            table: "ProjectDetails",
            type: "boolean",
            nullable: false,
            defaultValue: false,
            oldClrType: typeof(bool),
            oldType: "boolean",
            oldNullable: true);

        migrationBuilder.AlterColumn<bool>(
            name: "IsGatedComplex",
            table: "ProjectDetails",
            type: "boolean",
            nullable: false,
            defaultValue: false,
            oldClrType: typeof(bool),
            oldType: "boolean",
            oldNullable: true);

        migrationBuilder.AlterColumn<bool>(
            name: "IsEnergyEfficient",
            table: "ProjectDetails",
            type: "boolean",
            nullable: false,
            defaultValue: false,
            oldClrType: typeof(bool),
            oldType: "boolean",
            oldNullable: true);

        migrationBuilder.AlterColumn<bool>(
            name: "IsConfidential",
            table: "ProjectDetails",
            type: "boolean",
            nullable: false,
            defaultValue: false,
            oldClrType: typeof(bool),
            oldType: "boolean",
            oldNullable: true);

        migrationBuilder.AlterColumn<bool>(
            name: "IsApprovedProject",
            table: "ProjectDetails",
            type: "boolean",
            nullable: false,
            defaultValue: false,
            oldClrType: typeof(bool),
            oldType: "boolean",
            oldNullable: true);

        migrationBuilder.AlterColumn<bool>(
            name: "HasWater",
            table: "ProjectDetails",
            type: "boolean",
            nullable: false,
            defaultValue: false,
            oldClrType: typeof(bool),
            oldType: "boolean",
            oldNullable: true);

        migrationBuilder.AlterColumn<bool>(
            name: "HasView",
            table: "ProjectDetails",
            type: "boolean",
            nullable: false,
            defaultValue: false,
            oldClrType: typeof(bool),
            oldType: "boolean",
            oldNullable: true);

        migrationBuilder.AlterColumn<bool>(
            name: "HasSpecialPrice",
            table: "ProjectDetails",
            type: "boolean",
            nullable: false,
            defaultValue: false,
            oldClrType: typeof(bool),
            oldType: "boolean",
            oldNullable: true);

        migrationBuilder.AlterColumn<bool>(
            name: "HasSolarPanels",
            table: "ProjectDetails",
            type: "boolean",
            nullable: false,
            defaultValue: false,
            oldClrType: typeof(bool),
            oldType: "boolean",
            oldNullable: true);

        migrationBuilder.AlterColumn<bool>(
            name: "HasSewage",
            table: "ProjectDetails",
            type: "boolean",
            nullable: false,
            defaultValue: false,
            oldClrType: typeof(bool),
            oldType: "boolean",
            oldNullable: true);

        migrationBuilder.AlterColumn<bool>(
            name: "HasRestaurant",
            table: "ProjectDetails",
            type: "boolean",
            nullable: false,
            defaultValue: false,
            oldClrType: typeof(bool),
            oldType: "boolean",
            oldNullable: true);

        migrationBuilder.AlterColumn<bool>(
            name: "HasRailwayAccess",
            table: "ProjectDetails",
            type: "boolean",
            nullable: false,
            defaultValue: false,
            oldClrType: typeof(bool),
            oldType: "boolean",
            oldNullable: true);

        migrationBuilder.AlterColumn<bool>(
            name: "HasPublicTransportNearby",
            table: "ProjectDetails",
            type: "boolean",
            nullable: false,
            defaultValue: false,
            oldClrType: typeof(bool),
            oldType: "boolean",
            oldNullable: true);

        migrationBuilder.AlterColumn<bool>(
            name: "HasPool",
            table: "ProjectDetails",
            type: "boolean",
            nullable: false,
            defaultValue: false,
            oldClrType: typeof(bool),
            oldType: "boolean",
            oldNullable: true);

        migrationBuilder.AlterColumn<bool>(
            name: "HasPhone",
            table: "ProjectDetails",
            type: "boolean",
            nullable: false,
            defaultValue: false,
            oldClrType: typeof(bool),
            oldType: "boolean",
            oldNullable: true);

        migrationBuilder.AlterColumn<bool>(
            name: "HasParking",
            table: "ProjectDetails",
            type: "boolean",
            nullable: false,
            defaultValue: false,
            oldClrType: typeof(bool),
            oldType: "boolean",
            oldNullable: true);

        migrationBuilder.AlterColumn<bool>(
            name: "HasInternet",
            table: "ProjectDetails",
            type: "boolean",
            nullable: false,
            defaultValue: false,
            oldClrType: typeof(bool),
            oldType: "boolean",
            oldNullable: true);

        migrationBuilder.AlterColumn<bool>(
            name: "HasGas",
            table: "ProjectDetails",
            type: "boolean",
            nullable: false,
            defaultValue: false,
            oldClrType: typeof(bool),
            oldType: "boolean",
            oldNullable: true);

        migrationBuilder.AlterColumn<bool>(
            name: "HasGarden",
            table: "ProjectDetails",
            type: "boolean",
            nullable: false,
            defaultValue: false,
            oldClrType: typeof(bool),
            oldType: "boolean",
            oldNullable: true);

        migrationBuilder.AlterColumn<bool>(
            name: "HasFitness",
            table: "ProjectDetails",
            type: "boolean",
            nullable: false,
            defaultValue: false,
            oldClrType: typeof(bool),
            oldType: "boolean",
            oldNullable: true);

        migrationBuilder.AlterColumn<bool>(
            name: "HasElevator",
            table: "ProjectDetails",
            type: "boolean",
            nullable: false,
            defaultValue: false,
            oldClrType: typeof(bool),
            oldType: "boolean",
            oldNullable: true);

        migrationBuilder.AlterColumn<bool>(
            name: "HasElectricity",
            table: "ProjectDetails",
            type: "boolean",
            nullable: false,
            defaultValue: false,
            oldClrType: typeof(bool),
            oldType: "boolean",
            oldNullable: true);

        migrationBuilder.AlterColumn<bool>(
            name: "HasCableTv",
            table: "ProjectDetails",
            type: "boolean",
            nullable: false,
            defaultValue: false,
            oldClrType: typeof(bool),
            oldType: "boolean",
            oldNullable: true);

        migrationBuilder.AlterColumn<bool>(
            name: "HasBasement",
            table: "ProjectDetails",
            type: "boolean",
            nullable: false,
            defaultValue: false,
            oldClrType: typeof(bool),
            oldType: "boolean",
            oldNullable: true);

        migrationBuilder.AlterColumn<bool>(
            name: "HasAttic",
            table: "ProjectDetails",
            type: "boolean",
            nullable: false,
            defaultValue: false,
            oldClrType: typeof(bool),
            oldType: "boolean",
            oldNullable: true);

        migrationBuilder.AlterColumn<bool>(
            name: "HasAirConditionSystem",
            table: "ProjectDetails",
            type: "boolean",
            nullable: false,
            defaultValue: false,
            oldClrType: typeof(bool),
            oldType: "boolean",
            oldNullable: true);

        migrationBuilder.AlterColumn<bool>(
            name: "AdvertisementBanner",
            table: "ProjectDetails",
            type: "boolean",
            nullable: false,
            defaultValue: false,
            oldClrType: typeof(bool),
            oldType: "boolean",
            oldNullable: true);
    }
}