namespace RealtoCrm.EntityFrameworkCore.Migrations.Seed.Tenants;

using System.Linq;
using Abp.MultiTenancy;
using Editions;
using Microsoft.EntityFrameworkCore;

public class DefaultTenantBuilder(RealtoCrmDbContext context)
{
    public void Create()
    {
        this.CreateDefaultTenant();
    }

    private void CreateDefaultTenant()
    {
        //Default tenant

        var defaultTenant = context.Tenants.IgnoreQueryFilters().FirstOrDefault(t => t.TenancyName == AbpTenantBase.DefaultTenantName);
        if (defaultTenant == null)
        {
            defaultTenant = new MultiTenancy.Tenant(AbpTenantBase.DefaultTenantName, AbpTenantBase.DefaultTenantName);

            var defaultEdition = context.Editions.IgnoreQueryFilters().FirstOrDefault(e => e.Name == EditionManager.DefaultEditionName);
            if (defaultEdition != null)
            {
                defaultTenant.EditionId = defaultEdition.Id;
            }

            context.Tenants.Add(defaultTenant);
            context.SaveChanges();
        }
    }
}