namespace RealtoCrm.EntityFrameworkCore.Migrations.Seed.All.Entities;

using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using Base;
using Microsoft.EntityFrameworkCore;
using MultiTenancy;
using static CosherConsts.Tenants;

public class TenantsCreator(DbContext dbContext)
    : BaseMigrationDbBuilder<Tenant>(dbContext)
{
    protected override IEnumerable<IEnumerable<SeedValue<Tenant>>> ObjectData
        =>
        [
            [
                new SeedValue<Tenant>
                {
                    Name = nameof(Tenant.Name),
                    Value = AddressTenantName,
                },
                new SeedValue<Tenant>()
                {
                    Name = nameof(Tenant.TenancyName),
                    Value = AddressTenantName,
                },
                new SeedValue<Tenant>
                {
                    Name = nameof(Tenant.LogoUrl),
                    Value = "/logos/adres-big.svg",
                },
                new SeedValue<Tenant>
                {
                    Name = nameof(Tenant.SmallLogoUrl),
                    Value = "/logos/adres-small.svg",
                }
            ],
            [
                new SeedValue<Tenant>
                {
                    Name = nameof(Tenant.Name),
                    Value = ImotekaTenantName,
                },
                new SeedValue<Tenant>()
                {
                    Name = nameof(Tenant.TenancyName),
                    Value = ImotekaTenantName,
                },
                new SeedValue<Tenant>
                {
                    Name = nameof(Tenant.LogoUrl),
                    Value = "/logos/imoteka-big.svg",
                },
                new SeedValue<Tenant>
                {
                    Name = nameof(Tenant.SmallLogoUrl),
                    Value = "/logos/imoteka-small.svg",
                }
            ],
            [
                new SeedValue<Tenant>
                {
                    Name = nameof(Tenant.Name),
                    Value = UniqueEstatesTenantName,
                },
                new SeedValue<Tenant>()
                {
                    Name = nameof(Tenant.TenancyName),
                    Value = UniqueEstatesTenantName,
                },
                new SeedValue<Tenant>
                {
                    Name = nameof(Tenant.LogoUrl),
                    Value = "/logos/ues-big.svg",
                },
                new SeedValue<Tenant>
                {
                    Name = nameof(Tenant.SmallLogoUrl),
                    Value = "/logos/ues-small.svg",
                }
            ],
            [
                new SeedValue<Tenant>
                {
                    Name = nameof(Tenant.Name),
                    Value = NewEstatesTenantName,
                },
                new SeedValue<Tenant>()
                {
                    Name = nameof(Tenant.TenancyName),
                    Value = NewEstatesTenantName,
                },
                new SeedValue<Tenant>
                {
                    Name = nameof(Tenant.LogoUrl),
                    Value = "/logos/new-big.svg",
                },
                new SeedValue<Tenant>
                {
                    Name = nameof(Tenant.SmallLogoUrl),
                    Value = "/logos/new-small.svg",
                }
            ],
            [
                new SeedValue<Tenant>
                {
                    Name = nameof(Tenant.Name),
                    Value = CWFortonTenantName,
                },
                new SeedValue<Tenant>()
                {
                    Name = nameof(Tenant.TenancyName),
                    Value = CWFortonTenantName,
                },
                new SeedValue<Tenant>
                {
                    Name = nameof(Tenant.LogoUrl),
                    Value = "/logos/forton-big.svg",
                },
                new SeedValue<Tenant>
                {
                    Name = nameof(Tenant.SmallLogoUrl),
                    Value = "/logos/forton-small.svg",
                }
            ],
            [
                new SeedValue<Tenant>
                {
                    Name = nameof(Tenant.Name),
                    Value = ImofondTenantName,
                },
                new SeedValue<Tenant>()
                {
                    Name = nameof(Tenant.TenancyName),
                    Value = ImofondTenantName,
                },
                new SeedValue<Tenant>
                {
                    Name = nameof(Tenant.LogoUrl),
                    Value = "/logos/imofond-big.svg",
                },
                new SeedValue<Tenant>
                {
                    Name = nameof(Tenant.SmallLogoUrl),
                    Value = "/logos/imofond-small.svg",
                }
            ],
            [
                new SeedValue<Tenant>
                {
                    Name = nameof(Tenant.Name),
                    Value = BOPartnersTenantName,
                },
                new SeedValue<Tenant>()
                {
                    Name = nameof(Tenant.TenancyName),
                    Value = BOPartnersTenantName,
                },
                new SeedValue<Tenant>
                {
                    Name = nameof(Tenant.LogoUrl),
                    Value = "/logos/bo-partners-big.svg",
                },
                new SeedValue<Tenant>
                {
                    Name = nameof(Tenant.SmallLogoUrl),
                    Value = "/logos/bo-partners-small.svg",
                },
            ],
            [
                new SeedValue<Tenant>
                {
                    Name = nameof(Tenant.Name),
                    Value = RealtoTenantName,
                },
                new SeedValue<Tenant>()
                {
                    Name = nameof(Tenant.TenancyName),
                    Value = RealtoTenantName,
                },
                new SeedValue<Tenant>
                {
                    Name = nameof(Tenant.LogoUrl),
                    Value = "/logos/bo-partners-big.svg",
                },
                new SeedValue<Tenant>
                {
                    Name = nameof(Tenant.SmallLogoUrl),
                    Value = "/logos/bo-partners-big.svg",
                }
            ],
        ];

    protected override Expression<Func<Tenant, string>> EqualityMembersSelector
        => c => c.Name;
}