namespace RealtoCrm.EntityFrameworkCore.Migrations.Seed.All.Entities;

using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using Base;
using Microsoft.EntityFrameworkCore;
using Tags;
using static CosherConsts.TagCategories;

public class TagCategoriesCreator(DbContext dbContext)
    : BaseMigrationDbBuilder<TagCategory>(dbContext)
{
    protected override IEnumerable<IEnumerable<SeedValue<TagCategory>>> ObjectData
        =>
        [
            [
                new SeedValue<TagCategory>
                {
                    Name = nameof(TagCategory.Name),
                    Value = Interests,
                },
            ],
            [
                new SeedValue<TagCategory>
                {
                    Name = nameof(TagCategory.Name),
                    Value = Religion,
                },
            ],
            [
                new SeedValue<TagCategory>
                {
                    Name = nameof(TagCategory.Name),
                    Value = Embassies,
                },
            ],
        ];

    protected override Expression<Func<TagCategory, string>> EqualityMembersSelector
        => x => x.Name;
}