namespace RealtoCrm.EntityFrameworkCore.Migrations.Seed.All.Entities;

using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using Base;
using ContactDetails;
using Microsoft.EntityFrameworkCore;
using static CosherConsts.ContactDetailTypes;

public class ContactDetailTypesCreator(DbContext dbContext)
    : BaseMigrationDbBuilder<ContactDetailType>(dbContext)
{
    protected override IEnumerable<IEnumerable<SeedValue<ContactDetailType>>> ObjectData
        =>
        [
            [
                new SeedValue<ContactDetailType>
                {
                    Name = nameof(ContactDetailType.Name),
                    Value = PhoneContactDetailTypeName,
                }
            ],
            [
                new SeedValue<ContactDetailType>
                {
                    Name = nameof(ContactDetailType.Name),
                    Value = EmailContactDetailTypeName,
                }
            ],
            [
                new SeedValue<ContactDetailType>
                {
                    Name = nameof(ContactDetailType.Name),
                    Value = LinkContactDetailTypeName,
                }
            ]
        ];

    protected override Expression<Func<ContactDetailType, string>> EqualityMembersSelector
        => x => x.Name;
}