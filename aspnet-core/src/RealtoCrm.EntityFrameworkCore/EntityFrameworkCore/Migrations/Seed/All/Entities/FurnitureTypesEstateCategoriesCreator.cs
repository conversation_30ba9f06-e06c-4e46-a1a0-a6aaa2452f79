namespace RealtoCrm.EntityFrameworkCore.Migrations.Seed.All.Entities;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using Base;
using Estates;
using FurnitureTypesEstateCategories;
using Microsoft.EntityFrameworkCore;
using RealtoCrm.Nomenclatures;
using static CosherConsts.Furnitures;
using static CosherConsts.EstateCategories;

public class FurnitureTypesEstateCategoriesCreator(DbContext dbContext)
    : BaseMigrationDbBuilder<FurnitureTypeEstateCategory>(dbContext)
{
    protected override IEnumerable<IEnumerable<SeedValue<FurnitureTypeEstateCategory>>> ObjectData =>
    [
        [
            new SeedValue<FurnitureTypeEstateCategory>
            {
                Name = nameof(FurnitureTypeEstateCategory.FurnitureId),
                FindFunc = dbContext => dbContext.Set<Furniture>()
                    .First(x => x.Name == FullyFurnishedFurnitureName).Id,
            },
            new SeedValue<FurnitureTypeEstateCategory>
            {
                Name = nameof(FurnitureTypeEstateCategory.EstateCategoryId),
                FindFunc = dbContext => dbContext.Set<EstateCategory>()
                    .First(x => x.Name == BusinessPropertyEstateCategoryName).Id,
            }
        ],
        [
            new SeedValue<FurnitureTypeEstateCategory>
            {
                Name = nameof(FurnitureTypeEstateCategory.FurnitureId),
                FindFunc = dbContext => dbContext.Set<Furniture>()
                    .First(x => x.Name == FullyFurnishedFurnitureName).Id,
            },
            new SeedValue<FurnitureTypeEstateCategory>
            {
                Name = nameof(FurnitureTypeEstateCategory.EstateCategoryId),
                FindFunc = dbContext => dbContext.Set<EstateCategory>()
                    .First(x => x.Name == ResidentialPropertyEstateCategoryName).Id,
            }
        ],
        [
            new SeedValue<FurnitureTypeEstateCategory>
            {
                Name = nameof(FurnitureTypeEstateCategory.FurnitureId),
                FindFunc = dbContext => dbContext.Set<Furniture>()
                    .First(x => x.Name == PartiallyFurnishedFurnitureName).Id,
            },
            new SeedValue<FurnitureTypeEstateCategory>
            {
                Name = nameof(FurnitureTypeEstateCategory.EstateCategoryId),
                FindFunc = dbContext => dbContext.Set<EstateCategory>()
                    .First(x => x.Name == BusinessPropertyEstateCategoryName).Id,
            }
        ],
        [
            new SeedValue<FurnitureTypeEstateCategory>
            {
                Name = nameof(FurnitureTypeEstateCategory.FurnitureId),
                FindFunc = dbContext => dbContext.Set<Furniture>()
                    .First(x => x.Name == PartiallyFurnishedFurnitureName).Id,
            },
            new SeedValue<FurnitureTypeEstateCategory>
            {
                Name = nameof(FurnitureTypeEstateCategory.EstateCategoryId),
                FindFunc = dbContext => dbContext.Set<EstateCategory>()
                    .First(x => x.Name == ResidentialPropertyEstateCategoryName).Id,
            }
        ],
        [
            new SeedValue<FurnitureTypeEstateCategory>
            {
                Name = nameof(FurnitureTypeEstateCategory.FurnitureId),
                FindFunc = dbContext => dbContext.Set<Furniture>()
                    .First(x => x.Name == NotFurnishedFurnitureName).Id,
            },
            new SeedValue<FurnitureTypeEstateCategory>
            {
                Name = nameof(FurnitureTypeEstateCategory.EstateCategoryId),
                FindFunc = dbContext => dbContext.Set<EstateCategory>()
                    .First(x => x.Name == BusinessPropertyEstateCategoryName).Id,
            }
        ],
        [
            new SeedValue<FurnitureTypeEstateCategory>
            {
                Name = nameof(FurnitureTypeEstateCategory.FurnitureId),
                FindFunc = dbContext => dbContext.Set<Furniture>()
                    .First(x => x.Name == NotFurnishedFurnitureName).Id,
            },
            new SeedValue<FurnitureTypeEstateCategory>
            {
                Name = nameof(FurnitureTypeEstateCategory.EstateCategoryId),
                FindFunc = dbContext => dbContext.Set<EstateCategory>()
                    .First(x => x.Name == ResidentialPropertyEstateCategoryName).Id,
            }
        ],
        [
            new SeedValue<FurnitureTypeEstateCategory>
            {
                Name = nameof(FurnitureTypeEstateCategory.FurnitureId),
                FindFunc = dbContext => dbContext.Set<Furniture>()
                    .First(x => x.Name == FurnishedToKeyFurnitureName).Id,
            },
            new SeedValue<FurnitureTypeEstateCategory>
            {
                Name = nameof(FurnitureTypeEstateCategory.EstateCategoryId),
                FindFunc = dbContext => dbContext.Set<EstateCategory>()
                    .First(x => x.Name == ResidentialPropertyEstateCategoryName).Id,
            }
        ],
        [
            new SeedValue<FurnitureTypeEstateCategory>
            {
                Name = nameof(FurnitureTypeEstateCategory.FurnitureId),
                FindFunc = dbContext => dbContext.Set<Furniture>()
                    .First(x => x.Name == FurnishedToPaintAndPlasterFurnitureName).Id,
            },
            new SeedValue<FurnitureTypeEstateCategory>
            {
                Name = nameof(FurnitureTypeEstateCategory.EstateCategoryId),
                FindFunc = dbContext => dbContext.Set<EstateCategory>()
                    .First(x => x.Name == BusinessPropertyEstateCategoryName).Id,
            }
        ],
        [
            new SeedValue<FurnitureTypeEstateCategory>
            {
                Name = nameof(FurnitureTypeEstateCategory.FurnitureId),
                FindFunc = dbContext => dbContext.Set<Furniture>()
                    .First(x => x.Name == FurnishedToPaintAndPlasterFurnitureName).Id,
            },
            new SeedValue<FurnitureTypeEstateCategory>
            {
                Name = nameof(FurnitureTypeEstateCategory.EstateCategoryId),
                FindFunc = dbContext => dbContext.Set<EstateCategory>()
                    .First(x => x.Name == ResidentialPropertyEstateCategoryName).Id,
            }
        ],
        [
            new SeedValue<FurnitureTypeEstateCategory>
            {
                Name = nameof(FurnitureTypeEstateCategory.FurnitureId),
                FindFunc = dbContext => dbContext.Set<Furniture>()
                    .First(x => x.Name == FurnishedToPaintAndPlasterFurnitureName).Id,
            },
            new SeedValue<FurnitureTypeEstateCategory>
            {
                Name = nameof(FurnitureTypeEstateCategory.EstateCategoryId),
                FindFunc = dbContext => dbContext.Set<EstateCategory>()
                    .First(x => x.Name == IndustrialPropertyEstateCategoryName).Id,
            }
        ],
        [
            new SeedValue<FurnitureTypeEstateCategory>
            {
                Name = nameof(FurnitureTypeEstateCategory.FurnitureId),
                FindFunc = dbContext => dbContext.Set<Furniture>()
                    .First(x => x.Name == FullyEquippedFurnitureName).Id,
            },
            new SeedValue<FurnitureTypeEstateCategory>
            {
                Name = nameof(FurnitureTypeEstateCategory.EstateCategoryId),
                FindFunc = dbContext => dbContext.Set<EstateCategory>()
                    .First(x => x.Name == BusinessPropertyEstateCategoryName).Id,
            }
        ],
        [
            new SeedValue<FurnitureTypeEstateCategory>
            {
                Name = nameof(FurnitureTypeEstateCategory.FurnitureId),
                FindFunc = dbContext => dbContext.Set<Furniture>()
                    .First(x => x.Name == FullyEquippedFurnitureName).Id,
            },
            new SeedValue<FurnitureTypeEstateCategory>
            {
                Name = nameof(FurnitureTypeEstateCategory.EstateCategoryId),
                FindFunc = dbContext => dbContext.Set<EstateCategory>()
                    .First(x => x.Name == IndustrialPropertyEstateCategoryName).Id,
            }
        ],
        [
            new SeedValue<FurnitureTypeEstateCategory>
            {
                Name = nameof(FurnitureTypeEstateCategory.FurnitureId),
                FindFunc = dbContext => dbContext.Set<Furniture>()
                    .First(x => x.Name == PartiallyEquippedFurnitureName).Id,
            },
            new SeedValue<FurnitureTypeEstateCategory>
            {
                Name = nameof(FurnitureTypeEstateCategory.EstateCategoryId),
                FindFunc = dbContext => dbContext.Set<EstateCategory>()
                    .First(x => x.Name == BusinessPropertyEstateCategoryName).Id,
            }
        ],
        [
            new SeedValue<FurnitureTypeEstateCategory>
            {
                Name = nameof(FurnitureTypeEstateCategory.FurnitureId),
                FindFunc = dbContext => dbContext.Set<Furniture>()
                    .First(x => x.Name == PartiallyEquippedFurnitureName).Id,
            },
            new SeedValue<FurnitureTypeEstateCategory>
            {
                Name = nameof(FurnitureTypeEstateCategory.EstateCategoryId),
                FindFunc = dbContext => dbContext.Set<EstateCategory>()
                    .First(x => x.Name == IndustrialPropertyEstateCategoryName).Id,
            }
        ],
        [
            new SeedValue<FurnitureTypeEstateCategory>
            {
                Name = nameof(FurnitureTypeEstateCategory.FurnitureId),
                FindFunc = dbContext => dbContext.Set<Furniture>()
                    .First(x => x.Name == NotEquippedFurnitureName).Id,
            },
            new SeedValue<FurnitureTypeEstateCategory>
            {
                Name = nameof(FurnitureTypeEstateCategory.EstateCategoryId),
                FindFunc = dbContext => dbContext.Set<EstateCategory>()
                    .First(x => x.Name == BusinessPropertyEstateCategoryName).Id,
            }
        ],
        [
            new SeedValue<FurnitureTypeEstateCategory>
            {
                Name = nameof(FurnitureTypeEstateCategory.FurnitureId),
                FindFunc = dbContext => dbContext.Set<Furniture>()
                    .First(x => x.Name == NotEquippedFurnitureName).Id,
            },
            new SeedValue<FurnitureTypeEstateCategory>
            {
                Name = nameof(FurnitureTypeEstateCategory.EstateCategoryId),
                FindFunc = dbContext => dbContext.Set<EstateCategory>()
                    .First(x => x.Name == IndustrialPropertyEstateCategoryName).Id,
            }
        ],
    ];

    protected override Expression<Func<FurnitureTypeEstateCategory, string>> EqualityMembersSelector
        => x => string.Join(";", x.EstateCategoryId.ToString(), x.FurnitureId);
}