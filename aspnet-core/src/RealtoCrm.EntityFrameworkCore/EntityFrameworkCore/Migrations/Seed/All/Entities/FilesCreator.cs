namespace RealtoCrm.EntityFrameworkCore.Migrations.Seed.All.Entities;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using Base;
using Files;
using Microsoft.EntityFrameworkCore;
using MultiTenancy;

public class FilesCreator(DbContext dbContext): BaseMigrationDbBuilder<File>(dbContext)
{
    private const string CategoryTenant = "Пакет Наемател";
    private const string CategoryBuyer = "Пакет Купувач";
    private const string CategoryLandlord = "Пакет Наемодател";
    private const string CategorySeller = "Пакет Продавач";
    private const string TenantName = "Unique Estates";


    protected override IEnumerable<IEnumerable<SeedValue<File>>> ObjectData
        =>
        [
            [
                new SeedValue<File>
                {
                    Name = nameof(File.FileName),
                    Value = "02.01. Договор_премиум_купувач.doc",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Title),
                    Value = "ДОГОВОР ЗА ПРЕМИУМ УСЛУГА",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Source),
                    Value = "/documents/02.01. Договор_премиум_купувач.doc",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Size),
                    Value = 148992,
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Category),
                    FindFunc = dbContext => dbContext.Set<FileCategory>()
                        .FirstOrDefault(x => x.Name == CategoryBuyer),
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Tenant),
                    FindFunc = dbContext => dbContext.Set<Tenant>()
                        .FirstOrDefault(x => x.Name.ToLower() == TenantName.ToLower()),
                },
            ],
            [
                new SeedValue<File>
                {
                    Name = nameof(File.FileName),
                    Value = "02.03.1 Договор_конкретен_имот_купувач.doc",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Title),
                    Value = "ДОГОВОР ЗА СТАНДАРТНА УСЛУГА (Конкретен имот)",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Source),
                    Value = "/documents/02.03.1 Договор_конкретен_имот_купувач.doc",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Size),
                    Value = 148992,
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Category),
                    FindFunc = dbContext => dbContext.Set<FileCategory>()
                        .FirstOrDefault(x => x.Name == CategoryBuyer),
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Tenant),
                    FindFunc = dbContext => dbContext.Set<Tenant>()
                        .FirstOrDefault(x => x.Name.ToLower() == TenantName.ToLower()),
                },
            ],
            [
                new SeedValue<File>
                {
                    Name = nameof(File.FileName),
                    Value = "02.02.Договор_стандарт_купувач.doc",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Title),
                    Value = "ДОГОВОР ЗА СТАНДАРТНА УСЛУГА",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Source),
                    Value = "/documents/02.02.Договор_стандарт_купувач.doc",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Size),
                    Value = 148992,
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Category),
                    FindFunc = dbContext => dbContext.Set<FileCategory>()
                        .FirstOrDefault(x => x.Name == CategoryBuyer),
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Tenant),
                    FindFunc = dbContext => dbContext.Set<Tenant>()
                        .FirstOrDefault(x => x.Name.ToLower() == TenantName.ToLower()),
                },
            ],
            [
                new SeedValue<File>
                {
                    Name = nameof(File.FileName),
                    Value = "02.04. Анекс към договор за посредничество.doc",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Title),
                    Value = "Анекс към договор за посредничество",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Source),
                    Value = "/documents/02.04. Анекс към договор за посредничество.doc",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Size),
                    Value = 148992,
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Category),
                    FindFunc = dbContext => dbContext.Set<FileCategory>()
                        .FirstOrDefault(x => x.Name == CategoryBuyer),
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Tenant),
                    FindFunc = dbContext => dbContext.Set<Tenant>()
                        .FirstOrDefault(x => x.Name.ToLower() == TenantName.ToLower()),
                },
            ],
            [
                new SeedValue<File>
                {
                    Name = nameof(File.FileName),
                    Value = "02.09. Декларация за липса на фин.задължения.doc",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Title),
                    Value = "Декларация за липса на фин.задължения",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Source),
                    Value = "/documents/02.09. Декларация за липса на фин.задължения.doc",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Size),
                    Value = 148992,
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Category),
                    FindFunc = dbContext => dbContext.Set<FileCategory>()
                        .FirstOrDefault(x => x.Name == CategoryBuyer),
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Tenant),
                    FindFunc = dbContext => dbContext.Set<Tenant>()
                        .FirstOrDefault(x => x.Name.ToLower() == TenantName.ToLower()),
                },
            ],
            [
                new SeedValue<File>
                {
                    Name = nameof(File.FileName),
                    Value = "02.10. Договор_гаранция_купувач_стандарт.doc",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Title),
                    Value = "ДОГОВОР ЗА ГАРАНЦИЯ",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Source),
                    Value = "/documents/02.10. Договор_гаранция_купувач_стандарт.doc",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Size),
                    Value = 148992,
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Category),
                    FindFunc = dbContext => dbContext.Set<FileCategory>()
                        .FirstOrDefault(x => x.Name == CategoryBuyer),
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Tenant),
                    FindFunc = dbContext => dbContext.Set<Tenant>()
                        .FirstOrDefault(x => x.Name.ToLower() == TenantName.ToLower()),
                },
            ],
            [
                new SeedValue<File>
                {
                    Name = nameof(File.FileName),
                    Value = "04.01. Договор_премиум_наемател.doc",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Title),
                    Value = "ДОГОВОР ЗА ПРЕМИУМ УСЛУГА",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Source),
                    Value = "/documents/04.01. Договор_премиум_наемател.doc",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Size),
                    Value = 148992,
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Category),
                    FindFunc = dbContext => dbContext.Set<FileCategory>()
                        .FirstOrDefault(x => x.Name == CategoryTenant),
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Tenant),
                    FindFunc = dbContext => dbContext.Set<Tenant>()
                        .FirstOrDefault(x => x.Name.ToLower() == TenantName.ToLower()),
                },
            ],
            [
                new SeedValue<File>
                {
                    Name = nameof(File.FileName),
                    Value = "04.02. Договор_стандарт_наемател.doc",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Title),
                    Value = "ДОГОВОР ЗА СТАНДАРТНА УСЛУГА",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Source),
                    Value = "/documents/04.02. Договор_стандарт_наемател.doc",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Size),
                    Value = 148992,
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Category),
                    FindFunc = dbContext => dbContext.Set<FileCategory>()
                        .FirstOrDefault(x => x.Name == CategoryTenant),
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Tenant),
                    FindFunc = dbContext => dbContext.Set<Tenant>()
                        .FirstOrDefault(x => x.Name.ToLower() == TenantName.ToLower()),
                },
            ],
            [
                new SeedValue<File>
                {
                    Name = nameof(File.FileName),
                    Value = "04.03. Анекс към договор за посредничество.doc",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Title),
                    Value = "АНЕКС към Договор за ПРЕМИУМ / СТАНДАРТНА УСЛУГА",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Source),
                    Value = "/documents/04.03. Анекс към договор за посредничество.doc",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Size),
                    Value = 148992,
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Category),
                    FindFunc = dbContext => dbContext.Set<FileCategory>()
                        .FirstOrDefault(x => x.Name == CategoryTenant),
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Tenant),
                    FindFunc = dbContext => dbContext.Set<Tenant>()
                        .FirstOrDefault(x => x.Name.ToLower() == TenantName.ToLower()),
                },
            ],
            [
                new SeedValue<File>
                {
                    Name = nameof(File.FileName),
                    Value = "04.04. Дог. за гаранция наемател_стандарт.doc",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Title),
                    Value = "ДОГОВОР ЗА ГАРАНЦИЯ",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Source),
                    Value = "/documents/04.04. Дог. за гаранция наемател_стандарт.doc",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Size),
                    Value = 148992,
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Category),
                    FindFunc = dbContext => dbContext.Set<FileCategory>()
                        .FirstOrDefault(x => x.Name == CategoryTenant),
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Tenant),
                    FindFunc = dbContext => dbContext.Set<Tenant>()
                        .FirstOrDefault(x => x.Name.ToLower() == TenantName.ToLower()),
                },
            ],
            [
                new SeedValue<File>
                {
                    Name = nameof(File.FileName),
                    Value = "03.01. Договор_премиум_наемодател.doc",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Title),
                    Value = "ДОГОВОР ЗА ПРЕМИУМ УСЛУГА",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Source),
                    Value = "/documents/03.01. Договор_премиум_наемодател.doc",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Size),
                    Value = 148992,
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Category),
                    FindFunc = dbContext => dbContext.Set<FileCategory>()
                        .FirstOrDefault(x => x.Name == CategoryLandlord),
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Tenant),
                    FindFunc = dbContext => dbContext.Set<Tenant>()
                        .FirstOrDefault(x => x.Name.ToLower() == TenantName.ToLower()),
                },
            ],
            [
                new SeedValue<File>
                {
                    Name = nameof(File.FileName),
                    Value = "03.02. Договор_стандарт наемодател.doc",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Title),
                    Value = "ДОГОВОР ЗА СТАНДАРТНА УСЛУГА",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Source),
                    Value = "/documents/03.02. Договор_стандарт наемодател.doc",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Size),
                    Value = 148992,
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Category),
                    FindFunc = dbContext => dbContext.Set<FileCategory>()
                        .FirstOrDefault(x => x.Name == CategoryLandlord),
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Tenant),
                    FindFunc = dbContext => dbContext.Set<Tenant>()
                        .FirstOrDefault(x => x.Name.ToLower() == TenantName.ToLower()),
                },
            ],
            [
                new SeedValue<File>
                {
                    Name = nameof(File.FileName),
                    Value = "03.06. Договор за наем.doc",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Title),
                    Value = "Договор за наем",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Source),
                    Value = "/documents/03.06. Договор за наем.doc",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Size),
                    Value = 148992,
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Category),
                    FindFunc = dbContext => dbContext.Set<FileCategory>()
                        .FirstOrDefault(x => x.Name == CategoryLandlord),
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Tenant),
                    FindFunc = dbContext => dbContext.Set<Tenant>()
                        .FirstOrDefault(x => x.Name.ToLower() == TenantName.ToLower()),
                },
            ],
            [
                new SeedValue<File>
                {
                    Name = nameof(File.FileName),
                    Value = "03.09. Протокол-опис.doc",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Title),
                    Value = "ПРОТОКОЛ – ОПИС",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Source),
                    Value = "/documents/03.09. Протокол-опис.doc",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Size),
                    Value = 148992,
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Category),
                    FindFunc = dbContext => dbContext.Set<FileCategory>()
                        .FirstOrDefault(x => x.Name == CategoryLandlord),
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Tenant),
                    FindFunc = dbContext => dbContext.Set<Tenant>()
                        .FirstOrDefault(x => x.Name.ToLower() == TenantName.ToLower()),
                },
            ],
            [
                new SeedValue<File>
                {
                    Name = nameof(File.FileName),
                    Value = "01.01. Договор_Премиум_Продавач.doc",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Title),
                    Value = "ДОГОВОР ЗА ПРЕМИУМ УСЛУГА",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Source),
                    Value = "/documents/01.01. Договор_Премиум_Продавач.doc",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Size),
                    Value = 148992,
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Category),
                    FindFunc = dbContext => dbContext.Set<FileCategory>()
                        .FirstOrDefault(x => x.Name == CategorySeller),
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Tenant),
                    FindFunc = dbContext => dbContext.Set<Tenant>()
                        .FirstOrDefault(x => x.Name.ToLower() == TenantName.ToLower()),
                },
            ],
            [
                new SeedValue<File>
                {
                    Name = nameof(File.FileName),
                    Value = "01.01.1. Договор_Премиум_Продавач ко-екскл..doc",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Title),
                    Value = "ДОГОВОР ЗА ПРЕМИУМ УСЛУГА – ко-ексклузив",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Source),
                    Value = "/documents/01.01.1. Договор_Премиум_Продавач ко-екскл..doc",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Size),
                    Value = 148992,
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Category),
                    FindFunc = dbContext => dbContext.Set<FileCategory>()
                        .FirstOrDefault(x => x.Name == CategorySeller),
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Tenant),
                    FindFunc = dbContext => dbContext.Set<Tenant>()
                        .FirstOrDefault(x => x.Name.ToLower() == TenantName.ToLower()),
                },
            ],
            [
                new SeedValue<File>
                {
                    Name = nameof(File.FileName),
                    Value = "01.02. Договор_стандарт_продавач.doc",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Title),
                    Value = "ДОГОВОР ЗА СТАНДАРТНА УСЛУГА",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Source),
                    Value = "/documents/01.02. Договор_стандарт_продавач.doc",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Size),
                    Value = 148992,
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Category),
                    FindFunc = dbContext => dbContext.Set<FileCategory>()
                        .FirstOrDefault(x => x.Name == CategorySeller),
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Tenant),
                    FindFunc = dbContext => dbContext.Set<Tenant>()
                        .FirstOrDefault(x => x.Name.ToLower() == TenantName.ToLower()),
                },
            ],
            [
                new SeedValue<File>
                {
                    Name = nameof(File.FileName),
                    Value = "01.03. Анекс към договор за посредничество.doc",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Title),
                    Value = "Анекс към договор за посредничество",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Source),
                    Value = "/documents/01.03. Анекс към договор за посредничество.doc",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Size),
                    Value = 148992,
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Category),
                    FindFunc = dbContext => dbContext.Set<FileCategory>()
                        .FirstOrDefault(x => x.Name == CategorySeller),
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Tenant),
                    FindFunc = dbContext => dbContext.Set<Tenant>()
                        .FirstOrDefault(x => x.Name.ToLower() == TenantName.ToLower()),
                },
            ],
            [
                new SeedValue<File>
                {
                    Name = nameof(File.FileName),
                    Value = "01.13. Предварителен договор.doc",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Title),
                    Value = "ПРЕДВАРИТЕЛЕН ДОГОВОР ЗА ПОКУПКО - ПРОДАЖБА НА НЕДВИЖИМ ИМОТ",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Source),
                    Value = "/documents/01.13. Предварителен договор.doc",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Size),
                    Value = 148992,
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Category),
                    FindFunc = dbContext => dbContext.Set<FileCategory>()
                        .FirstOrDefault(x => x.Name == CategorySeller),
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Tenant),
                    FindFunc = dbContext => dbContext.Set<Tenant>()
                        .FirstOrDefault(x => x.Name.ToLower() == TenantName.ToLower()),
                },
            ],
            [
                new SeedValue<File>
                {
                    Name = nameof(File.FileName),
                    Value = "01.16. Анекс към предварителен договор.doc",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Title),
                    Value = "Анекс към предварителен договор за покупко- продажба",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Source),
                    Value = "/documents/01.16. Анекс към предварителен договор.doc",
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Size),
                    Value = 148992,
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Category),
                    FindFunc = dbContext => dbContext.Set<FileCategory>()
                        .FirstOrDefault(x => x.Name == CategorySeller),
                },
                new SeedValue<File>
                {
                    Name = nameof(File.Tenant),
                    FindFunc = dbContext => dbContext.Set<Tenant>()
                        .FirstOrDefault(x => x.Name.ToLower() == TenantName.ToLower()),
                },
            ],
        ];

    protected override Expression<Func<File, string>> EqualityMembersSelector
        => x => x.FileName;
}