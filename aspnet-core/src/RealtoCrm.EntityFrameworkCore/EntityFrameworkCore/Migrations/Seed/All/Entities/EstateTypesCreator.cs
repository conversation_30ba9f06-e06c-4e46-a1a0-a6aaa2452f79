namespace RealtoCrm.EntityFrameworkCore.Migrations.Seed.All.Entities;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using Microsoft.EntityFrameworkCore;
using RealtoCrm.EntityFrameworkCore.Migrations.Seed.All.Base;
using RealtoCrm.Estates;

using static RealtoCrm.CosherConsts.EstateTypes;
using static RealtoCrm.CosherConsts.EstateCategories;

public class EstateTypesCreator(DbContext dbContext)
    : BaseMigrationNomenclaturesDbBuilder<EstateType>(dbContext)
{
    protected override IEnumerable<IEnumerable<SeedValue<EstateType>>> ObjectData
        =>
        [
            [
                new SeedValue<EstateType>
                {
                    Name = nameof(EstateType.Name),
                    Value = OneBedroomEstateTypeName,
                },
                new SeedValue<EstateType?>
                {
                    Name = nameof(EstateType.Category),
                    FindFunc = dbContext => dbContext.Set<EstateCategory>()
                        .FirstOrDefault(x => x.Name == ApartmentEstateCategoryName),
                },
            ],
            [
                new SeedValue<EstateType>
                {
                    Name = nameof(EstateType.Name),
                    Value = TwoBedroomEstateTypeName,
                },
                new SeedValue<EstateType?>
                {
                    Name = nameof(EstateType.Category),
                    FindFunc = dbContext => dbContext.Set<EstateCategory>()
                        .FirstOrDefault(x => x.Name == ApartmentEstateCategoryName),
                },
            ],
            [
                new SeedValue<EstateType>
                {
                    Name = nameof(EstateType.Name),
                    Value = ThreeBedroomEstateTypeName,
                },
                new SeedValue<EstateType?>
                {
                    Name = nameof(EstateType.Category),
                    FindFunc = dbContext => dbContext.Set<EstateCategory>()
                        .FirstOrDefault(x => x.Name == ApartmentEstateCategoryName),
                },
            ],
            [
                new SeedValue<EstateType>
                {
                    Name = nameof(EstateType.Name),
                    Value = FourBedroomEstateTypeName,
                },
                new SeedValue<EstateType>
                {
                    Name = nameof(EstateType.IsMainType),
                    Value = false,
                },
                new SeedValue<EstateType?>
                {
                    Name = nameof(EstateType.Category),
                    FindFunc = dbContext => dbContext.Set<EstateCategory>()
                        .FirstOrDefault(x => x.Name == ApartmentEstateCategoryName),
                }
            ],
            [
                new SeedValue<EstateType>
                {
                    Name = nameof(EstateType.Name),
                    Value = MultiBedroomEstateTypeName,
                },
                new SeedValue<EstateType?>
                {
                    Name = nameof(EstateType.Category),
                    FindFunc = dbContext => dbContext.Set<EstateCategory>()
                        .FirstOrDefault(x => x.Name == ApartmentEstateCategoryName),
                },
            ],
            [
                new SeedValue<EstateType>
                {
                    Name = nameof(EstateType.Name),
                    Value = StudioEstateTypeName,
                },
                new SeedValue<EstateType?>
                {
                    Name = nameof(EstateType.Category),
                    FindFunc = dbContext => dbContext.Set<EstateCategory>()
                        .FirstOrDefault(x => x.Name == ApartmentEstateCategoryName),
                },
            ],
            [
                new SeedValue<EstateType>
                {
                    Name = nameof(EstateType.Name),
                    Value = HouseFloorEstateTypeName,
                },
                new SeedValue<EstateType?>
                {
                    Name = nameof(EstateType.Category),
                    FindFunc = dbContext => dbContext.Set<EstateCategory>()
                        .FirstOrDefault(x => x.Name == HouseFloorEstateCategoryName),
                },
            ],
            [
                new SeedValue<EstateType>
                {
                    Name = nameof(EstateType.Name),
                    Value = HouseOrVillaEstateTypeName,
                },
                new SeedValue<EstateType?>
                {
                    Name = nameof(EstateType.Category),
                    FindFunc = dbContext => dbContext.Set<EstateCategory>()
                        .FirstOrDefault(x => x.Name == HouseOrVillaEstateCategoryName),
                },
            ],
            [
                new SeedValue<EstateType>
                {
                    Name = nameof(EstateType.Name),
                    Value = PlotOrLandEstateTypeName,
                },
                new SeedValue<EstateType?>
                {
                    Name = nameof(EstateType.Category),
                    FindFunc = dbContext => dbContext.Set<EstateCategory>()
                        .FirstOrDefault(x => x.Name == PlotOrLandEstateCategoryName),
                },
            ],
            [
                new SeedValue<EstateType>
                {
                    Name = nameof(EstateType.Name),
                    Value = AgriculturalLandEstateTypeName,
                },
                new SeedValue<EstateType?>
                {
                    Name = nameof(EstateType.Category),
                    FindFunc = dbContext => dbContext.Set<EstateCategory>()
                        .FirstOrDefault(x => x.Name == AgriculturalLandEstateCategoryName),
                },
            ],
            [
                new SeedValue<EstateType>
                {
                    Name = nameof(EstateType.Name),
                    Value = GarageEstateTypeName,
                },
                new SeedValue<EstateType?>
                {
                    Name = nameof(EstateType.Category),
                    FindFunc = dbContext => dbContext.Set<EstateCategory>()
                        .FirstOrDefault(x => x.Name == GarageOrParkingSlotEstateCategoryName),
                },
            ],
            [
                new SeedValue<EstateType>
                {
                    Name = nameof(EstateType.Name),
                    Value = ParkingSlotEstateTypeName,
                },
                new SeedValue<EstateType?>
                {
                    Name = nameof(EstateType.Category),
                    FindFunc = dbContext => dbContext.Set<EstateCategory>()
                        .FirstOrDefault(x => x.Name == GarageOrParkingSlotEstateCategoryName),
                },
            ],
            [
                new SeedValue<EstateType>
                {
                    Name = nameof(EstateType.Name),
                    Value = OfficeEstateTypeName,
                },
                new SeedValue<EstateType?>
                {
                    Name = nameof(EstateType.Category),
                    FindFunc = dbContext => dbContext.Set<EstateCategory>()
                        .FirstOrDefault(x => x.Name == OfficeEstateCategoryName),
                },
            ],
            [
                new SeedValue<EstateType>
                {
                    Name = nameof(EstateType.Name),
                    Value = StoreEstateTypeName,
                },
                new SeedValue<EstateType?>
                {
                    Name = nameof(EstateType.Category),
                    FindFunc = dbContext => dbContext.Set<EstateCategory>()
                        .FirstOrDefault(x => x.Name == StoreEstateCategoryName),
                },
            ],
            [
                new SeedValue<EstateType>
                {
                    Name = nameof(EstateType.Name),
                    Value = HotelOrMotelEstateTypeName,
                },
                new SeedValue<EstateType?>
                {
                    Name = nameof(EstateType.Category),
                    FindFunc = dbContext => dbContext.Set<EstateCategory>()
                        .FirstOrDefault(x => x.Name == HotelOrMotelEstateCategoryName),
                },
            ],
            [
                new SeedValue<EstateType>
                {
                    Name = nameof(EstateType.Name),
                    Value = OfficeBuildingOrShoppingCenterEstateTypeName,
                },
                new SeedValue<EstateType?>
                {
                    Name = nameof(EstateType.Category),
                    FindFunc = dbContext => dbContext.Set<EstateCategory>()
                        .FirstOrDefault(x => x.Name == OfficeBuildingOrShoppingCenterEstateCategoryName),
                },
            ],
            [
                new SeedValue<EstateType>
                {
                    Name = nameof(EstateType.Name),
                    Value = RestaurantEstateTypeName,
                },
                new SeedValue<EstateType?>
                {
                    Name = nameof(EstateType.Category),
                    FindFunc = dbContext => dbContext.Set<EstateCategory>()
                        .FirstOrDefault(x => x.Name == RestaurantEstateCategoryName),
                },
            ],
            [
                new SeedValue<EstateType>
                {
                    Name = nameof(EstateType.Name),
                    Value = UnspecifiedEstateTypeName,
                },
                new SeedValue<EstateType?>
                {
                    Name = nameof(EstateType.Category),
                    FindFunc = dbContext => dbContext.Set<EstateCategory>()
                        .FirstOrDefault(x => x.Name == UnspecifiedEstateCategoryName),
                },
            ],
            [
                new SeedValue<EstateType>
                {
                    Name = nameof(EstateType.Name),
                    Value = OutbuildingOrFarmEstateTypeName,
                },
                new SeedValue<EstateType?>
                {
                    Name = nameof(EstateType.Category),
                    FindFunc = dbContext => dbContext.Set<EstateCategory>()
                        .FirstOrDefault(x => x.Name == OutbuildingOrFarmEstateCategoryName),
                },
            ],
            [
                new SeedValue<EstateType>
                {
                    Name = nameof(EstateType.Name),
                    Value = WorkshopOrWarehouseEstateTypeName,
                },
                new SeedValue<EstateType?>
                {
                    Name = nameof(EstateType.Category),
                    FindFunc = dbContext => dbContext.Set<EstateCategory>()
                        .FirstOrDefault(x => x.Name == WorkshopOrWarehouseEstateCategoryName),
                },
            ],
            //Marketing Estate Types
            [
                new SeedValue<EstateType>
                {
                    Name = nameof(EstateType.Name),
                    Value = BoxonieraEstateTypeName,
                },
                new SeedValue<EstateType>
                {
                    Name = nameof(EstateType.IsMainType),
                    Value = false,
                },
                new SeedValue<EstateType?>
                {
                    Name = nameof(EstateType.Category),
                    FindFunc = dbContext => dbContext.Set<EstateCategory>()
                        .FirstOrDefault(x => x.Name == ApartmentEstateCategoryName),
                }
            ],
            [
                new SeedValue<EstateType>
                {
                    Name = nameof(EstateType.Name),
                    Value = MaisonetteEstateTypeName,
                },
                new SeedValue<EstateType>
                {
                    Name = nameof(EstateType.IsMainType),
                    Value = false,
                },
                new SeedValue<EstateType?>
                {
                    Name = nameof(EstateType.Category),
                    FindFunc = dbContext => dbContext.Set<EstateCategory>()
                        .FirstOrDefault(x => x.Name == ApartmentEstateCategoryName),
                }
            ],
            [
                new SeedValue<EstateType>
                {
                    Name = nameof(EstateType.Name),
                    Value = GarageCellarEstateTypeName,
                },
                new SeedValue<EstateType>
                {
                    Name = nameof(EstateType.IsMainType),
                    Value = false,
                },
                new SeedValue<EstateType?>
                {
                    Name = nameof(EstateType.Category),
                    FindFunc = dbContext => dbContext.Set<EstateCategory>()
                        .FirstOrDefault(x => x.Name == GarageOrParkingSlotEstateCategoryName),
                }
            ],
            [
                new SeedValue<EstateType>
                {
                    Name = nameof(EstateType.Name),
                    Value = PharmacyEstateTypeName,
                },
                new SeedValue<EstateType>
                {
                    Name = nameof(EstateType.IsMainType),
                    Value = false,
                },
                new SeedValue<EstateType?>
                {
                    Name = nameof(EstateType.Category),
                    FindFunc = dbContext => dbContext.Set<EstateCategory>()
                        .FirstOrDefault(x => x.Name == StoreEstateCategoryName),
                }
            ],
            [
                new SeedValue<EstateType>
                {
                    Name = nameof(EstateType.Name),
                    Value = BeautyHairSalonEstateTypeName,
                },
                new SeedValue<EstateType>
                {
                    Name = nameof(EstateType.IsMainType),
                    Value = false,
                },
                new SeedValue<EstateType?>
                {
                    Name = nameof(EstateType.Category),
                    FindFunc = dbContext => dbContext.Set<EstateCategory>()
                        .FirstOrDefault(x => x.Name == StoreEstateCategoryName),
                }
            ],
            [
                new SeedValue<EstateType>
                {
                    Name = nameof(EstateType.Name),
                    Value = MedicalOfficeEstateTypeName,
                },
                new SeedValue<EstateType>
                {
                    Name = nameof(EstateType.IsMainType),
                    Value = false,
                },
                new SeedValue<EstateType?>
                {
                    Name = nameof(EstateType.Category),
                    FindFunc = dbContext => dbContext.Set<EstateCategory>()
                        .FirstOrDefault(x => x.Name == StoreEstateCategoryName),
                }
            ],
            [
                new SeedValue<EstateType>
                {
                    Name = nameof(EstateType.Name),
                    Value = IndustrialPropertyEstateTypeName,
                },
                new SeedValue<EstateType>
                {
                    Name = nameof(EstateType.IsMainType),
                    Value = false,
                },
                new SeedValue<EstateType?>
                {
                    Name = nameof(EstateType.Category),
                    FindFunc = dbContext => dbContext.Set<EstateCategory>()
                        .FirstOrDefault(x => x.Name == UnspecifiedEstateCategoryName),
                }
            ],
            [
                new SeedValue<EstateType>
                {
                    Name = nameof(EstateType.Name),
                    Value = RetailPremisesEstateTypeName,
                },
                new SeedValue<EstateType>
                {
                    Name = nameof(EstateType.IsMainType),
                    Value = false,
                },
                new SeedValue<EstateType?>
                {
                    Name = nameof(EstateType.Category),
                    FindFunc = dbContext => dbContext.Set<EstateCategory>()
                        .FirstOrDefault(x => x.Name == UnspecifiedEstateCategoryName),
                }
            ],
            [
                new SeedValue<EstateType>
                {
                    Name = nameof(EstateType.Name),
                    Value = FitnessEstateTypeName,
                },
                new SeedValue<EstateType>
                {
                    Name = nameof(EstateType.IsMainType),
                    Value = false,
                },
                new SeedValue<EstateType?>
                {
                    Name = nameof(EstateType.Category),
                    FindFunc = dbContext => dbContext.Set<EstateCategory>()
                        .FirstOrDefault(x => x.Name == UnspecifiedEstateCategoryName),
                }
            ],
            [
                new SeedValue<EstateType>
                {
                    Name = nameof(EstateType.Name),
                    Value = ResidentialBuildingEstateTypeName,
                },
                new SeedValue<EstateType>
                {
                    Name = nameof(EstateType.IsMainType),
                    Value = false,
                },
                new SeedValue<EstateType?>
                {
                    Name = nameof(EstateType.Category),
                    FindFunc = dbContext => dbContext.Set<EstateCategory>()
                        .FirstOrDefault(x => x.Name == ResidentialBuildingEstateCategoryName),
                }
            ],
        ];

    protected override Expression<Func<EstateType, string>> EqualityMembersSelector
        => x => x.Name;
}