namespace RealtoCrm.EntityFrameworkCore.Migrations.Seed.All.Csv;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using CsvHelper;
using Microsoft.EntityFrameworkCore;
using RealtoCrm.EntityFrameworkCore.Migrations.Seed.All.Base;
using RealtoCrm.Estates;
using RealtoCrm.MultiTenancy;
using RealtoCrm.PropertyToEstateCategorySettings;

public class PropertyToEstateCategorySettingCreator(DbContext context) : BaseCsvDbBuilder<PropertyToEstateCategorySetting>(context)
{
    private const string EstateCategoryNameColumnName = "EstateCategoryName";
    private const string TenantNameColumnName = "TenantName";

    private const string TenantIdPropertyName = nameof(PropertyToEstateCategorySetting.TenantId);
    private const string EstateCategoryIdColumnName = nameof(PropertyToEstateCategorySetting.CategoryId);

    protected override string FileName => "property_to_estate_category_settings.csv";

    protected override Expression<Func<PropertyToEstateCategorySetting, string>> EqualityMembersSelector
        => x => string.Join(";", x.TenantId.ToString(), x.PropertyName.ToString(), x.CategoryId, x.Model);

    protected override List<string> CsvColumnNames =>
    [
        nameof(PropertyToEstateCategorySetting.PropertyName),
        nameof(PropertyToEstateCategorySetting.Model),
        nameof(PropertyToEstateCategorySetting.IsVisible),
        nameof(PropertyToEstateCategorySetting.RequiredForOfferStatus),
        EstateCategoryNameColumnName,
        TenantNameColumnName,
    ];

    private IDictionary<string, int> TenantsMap { get; set; }
    private IDictionary<string, int> EstateCategoriesMap { get; set; }

    private static bool IsTrue(object value) => value.ToString() == "t" || value.ToString() == "true" || value.ToString() == "1";

    protected override async Task PreloadData()
    {
        this.TenantsMap = (
                await context.Set<Tenant>()
                    .ToListAsync()
            )
            .ToDictionary(
                t => t.Name,
                t => t.Id
            );

        this.EstateCategoriesMap = (
                await context.Set<EstateCategory>()
                    .ToListAsync()
            )
            .ToDictionary(
                ec => ec.Name,
                ec => ec.Id);

        await base.PreloadData();
    }

    protected override string GetPropertyName(string columnName)
    {
        var result = columnName switch
        {
            TenantNameColumnName => TenantIdPropertyName,
            EstateCategoryNameColumnName => EstateCategoryIdColumnName,
            var _ => base.GetPropertyName(columnName)
        };

        return result;
    }


    protected override object ParseValue(string columnName, object originalValue, CsvReader csv)
    {
        var result = columnName switch
        {
            TenantNameColumnName => this.TenantsMap[originalValue.ToString()],
            EstateCategoryNameColumnName => this.EstateCategoriesMap[originalValue.ToString()],
            nameof(PropertyToEstateCategorySetting.IsVisible) => IsTrue(originalValue),
            nameof(PropertyToEstateCategorySetting.RequiredForOfferStatus) => (RequiredSettingForOfferStatus)int.Parse((string)originalValue),
            nameof(PropertyToEstateCategorySetting.Model) => int.Parse(originalValue.ToString()),
            var _ => base.ParseValue(columnName, originalValue, csv),
        };

        return result;
    }
}