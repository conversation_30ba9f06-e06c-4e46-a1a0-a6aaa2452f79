namespace RealtoCrm.EntityFrameworkCore.Migrations.Seed.All.Nomenclatures;

using System.Collections.Generic;
using Base;
using Microsoft.EntityFrameworkCore;
using RealtoCrm.Nomenclatures;
using static CosherConsts.OfferStatuses;

using static RealtoCrm.Nomenclatures.NomenclatureConsts.OfferStatus;

public class OfferStatusesCreator(DbContext dbContext)
    : BaseMigrationNomenclaturesDbBuilder<OfferStatus>(dbContext)
{
    protected override IEnumerable<IEnumerable<SeedValue<OfferStatus>>> ObjectData
        =>
        [
            [
                new SeedValue<OfferStatus>
                {
                    Name = nameof(OfferStatus.Name),
                    Value = DraftOfferStatusName,
                }
            ],
            [
                new SeedValue<OfferStatus>
                {
                    Name = nameof(OfferStatus.Name),
                    Value = PotentialOfferStatusName,
                }
            ],
            [
                new SeedValue<OfferStatus>
                {
                    Name = nameof(OfferStatus.Name),
                    Value = ActiveOfferStatusName,
                }
            ],
            [
                new SeedValue<OfferStatus>
                {
                    Name = nameof(OfferStatus.Name),
                    Value = ArchiveOfferStatusName,
                }
            ],
            [
                new SeedValue<OfferStatus>
                {
                    Name = nameof(OfferStatus.Name),
                    Value = DealOfferStatusName,
                }
            ],
        ];
}