namespace RealtoCrm.EntityFrameworkCore.Migrations.Seed.All.Nomenclatures;

using System.Collections.Generic;
using Base;
using Microsoft.EntityFrameworkCore;
using RealtoCrm.Nomenclatures;
using static CosherConsts.Vats;

public class VatsCreator(DbContext dbContext)
    : BaseMigrationNomenclaturesDbBuilder<Vat>(dbContext)
{
    protected override IEnumerable<IEnumerable<SeedValue<Vat>>> ObjectData
        =>
        [
            [
                new SeedValue<Vat>
                {
                    Name = nameof(Vat.Name),
                    Value = WithoutVatName,
                }
            ],
            [
                new SeedValue<Vat>
                {
                    Name = nameof(Vat.Name),
                    Value = WithVatName,
                }
            ],
            [
                new SeedValue<Vat>
                {
                    Name = nameof(Vat.Name),
                    Value = NotSubjectToVatName,
                }
            ]
        ];
}