namespace RealtoCrm.EntityFrameworkCore.Migrations.Seed.All.Nomenclatures;

using Base;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using RealtoCrm.Nomenclatures;
using static CosherConsts.SearchStatuses;

public class SearchStatusesCreator(DbContext dbContext)
    : BaseMigrationNomenclaturesDbBuilder<SearchStatus>(dbContext)
{
    protected override IEnumerable<IEnumerable<SeedValue<SearchStatus>>> ObjectData
        =>
        [
            [
                new SeedValue<SearchStatus>
                {
                    Name = nameof(SearchStatus.Name),
                    Value = ActiveSearchStatusName,
                }
            ],
            [
                new SeedValue<SearchStatus>
                {
                    Name = nameof(SearchStatus.Name),
                    Value = ArchivedSearchStatusName,
                }
            ],
            [
                new SeedValue<SearchStatus>
                {
                    Name = nameof(SearchStatus.Name),
                    Value = DealSearchStatusName,
                }
            ]
        ];
}