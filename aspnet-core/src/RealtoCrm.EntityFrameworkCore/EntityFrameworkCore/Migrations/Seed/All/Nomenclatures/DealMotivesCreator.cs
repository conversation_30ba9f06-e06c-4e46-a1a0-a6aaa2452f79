namespace RealtoCrm.EntityFrameworkCore.Migrations.Seed.All.Nomenclatures;

using System.Collections.Generic;
using System.Linq;
using Base;
using DealMotiveOperationTypes;
using Microsoft.EntityFrameworkCore;
using RealtoCrm.Nomenclatures;
using static RealtoCrm.CosherConsts.DealMotives;
using static RealtoCrm.CosherConsts.OperationTypes;

public class DealMotivesCreator(DbContext dbContext)
    : BaseMigrationNomenclaturesDbBuilder<DealMotive>(dbContext)
{
    protected override IEnumerable<IEnumerable<SeedValue<DealMotive>>> ObjectData
        =>
        [
            [
                new SeedValue<DealMotive>
                {
                    Name = nameof(DealMotive.Name),
                    Value = LeavingInvestmentDealMotiveName,
                },

                new SeedValue<DealMotive>
                {
                    Name = nameof(DealMotive.DealMotiveOperationTypes),
                    FindFunc = dbContext => new List<DealMotiveOperationType>()
                    {
                        new DealMotiveOperationType
                        {
                            OperationTypeId = dbContext.Set<OperationType>()
                                .Single(op => op.Name == SellingOperationTypeName).Id
                        }
                    }
                }
            ],
            [
                new SeedValue<DealMotive>
                {
                    Name = nameof(DealMotive.Name),
                    Value = SplittingDealMotiveName,
                },
                new SeedValue<DealMotive>
                {
                    Name = nameof(DealMotive.DealMotiveOperationTypes),
                    FindFunc = dbContext => new List<DealMotiveOperationType>()
                    {
                        new DealMotiveOperationType
                        {
                            OperationTypeId = dbContext.Set<OperationType>()
                                .Single(op => op.Name == SellingOperationTypeName).Id
                        }
                    }
                }
            ],
            [
                new SeedValue<DealMotive>
                {
                    Name = nameof(DealMotive.Name),
                    Value = NeedMoneyDealMotiveName,
                },
                new SeedValue<DealMotive>
                {
                    Name = nameof(DealMotive.DealMotiveOperationTypes),
                    FindFunc = dbContext => new List<DealMotiveOperationType>()
                    {
                        new DealMotiveOperationType
                        {
                            OperationTypeId = dbContext.Set<OperationType>()
                                .Single(op => op.Name == SellingOperationTypeName).Id
                        }
                    }
                }
            ],
            [
                new SeedValue<DealMotive>
                {
                    Name = nameof(DealMotive.Name),
                    Value = RepayingCreditDealMotiveName,
                },
                new SeedValue<DealMotive>
                {
                    Name = nameof(DealMotive.DealMotiveOperationTypes),
                    FindFunc = dbContext => new List<DealMotiveOperationType>()
                    {
                        new DealMotiveOperationType
                        {
                            OperationTypeId = dbContext.Set<OperationType>()
                                .Single(op => op.Name == SellingOperationTypeName).Id
                        }
                    }
                }
            ],
            [
                new SeedValue<DealMotive>
                {
                    Name = nameof(DealMotive.Name),
                    Value = SellingToBuyAnotherDealMotiveName,
                },
                new SeedValue<DealMotive>
                {
                    Name = nameof(DealMotive.DealMotiveOperationTypes),
                    FindFunc = dbContext => new List<DealMotiveOperationType>()
                    {
                        new DealMotiveOperationType
                        {
                            OperationTypeId = dbContext.Set<OperationType>()
                                .Single(op => op.Name == SellingOperationTypeName).Id
                        }
                    }
                }
            ],
            [
                new SeedValue<DealMotive>
                {
                    Name = nameof(DealMotive.Name),
                    Value = ProductRealizationDealMotiveName,
                },
                new SeedValue<DealMotive>
                {
                    Name = nameof(DealMotive.DealMotiveOperationTypes),
                    FindFunc = dbContext => new List<DealMotiveOperationType>()
                    {
                        new DealMotiveOperationType
                        {
                            OperationTypeId = dbContext.Set<OperationType>()
                                .Single(op => op.Name == SellingOperationTypeName).Id
                        }
                    }
                }
            ],
            [
                new SeedValue<DealMotive>
                {
                    Name = nameof(DealMotive.Name),
                    Value = ForInvestmentDealMotiveName,
                },
                new SeedValue<DealMotive>
                {
                    Name = nameof(DealMotive.DealMotiveOperationTypes),
                    FindFunc = dbContext => new List<DealMotiveOperationType>()
                    {
                        new DealMotiveOperationType
                        {
                            OperationTypeId = dbContext.Set<OperationType>()
                                .Single(op => op.Name == BuyingOperationTypeName).Id
                        }
                    }
                }
            ],
            [
                new SeedValue<DealMotive>
                {
                    Name = nameof(DealMotive.Name),
                    Value = ForBusinessDealMotiveName,
                },
                new SeedValue<DealMotive>
                {
                    Name = nameof(DealMotive.DealMotiveOperationTypes),
                    FindFunc = dbContext => new List<DealMotiveOperationType>()
                    {
                        new DealMotiveOperationType
                        {
                            OperationTypeId = dbContext.Set<OperationType>()
                                .Single(op => op.Name == BuyingOperationTypeName).Id
                        },
                        new DealMotiveOperationType
                        {
                            OperationTypeId = dbContext.Set<OperationType>()
                                .Single(op => op.Name == RentingOperationTypeName).Id
                        }
                    }
                }
            ],
            [
                new SeedValue<DealMotive>
                {
                    Name = nameof(DealMotive.Name),
                    Value = LivingUnderRentDealMotiveName,
                },
                new SeedValue<DealMotive>
                {
                    Name = nameof(DealMotive.DealMotiveOperationTypes),
                    FindFunc = dbContext => new List<DealMotiveOperationType>()
                    {
                        new DealMotiveOperationType
                        {
                            OperationTypeId = dbContext.Set<OperationType>()
                                .Single(op => op.Name == BuyingOperationTypeName).Id
                        },
                        new DealMotiveOperationType
                        {
                            OperationTypeId = dbContext.Set<OperationType>()
                                .Single(op => op.Name == RentingOperationTypeName).Id
                        }
                    }
                }
            ],
            [
                new SeedValue<DealMotive>
                {
                    Name = nameof(DealMotive.Name),
                    Value = BuyingForLivingDealMotiveName,
                },
                new SeedValue<DealMotive>
                {
                    Name = nameof(DealMotive.DealMotiveOperationTypes),
                    FindFunc = dbContext => new List<DealMotiveOperationType>()
                    {
                        new DealMotiveOperationType
                        {
                            OperationTypeId = dbContext.Set<OperationType>()
                                .Single(op => op.Name == BuyingOperationTypeName).Id
                        },
                    }
                }
            ],
            [
                new SeedValue<DealMotive>
                {
                    Name = nameof(DealMotive.Name),
                    Value = ChangingResidenceDealMotiveName,
                },
                new SeedValue<DealMotive>
                {
                    Name = nameof(DealMotive.DealMotiveOperationTypes),
                    FindFunc = dbContext => new List<DealMotiveOperationType>()
                    {
                        new DealMotiveOperationType
                        {
                            OperationTypeId = dbContext.Set<OperationType>()
                                .Single(op => op.Name == BuyingOperationTypeName).Id
                        },
                        new DealMotiveOperationType
                        {
                            OperationTypeId = dbContext.Set<OperationType>()
                                .Single(op => op.Name == RentingOperationTypeName).Id
                        }
                    }
                }
            ],
            [
                new SeedValue<DealMotive>
                {
                    Name = nameof(DealMotive.Name),
                    Value = OtherDealMotiveName,
                },
                new SeedValue<DealMotive>
                {
                    Name = nameof(DealMotive.DealMotiveOperationTypes),
                    FindFunc = dbContext => new List<DealMotiveOperationType>()
                    {
                        new DealMotiveOperationType
                        {
                            OperationTypeId = dbContext.Set<OperationType>()
                                .Single(op => op.Name == BuyingOperationTypeName).Id
                        },
                        new DealMotiveOperationType
                        {
                            OperationTypeId = dbContext.Set<OperationType>()
                                .Single(op => op.Name == RentingOperationTypeName).Id
                        },
                        new DealMotiveOperationType
                        {
                            OperationTypeId = dbContext.Set<OperationType>()
                                .Single(op => op.Name == GivingOperationTypeName).Id
                        },
                        new DealMotiveOperationType
                        {
                            OperationTypeId = dbContext.Set<OperationType>()
                                .Single(op => op.Name == SellingOperationTypeName).Id
                        }
                    }
                }
            ],
        ];
}