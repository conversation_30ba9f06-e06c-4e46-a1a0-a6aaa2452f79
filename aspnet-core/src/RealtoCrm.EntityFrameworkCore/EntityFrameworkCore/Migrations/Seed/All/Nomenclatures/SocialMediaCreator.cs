namespace RealtoCrm.EntityFrameworkCore.Migrations.Seed.All.Nomenclatures;

using System.Collections.Generic;
using Base;
using Microsoft.EntityFrameworkCore;
using RealtoCrm.Nomenclatures;

public class SocialMediaCreator(DbContext dbContext)
    : BaseMigrationNomenclaturesDbBuilder<SocialMedia>(dbContext)
{
    protected override IEnumerable<IEnumerable<SeedValue<SocialMedia>>> ObjectData
        =>
        [
            [
                new SeedValue<SocialMedia>
                {
                    Name = nameof(SocialMedia.Name),
                    Value = "LinkedIn",
                }
            ],
            [
                new SeedValue<SocialMedia>
                {
                    Name = nameof(SocialMedia.Name),
                    Value = "Facebook",
                }
            ],
            [
                new SeedValue<SocialMedia>
                {
                    Name = nameof(SocialMedia.Name),
                    Value = "Instagram",
                }
            ],
            [
                new SeedValue<SocialMedia>
                {
                    Name = nameof(SocialMedia.Name),
                    Value = "TikTok",
                }
            ],
            [
                new SeedValue<SocialMedia>
                {
                    Name = nameof(SocialMedia.Name),
                    Value = "WhatsApp",
                }
            ],
            [
                new SeedValue<SocialMedia>
                {
                    Name = nameof(SocialMedia.Name),
                    Value = "Viber",
                }
            ],
        ];
}