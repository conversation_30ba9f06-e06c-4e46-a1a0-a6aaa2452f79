namespace RealtoCrm.EntityFrameworkCore.Migrations.Seed.All.Nomenclatures;

using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using Base;
using RealtoCrm.Nomenclatures;
using static RealtoCrm.CosherConsts.ArchiveReasons;

public class ArchiveReasonsCreator(DbContext dbContext) : BaseMigrationNomenclaturesDbBuilder<ArchiveReason>(dbContext)
{
    protected override IEnumerable<IEnumerable<SeedValue<ArchiveReason>>> ObjectData
        =>
        [
            [
                new SeedValue<ArchiveReason>
                {
                    Name = nameof(ArchiveReason.Name),
                    Value = TemporaryNotLookingArchiveReasonName,
                },
                new SeedValue<ArchiveReason>
                {
                    Name = nameof(ArchiveReason.ArchiveType),
                    Value = ArchiveType.Shallow,
                },
            ],
            [
                new SeedValue<ArchiveReason>
                {
                    Name = nameof(ArchiveReason.Name),
                    Value = WrongNumberArchiveReasonName,
                },
                new SeedValue<ArchiveReason>
                {
                    Name = nameof(ArchiveReason.ArchiveType),
                    Value = ArchiveType.Deep,
                },
            ],
            [
                new SeedValue<ArchiveReason>
                {
                    Name = nameof(ArchiveReason.Name),
                    Value = BoughtSoldThemselvesArchiveReasonName,
                },
                new SeedValue<ArchiveReason>
                {
                    Name = nameof(ArchiveReason.ArchiveType),
                    Value = ArchiveType.Deep,
                },
            ],
            [
                new SeedValue<ArchiveReason>
                {
                    Name = nameof(ArchiveReason.Name),
                    Value = BoughtSoldWithCompetitionArchiveReasonName,
                },
                new SeedValue<ArchiveReason>
                {
                    Name = nameof(ArchiveReason.ArchiveType),
                    Value = ArchiveType.Deep,
                },
            ],
            [
                new SeedValue<ArchiveReason>
                {
                    Name = nameof(ArchiveReason.Name),
                    Value = AbandonedBuySellArchiveReasonName,
                },
                new SeedValue<ArchiveReason>
                {
                    Name = nameof(ArchiveReason.ArchiveType),
                    Value = ArchiveType.Shallow,
                },
            ],
            [
                new SeedValue<ArchiveReason>
                {
                    Name = nameof(ArchiveReason.Name),
                    Value = OtherArchiveReasonName,
                },
                new SeedValue<ArchiveReason>
                {
                    Name = nameof(ArchiveReason.ArchiveType),
                    Value = ArchiveType.Shallow,
                },
            ],
            [
                new SeedValue<ArchiveReason>
                {
                    Name = nameof(ArchiveReason.Name),
                    Value = RentGaveThemselvesArchiveReasonName,
                },
                new SeedValue<ArchiveReason>
                {
                    Name = nameof(ArchiveReason.ArchiveType),
                    Value = ArchiveType.Shallow,
                },
            ],
            [
                new SeedValue<ArchiveReason>
                {
                    Name = nameof(ArchiveReason.Name),
                    Value = RentGaveWithCompetitionArchiveReasonName,
                },
                new SeedValue<ArchiveReason>
                {
                    Name = nameof(ArchiveReason.ArchiveType),
                    Value = ArchiveType.Shallow,
                },
            ],
            [
                new SeedValue<ArchiveReason>
                {
                    Name = nameof(ArchiveReason.Name),
                    Value = ExclusiveContractWithCompetitionArchiveReasonName,
                },
                new SeedValue<ArchiveReason>
                {
                    Name = nameof(ArchiveReason.ArchiveType),
                    Value = ArchiveType.Shallow,
                },
            ],
            [
                new SeedValue<ArchiveReason>
                {
                    Name = nameof(ArchiveReason.Name),
                    Value = DuplicatedArchiveReasonName,
                },
                new SeedValue<ArchiveReason>
                {
                    Name = nameof(ArchiveReason.ArchiveType),
                    Value = ArchiveType.Deep,
                },
            ],
            [
                new SeedValue<ArchiveReason>
                {
                    Name = nameof(ArchiveReason.Name),
                    Value = ClientPassedAwayArchiveReasonName,
                },
                new SeedValue<ArchiveReason>
                {
                    Name = nameof(ArchiveReason.ArchiveType),
                    Value = ArchiveType.Deep,
                },
            ],
            [
                new SeedValue<ArchiveReason>
                {
                    Name = nameof(ArchiveReason.Name),
                    Value = OfferSearchByCompetitionArchiveReasonName,
                },
                new SeedValue<ArchiveReason>
                {
                    Name = nameof(ArchiveReason.ArchiveType),
                    Value = ArchiveType.Deep,
                },
            ],
        ];
}