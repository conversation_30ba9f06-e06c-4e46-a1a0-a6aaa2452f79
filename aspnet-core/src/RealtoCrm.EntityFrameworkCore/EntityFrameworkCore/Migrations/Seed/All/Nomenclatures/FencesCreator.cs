namespace RealtoCrm.EntityFrameworkCore.Migrations.Seed.All.Nomenclatures;

using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using RealtoCrm.EntityFrameworkCore.Migrations.Seed.All.Base;
using RealtoCrm.Nomenclatures;

public class FencesCreator(DbContext dbContext)
    : BaseMigrationNomenclaturesDbBuilder<Fence>(dbContext)
{
    protected override IEnumerable<IEnumerable<SeedValue<Fence>>> ObjectData
        =>
        [
            [
                new SeedValue<Fence>
                {
                    Name = nameof(Fence.Name),
                    Value = "Дървена",
                }
            ],
            [
                new SeedValue<Fence>
                {
                    Name = nameof(Fence.Name),
                    Value = "Зид",
                }
            ],
            [
                new SeedValue<Fence>
                {
                    Name = nameof(Fence.Name),
                    Value = "Мрежа",
                }
            ],
            [
                new SeedValue<Fence>
                {
                    Name = nameof(Fence.Name),
                    Value = "Без ограда",
                }
            ],
        ];
}