namespace RealtoCrm.EntityFrameworkCore.Migrations.Seed.All.Nomenclatures;

using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using RealtoCrm.EntityFrameworkCore.Migrations.Seed.All.Base;
using RealtoCrm.Nomenclatures;

using static RealtoCrm.CosherConsts.Conditions;
public class ConditionsCreator(DbContext dbContext)
    : BaseMigrationNomenclaturesDbBuilder<Condition>(dbContext)
{
    protected override IEnumerable<IEnumerable<SeedValue<Condition>>> ObjectData
        =>
        [
            [
                new SeedValue<Condition>
                {
                    Name = nameof(Condition.Name),
                    Value = UnderConstructionConditionName,
                }
            ],
            [
                new SeedValue<Condition>
                {
                    Name = nameof(Condition.Name),
                    Value = NeedsRepairConditionName,
                }
            ],
            [
                new SeedValue<Condition>
                {
                    Name = nameof(Condition.Name),
                    Value = GoodConditionName,
                }
            ],
            [
                new SeedValue<Condition>
                {
                    Name = nameof(Condition.Name),
                    Value = ExcellentConditionName,
                }
            ],
            [
                new SeedValue<Condition>
                {
                    Name = nameof(Condition.Name),
                    Value = LuxConditionName,
                }
            ],
        ];
}
