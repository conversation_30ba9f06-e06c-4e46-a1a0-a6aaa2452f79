namespace RealtoCrm.EntityFrameworkCore.Migrations.Seed.All.Nomenclatures;

using System.Collections.Generic;
using Base;
using Microsoft.EntityFrameworkCore;
using RealtoCrm.Nomenclatures;
using static CosherConsts.ProjectStatuses;

public class ProjectStatusesCreator(DbContext dbContext)
    : BaseMigrationNomenclaturesDbBuilder<ProjectStatus>(dbContext)
{
    protected override IEnumerable<IEnumerable<SeedValue<ProjectStatus>>> ObjectData
        =>
        [
            [
                new SeedValue<ProjectStatus>
                {
                    Name = nameof(ProjectStatus.Name),
                    Value = DraftProjectStatusName,
                }
            ],
            [
                new SeedValue<ProjectStatus>
                {
                    Name = nameof(ProjectStatus.Name),
                    Value = ActiveProjectStatusName,
                }
            ],
        ];
}