using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using RealtoCrm.EntityFrameworkCore.Migrations.Seed.All.Base;
using RealtoCrm.Nomenclatures;

namespace RealtoCrm.EntityFrameworkCore.Migrations.Seed.All.Nomenclatures;

public class BuildingPurposesCreator(DbContext dbContext)
    : BaseMigrationNomenclaturesDbBuilder<BuildingPurpose>(dbContext)
{
    protected override IEnumerable<IEnumerable<SeedValue<BuildingPurpose>>> ObjectData
        =>
        [
            [
                new SeedValue<BuildingPurpose>
                {
                    Name = nameof(BuildingPurpose.Name),
                    Value = "Офис сграда",
                }
            ],
            [
                new SeedValue<BuildingPurpose>
                {
                    Name = nameof(BuildingPurpose.Name),
                    Value = "Жилищна сграда",
                }
            ],
            [
                new SeedValue<BuildingPurpose>
                {
                    Name = nameof(BuildingPurpose.Name),
                    Value = "Смесен тип",
                }
            ],
            [
                new SeedValue<BuildingPurpose>
                {
                    Name = nameof(BuildingPurpose.Name),
                    Value = "Самостоятелна сграда",
                }
            ],
        ];
}