namespace RealtoCrm.EntityFrameworkCore.Migrations.Seed.All.Nomenclatures;

using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using RealtoCrm.EntityFrameworkCore.Migrations.Seed.All.Base;
using RealtoCrm.Nomenclatures;

public class LeaseTermsCreator(DbContext dbContext)
    : BaseMigrationNomenclaturesDbBuilder<LeaseTerm>(dbContext)
{
    protected override IEnumerable<IEnumerable<SeedValue<LeaseTerm>>> ObjectData
        =>
        [
            [
                new SeedValue<LeaseTerm>
                {
                    Name = nameof(LeaseTerm.Name),
                    Value = "Дългосрочен",
                },
            ],
            [
                new SeedValue<LeaseTerm>
                {
                    Name = nameof(LeaseTerm.Name),
                    Value = "Краткосрочен",
                },
            ],
        ];
}