namespace RealtoCrm.EntityFrameworkCore.Migrations.Seed.All.Nomenclatures;

using System.Collections.Generic;
using Base;
using Microsoft.EntityFrameworkCore;
using RealtoCrm.Nomenclatures;

using static RealtoCrm.CosherConsts.ConstructionTypes;

public class ConstructionTypesCreator(DbContext dbContext)
    : BaseMigrationNomenclaturesDbBuilder<ConstructionType>(dbContext)
{
    protected override IEnumerable<IEnumerable<SeedValue<ConstructionType>>> ObjectData
        =>
        [
            [
                new SeedValue<ConstructionType>
                {
                    Name = nameof(ConstructionType.Name),
                    Value = PanelConstructionTypeName,
                }
            ],
            [
                new SeedValue<ConstructionType>
                {
                    Name = nameof(ConstructionType.Name),
                    Value = BrickConstructionTypeName,
                }
            ],
            [
                new SeedValue<ConstructionType>
                {
                    Name = nameof(ConstructionType.Name),
                    Value = ConcreteWithSteelConstructionTypeName,
                }
            ],
            [
                new SeedValue<ConstructionType>
                {
                    Name = nameof(ConstructionType.Name),
                    Value = ConcreteConstructionTypeName,
                }
            ],
            [
                new SeedValue<ConstructionType>
                {
                    Name = nameof(ConstructionType.Name),
                    Value = OtherConstructionTypeName,
                }
            ],
        ];
}