namespace RealtoCrm.EntityFrameworkCore.Migrations.Seed.All.Base;

using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using CsvHelper;
using CsvHelper.Configuration;
using System.Linq.Expressions;

public abstract class BaseCsvDbBuilder<T> : BaseMigrationDbBuilder<T> where T : class
{
    private const string CvsDirectoryName = "csvs";

    protected abstract string FileName { get; }

    protected override IEnumerable<IEnumerable<SeedValue<T>>> ObjectData { get; }

    protected abstract List<string> CsvColumnNames { get; }

    protected override bool DoBatchSaveChanges => true;

    protected BaseCsvDbBuilder(DbContext context)
        : base(context)
    {
        this.PreloadData().Wait();
        this.ObjectData = this.LoadData().GetAwaiter().GetResult().ToList();
    }

    protected async virtual Task PreloadData()
    {
    }

    protected async Task<IEnumerable<IEnumerable<SeedValue<T>>>> LoadData()
    {
        var assembly = Assembly.GetExecutingAssembly();
        var buildDirectory = Directory.GetParent(assembly.Location)?.ToString();
        var filePath = Path.Combine(buildDirectory, CvsDirectoryName, this.FileName);

        var config = new CsvConfiguration(CultureInfo.InvariantCulture)
        {
            HasHeaderRecord = true,
            MissingFieldFound = null,
            HeaderValidated = null, // Prevent throwing on header validation
            IgnoreBlankLines = true,
            TrimOptions = TrimOptions.Trim, // Trim whitespace from fields
        };

        await using var stream = new FileStream(filePath, FileMode.Open, FileAccess.Read, FileShare.Read);
        using var reader = new StreamReader(stream, Encoding.UTF8);
        using var csv = new CsvReader(reader, config);
        if (!await csv.ReadAsync())
        {
            throw new InvalidOperationException("CSV file is empty");
        }

        csv.ReadHeader();
        var seedValues = new List<IEnumerable<SeedValue<T>>>();

        var rowNumber = 0;
        while (await csv.ReadAsync())
        {
            rowNumber++;
            var record = await this.ParseRecordAsync(csv);
            if (record != null)
            {
                seedValues.Add(record);
            }
        }

        return seedValues;
    }

    protected virtual string GetPropertyName(string columnName)
        => columnName;

    private async Task<IEnumerable<SeedValue<T>>> ParseRecordAsync(CsvReader csv)
    {
        var values = new List<SeedValue<T>>();
        foreach (var columnName in this.CsvColumnNames)
        {
            var value = await this.ParseFieldValueAsync(csv, columnName);
            values.Add(new SeedValue<T>()
            {
                Name = this.GetPropertyName(columnName),
                Value = value
            });
        }

        return values;
    }

    private async Task<object> ParseFieldValueAsync(CsvReader csv, string columnName)
    {
        var field = csv.GetField(columnName);
        if (string.IsNullOrEmpty(field))
        {
            return null;
        }

        return await Task.Run(() => this.ParseValue(columnName, field, csv));
    }

    protected virtual object ParseValue(string columnName, object originalValue, CsvReader csv)
    {
        var propertyInfo = typeof(T).GetProperty(columnName);
        if (propertyInfo == null)
        {
            throw new InvalidOperationException($"Property {columnName} not found on type {typeof(T).Name}");
        }

        if (originalValue == null || string.IsNullOrWhiteSpace(originalValue.ToString()))
        {
            return null;
        }

        var propertyType = Nullable.GetUnderlyingType(propertyInfo.PropertyType) ?? propertyInfo.PropertyType;
        return Convert.ChangeType(originalValue, propertyType);
    }
}