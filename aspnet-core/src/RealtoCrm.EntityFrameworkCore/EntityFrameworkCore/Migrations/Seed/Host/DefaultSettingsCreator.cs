namespace RealtoCrm.EntityFrameworkCore.Migrations.Seed.Host;

using System.Linq;
using System.Linq.Dynamic.Core;
using Abp.Configuration;
using Abp.Localization;
using Abp.MultiTenancy;
using Abp.Net.Mail;
using Microsoft.EntityFrameworkCore;

public class DefaultSettingsCreator(RealtoCrmDbContext context)
{
    public void Create()
    {
        int? tenantId = null;

        // ReSharper disable once ConditionIsAlwaysTrueOrFalse
        if (!RealtoCrmConsts.MultiTenancyEnabled)
#pragma warning disable 162
        {
            tenantId = MultiTenancyConsts.DefaultTenantId;
        }
#pragma warning restore 162
        //Languages
        this.AddSettingIfNotExists(LocalizationSettingNames.DefaultLanguage, "bg-BG", tenantId);
    }

    private void AddSettingIfNotExists(string name, string value, int? tenantId = null)
    {
        if (context.Settings.IgnoreQueryFilters().Any(s => s.Name == name && s.TenantId == tenantId && s.UserId == null))
        {
            return;
        }

        context.Settings.Add(new Setting(tenantId, null, name, value));
        context.SaveChanges();
    }
}