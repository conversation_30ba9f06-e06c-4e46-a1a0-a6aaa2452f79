namespace RealtoCrm.EntityFrameworkCore.Configurations.Projects;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Projects;

internal class ProjectEstateTypeConfiguration : IEntityTypeConfiguration<ProjectEstateType>
{
    public void Configure(EntityTypeBuilder<ProjectEstateType> builder)
    {
        builder
            .HasKey(pe => new { pe.ProjectId, pe.EstateTypeId });

        builder
            .Property(pe => pe.Number)
            .IsRequired();

        builder
            .HasOne(pe => pe.Project)
            .WithMany(p => p.ProjectsEstateTypes)
            .HasForeignKey(pe => pe.ProjectId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(pe => pe.EstateType)
            .WithMany(e => e.ProjectsEstateTypes)
            .HasForeignKey(pe => pe.EstateTypeId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}