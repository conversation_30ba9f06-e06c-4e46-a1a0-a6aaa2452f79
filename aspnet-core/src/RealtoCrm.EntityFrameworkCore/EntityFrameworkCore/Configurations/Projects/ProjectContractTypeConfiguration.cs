namespace RealtoCrm.EntityFrameworkCore.Configurations.Projects;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Projects;

internal class ProjectContractTypeConfiguration : IEntityTypeConfiguration<ProjectContractType>
{
    public void Configure(EntityTypeBuilder<ProjectContractType> builder)
    {
        builder
            .HasKey(pc => new { pc.ProjectId, pc.ContractTypeId });

        builder
            .HasOne(pc => pc.Project)
            .WithMany(p => p.ProjectsContractTypes)
            .HasForeignKey(pc => pc.ProjectId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(pc => pc.ContractType)
            .WithMany(ct => ct.ProjectsContractTypes)
            .HasForeignKey(pc => pc.ContractTypeId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}