using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Deposits;

namespace RealtoCrm.EntityFrameworkCore.Configurations.Deposits;

public class DepositMappingConfiguration: IEntityTypeConfiguration<DepositMapping>
{
    public void Configure(EntityTypeBuilder<DepositMapping> builder)
    {
        builder
            .HasKey(cm => cm.Id);
        
        builder
            .HasOne(om => om.Deposit)
            .WithOne(o => o.DepositMapping)
            .HasForeignKey<DepositMapping>(om => om.DepositId)
            .OnDelete(DeleteBehavior.Restrict);
        
        builder.Property(om => om.AsiId)
            .IsRequired(false);

        builder.Property(om => om.EaId)
            .IsRequired(false);

        builder.Property(om => om.AdminId)
            .IsRequired(false);
    }
}