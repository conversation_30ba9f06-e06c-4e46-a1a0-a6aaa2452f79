namespace RealtoCrm.EntityFrameworkCore.Configurations.PropertyToEstateCategorySettings;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.PropertyToEstateCategorySettings;
using static ModelConstants.Common;

public class PropertyToEstateCategorySettingConfiguration : IEntityTypeConfiguration<PropertyToEstateCategorySetting>
{
    public void Configure(EntityTypeBuilder<PropertyToEstateCategorySetting> builder)
    {
        builder
            .Property(o => o.PropertyName)
            .HasMaxLength(MaxNameLength)
            .IsRequired();

        builder
            .HasOne(o => o.Category)
            .WithMany()
            .HasForeignKey(o => o.CategoryId)
            .IsRequired();

        builder
            .Property(o => o.IsVisible)
            .HasDefaultValue(true)
            .IsRequired();

        builder
            .HasOne(o => o.Tenant)
            .WithMany()
            .HasForeignKey(o => o.TenantId)
            .IsRequired();
    }
}