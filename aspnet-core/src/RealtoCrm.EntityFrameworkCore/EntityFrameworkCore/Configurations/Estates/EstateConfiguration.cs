namespace RealtoCrm.EntityFrameworkCore.Configurations.Estates;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Estates;
using static RealtoCrm.Estates.ModelConstants.Estate;

internal class EstateConfiguration : IEntityTypeConfiguration<Estate>
{
    public void Configure(EntityTypeBuilder<Estate> builder)
    {
        builder
            .HasKey(e => e.Id);

        builder
            .Property(e => e.CadastralNumber)
            .HasMaxLength(MaxCadastralNumberLength);

        builder
            .Property(e => e.ZonedPropertyId)
            .HasMaxLength(MaxZonedPropertyIdLength);

        builder
            .Property(e => e.ZonedPropertyNumber)
            .HasMaxLength(MaxZonedPropertyNumberLength);

        builder
            .HasOne(e => e.Type)
            .WithMany(t => t.Estates)
            .HasForeignKey(e => e.TypeId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(e => e.Category)
            .WithMany()
            .HasForeignKey(e => e.CategoryId)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(e => e.Subcategory)
            .WithMany()
            .HasForeignKey(e => e.SubcategoryId)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(e => e.Address)
            .WithMany()
            .HasForeignKey(e => e.AddressId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(e => e.ConstructionType)
            .WithMany(c => c.Estates)
            .HasForeignKey(e => e.ConstructionTypeId)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(e => e.BuildingClass)
            .WithMany(b => b.Estates)
            .HasForeignKey(e => e.BuildingClassId)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(e => e.Condition)
            .WithMany(c => c.Estates)
            .HasForeignKey(e => e.ConditionId)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(e => e.CompletionLevel)
            .WithMany(c => c.Estates)
            .HasForeignKey(e => e.CompletionLevelId)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(e => e.WindowJoinery)
            .WithMany(w => w.Estates)
            .HasForeignKey(e => e.WindowJoineryId)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(o => o.Building)
            .WithMany(b => b.Estates)
            .HasForeignKey(o => o.BuildingId)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(o => o.EstateGroup)
            .WithMany(c => c.Estates)
            .HasForeignKey(o => o.EstateGroupId)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(o => o.Tenant)
            .WithMany()
            .HasForeignKey(o => o.TenantId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
        
        builder
            .HasOne(s => s.EstateDetail)
            .WithOne(d => d.Estate)
            .HasForeignKey<EstateDetail>(d => d.Id)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    
    }
}