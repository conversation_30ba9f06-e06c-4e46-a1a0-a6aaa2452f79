namespace RealtoCrm.EntityFrameworkCore.Configurations.Estates;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Estates;

internal class EstateImageConfiguration : IEntityTypeConfiguration<EstateImage>
{
    public void Configure(EntityTypeBuilder<EstateImage> builder)
    {
        builder
            .HasKey(ei => new { ei.EstateId, ei.ImageId });

        builder
            .HasOne(ei => ei.Estate)
            .WithMany(e => e.EstatesImages)
            .HasForeignKey(ei => ei.EstateId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(ei => ei.Image)
            .WithMany(i => i.EstatesImages)
            .HasForeignKey(ei => ei.ImageId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}