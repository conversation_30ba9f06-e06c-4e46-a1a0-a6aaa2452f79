namespace RealtoCrm.EntityFrameworkCore.Configurations.Deals;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Deals;

internal class DealTenantConfiguration : IEntityTypeConfiguration<DealTenant>
{
    public void Configure(EntityTypeBuilder<DealTenant> builder)
    {
        builder
            .HasKey(dt => dt.Id);

        builder
            .Property(dt => dt.DealTenantId)
            .IsRequired();

        builder
            .HasOne(dt => dt.Deal)
            .WithMany(d => d.DealTenants)
            .HasForeignKey(dt => dt.DealId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(dt => dt.Tenant)
            .WithMany()
            .HasForeignKey(dt => dt.TenantId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}