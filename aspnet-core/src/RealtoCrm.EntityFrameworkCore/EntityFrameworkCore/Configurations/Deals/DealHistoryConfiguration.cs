namespace RealtoCrm.EntityFrameworkCore.Configurations.Deals;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Deals;

internal class DealHistoryConfiguration : IEntityTypeConfiguration<DealHistory>
{
    public void Configure(EntityTypeBuilder<DealHistory> builder)
    {
        builder
            .<PERSON><PERSON><PERSON>(h => h.Id);

        builder
            .Property(h => h.State)
            .IsRequired();

        builder
            .HasOne(h => h.Deal)
            .WithMany(d => d.DealHistories)
            .HasForeignKey(h => h.DealId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}