using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.ExternalSearches;

namespace RealtoCrm.EntityFrameworkCore.Configurations.ExternalSearches;

public class ExternalSearchesMappingConfiguration : IEntityTypeConfiguration<ExternalSearchesMapping>
{
    public void Configure(EntityTypeBuilder<ExternalSearchesMapping> builder)
    {
        builder
            .HasKey(cm => cm.Id);
        
        builder
            .HasOne(c => c.ExternalSearch)
            .WithOne(c => c.ExternalSearchesMapping)
            .HasForeignKey<ExternalSearchesMapping>(cm => cm.ExternalSearchId)
            .OnDelete(DeleteBehavior.Restrict);
        
        builder.Property(pm => pm.AsiId)
            .IsRequired(false);

        builder.Property(pm => pm.EaId)
            .IsRequired(false);

        builder.Property(pm => pm.AdminId)
            .IsRequired(false);
    }
}