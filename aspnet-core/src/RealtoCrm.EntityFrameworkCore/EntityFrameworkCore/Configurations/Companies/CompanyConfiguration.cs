namespace RealtoCrm.EntityFrameworkCore.Configurations.Companies;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Companies;
using static ModelConstants.Common;
using static RealtoCrm.Companies.ModelConstants.Company;

internal class CompanyConfiguration : IEntityTypeConfiguration<Company>
{
    public void Configure(EntityTypeBuilder<Company> builder)
    {
        builder
            .<PERSON><PERSON><PERSON>(c => c.Id);

        builder
            .HasIndex(c => new { c.Name })
            .IsUnique();

        builder
            .Property(c => c.Name)
            .HasMaxLength(MaxNameLength)
            .IsRequired();

        builder
            .Property(c => c.Bulstat)
            .HasMaxLength(MaxBulstatLength);

        builder
            .Property(c => c.MaterialResponsiblePerson)
            .HasMaxLength(MaxMaterialResponsiblePersonLength);

        builder
            .Property(t => t.IsActive)
            .HasDefaultValue(true)
            .IsRequired();

        builder
            .HasOne(c => c.RegistrationAddress)
            .WithMany()
            .HasForeignKey(c => c.RegistrationAddressId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}