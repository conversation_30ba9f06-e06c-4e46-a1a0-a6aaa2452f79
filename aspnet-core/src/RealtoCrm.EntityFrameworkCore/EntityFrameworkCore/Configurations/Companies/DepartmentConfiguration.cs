namespace RealtoCrm.EntityFrameworkCore.Configurations.Companies;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Companies;
using static ModelConstants.Common;

internal class DepartmentConfiguration : IEntityTypeConfiguration<Department>
{
    public void Configure(EntityTypeBuilder<Department> builder)
    {
        builder
            .HasKey(p => p.Id);

        builder
            .Property(p => p.Name)
            .HasMaxLength(MaxNameLength)
            .IsRequired();

        builder
            .Property(t => t.IsActive)
            .HasDefaultValue(true)
            .IsRequired();

        builder
            .HasOne(p => p.Office)
            .WithMany(o => o.Departments)
            .HasForeignKey(p => p.OfficeId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(p => p.Division)
            .WithMany(d => d.Departments)
            .HasForeignKey(p => p.DivisionId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(p => p.Company)
            .WithMany(p => p.Departments)
            .HasForeignKey(p => p.CompanyId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(p => p.Tenant)
            .WithMany(t => t.Departments)
            .HasForeignKey(p => p.TenantId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}