namespace RealtoCrm.EntityFrameworkCore.Configurations.Companies;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Companies;

public class DivisionMappingConfiguration : IEntityTypeConfiguration<DivisionMapping>
{
    public void Configure(EntityTypeBuilder<DivisionMapping> builder)
    {
        builder.HasKey(pm => pm.Id);

        builder
            .HasOne(o => o.Division)
            .WithMany()
            .HasForeignKey(o => o.DivisionId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder.Property(pm => pm.AsiId)
            .IsRequired(false);

        builder.Property(pm => pm.EaId)
            .IsRequired(false);

        builder.Property(pm => pm.AdminId)
            .IsRequired(false);
    }
}