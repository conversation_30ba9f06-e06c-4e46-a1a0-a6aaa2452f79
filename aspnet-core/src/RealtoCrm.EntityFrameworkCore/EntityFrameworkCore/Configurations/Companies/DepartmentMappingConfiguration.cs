namespace RealtoCrm.EntityFrameworkCore.Configurations.Companies;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Companies;

public class DepartmentMappingConfiguration : IEntityTypeConfiguration<DepartmentMapping>
{
    public void Configure(EntityTypeBuilder<DepartmentMapping> builder)
    {
        builder.HasKey(pm => pm.Id);

        builder
            .HasOne(o => o.Department)
            .WithMany()
            .HasForeignKey(o => o.DepartmentId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder.Property(pm => pm.AsiId)
            .IsRequired(false);

        builder.Property(pm => pm.EaId)
            .IsRequired(false);

        builder.Property(pm => pm.AdminId)
            .IsRequired(false);
    }
}