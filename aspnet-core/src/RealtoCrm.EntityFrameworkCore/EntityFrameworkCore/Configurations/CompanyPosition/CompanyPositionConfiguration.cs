namespace RealtoCrm.EntityFrameworkCore.Configurations.CompanyPosition;

using CompanyPositions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

internal class CompanyPositionConfiguration : IEntityTypeConfiguration<CompanyPosition>
{
    public void Configure(EntityTypeBuilder<CompanyPosition> builder)
    {
        builder
            .<PERSON><PERSON><PERSON>(e => e.Id);

        builder.HasOne(cp => cp.Tenant)
            .WithMany()
            .HasForeignKey(cp => cp.TenantId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}