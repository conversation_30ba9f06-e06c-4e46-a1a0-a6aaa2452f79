namespace RealtoCrm.EntityFrameworkCore.Configurations.Buildings;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Buildings;

internal class BuildingConfiguration : IEntityTypeConfiguration<Building>
{
    public void Configure(EntityTypeBuilder<Building> builder)
    {
        builder
            .HasKey(b => b.Id);

        builder
            .Property(b => b.Type)
            .IsRequired();

        builder
            .HasOne(b => b.Address)
            .WithMany()
            .HasForeignKey(b => b.AddressId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(b => b.EstateGroup)
            .WithMany(eg => eg.Buildings)
            .HasForeignKey(b => b.EstateGroupId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(b => b.BuildingPurpose)
            .WithMany(bp => bp.Buildings)
            .HasForeignKey(b => b.BuildingPurposeId)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(b => b.CompletionLevel)
            .WithMany(cl => cl.Buildings)
            .HasForeignKey(b => b.CompletionLevelId)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.Restrict);
    }
}