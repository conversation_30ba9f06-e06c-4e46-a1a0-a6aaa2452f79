namespace RealtoCrm.EntityFrameworkCore.Configurations.Clients;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Clients;
using static ModelConstants.Common;

internal class ClientContactDetailConfiguration : IEntityTypeConfiguration<ClientContactDetail>
{
    public void Configure(EntityTypeBuilder<ClientContactDetail> builder)
    {
        builder
            .<PERSON><PERSON><PERSON>(cd => cd.Id);

        builder
            .Property(dd => dd.Value)
            .HasMaxLength(MaxUrlLength)
            .IsRequired();

        builder
            .HasOne(cd => cd.Client)
            .WithMany(c => c.ContactDetails)
            .HasForeignKey(cd => cd.ClientId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(cd => cd.ContactDetail)
            .WithMany()
            .HasForeignKey(cd => cd.ContactDetailId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}