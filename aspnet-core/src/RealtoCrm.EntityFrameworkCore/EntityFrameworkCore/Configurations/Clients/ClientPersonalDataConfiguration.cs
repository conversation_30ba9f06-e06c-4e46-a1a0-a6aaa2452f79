namespace RealtoCrm.EntityFrameworkCore.Configurations.Clients;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Clients;
using static ModelConstants.Common;
using ClientAnnotations = RealtoCrm.Clients.ModelConstants.Client;

internal class ClientPersonalDataConfiguration : IEntityTypeConfiguration<ClientPersonalData>
{
    public void Configure(EntityTypeBuilder<ClientPersonalData> builder)
    {
        builder
            .HasKey(c => c.Id);

        builder
            .Property(c => c.FirstName)
            .HasMaxLength(ClientAnnotations.MaxNameLength)
            .IsRequired();

        builder
            .Property(c => c.MiddleName)
            .HasMaxLength(ClientAnnotations.MaxNameLength);

        builder
            .Property(c => c.LastName)
            .HasMaxLength(ClientAnnotations.MaxNameLength)
            .IsRequired();

        builder
            .HasOne(c => c.Title)
            .WithMany(t => t.ClientsPersonalData)
            .HasForeignKey(c => c.TitleId)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .Property(c => c.IdentificationNumber)
            .HasMaxLength(MaxIdentificationNumberLength);

        builder
            .Property(c => c.DocumentNumber)
            .HasMaxLength(ClientAnnotations.MaxDocumentNumberLength);

        builder
            .Property(c => c.DocumentAuthority)
            .HasMaxLength(ClientAnnotations.MaxDocumentAuthorityLength);

        builder
            .HasOne(c => c.Gender)
            .WithMany(g => g.ClientPersonalData)
            .HasForeignKey(c => c.GenderId)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(c => c.MaritalStatus)
            .WithMany(ms => ms.ClientPersonalData)
            .HasForeignKey(c => c.MaritalStatusId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}