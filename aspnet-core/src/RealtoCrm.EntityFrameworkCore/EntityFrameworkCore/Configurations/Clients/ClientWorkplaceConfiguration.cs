namespace RealtoCrm.EntityFrameworkCore.Configurations.Clients;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Clients;

internal class ClientWorkplaceConfiguration : IEntityTypeConfiguration<ClientWorkplace>
{
    public void Configure(EntityTypeBuilder<ClientWorkplace> builder)
    {
        builder
            .HasKey(cw => new { cw.ClientId, cw.WorkplaceId });

        builder
            .HasOne(cw => cw.Client)
            .WithMany(c => c.ClientsWorkplaces)
            .HasForeignKey(cw => cw.ClientId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(cw => cw.Workplace)
            .WithMany(c => c.ClientsWorkplaces)
            .HasForeignKey(cw => cw.WorkplaceId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}