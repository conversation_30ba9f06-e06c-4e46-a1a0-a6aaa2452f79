using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Offers;

namespace RealtoCrm.EntityFrameworkCore.Configurations.Offers;

public class ExternalOfferMappingConfiguration : IEntityTypeConfiguration<ExternalOfferMapping>
{
    public void Configure(EntityTypeBuilder<ExternalOfferMapping> builder)
    {
        builder
            .HasKey(cm => cm.Id);
        
        builder
            .HasOne(om => om.ExternalOffer)
            .WithOne(o => o.ExternalOfferMapping)
            .HasForeignKey<ExternalOfferMapping>(om => om.ExternalOfferId)
            .OnDelete(DeleteBehavior.Restrict);
        
        builder.Property(om => om.AsiId)
            .IsRequired(false);

        builder.Property(om => om.EaId)
            .IsRequired(false);

        builder.Property(om => om.AdminId)
            .IsRequired(false);
    }
}