namespace RealtoCrm.EntityFrameworkCore.Configurations.Offers;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Offers;

internal class OfferDetailConfiguration : IEntityTypeConfiguration<OfferDetail>
{
    public void Configure(EntityTypeBuilder<OfferDetail> builder)
    {
        builder
            .<PERSON><PERSON><PERSON>(d => d.Id);

        builder
            .HasOne(d => d.LeaseTerm)
            .WithMany()
            .HasForeignKey(d => d.LeaseTermId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}