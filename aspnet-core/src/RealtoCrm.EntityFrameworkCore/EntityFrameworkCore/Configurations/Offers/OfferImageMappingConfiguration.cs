namespace RealtoCrm.EntityFrameworkCore.Configurations.Offers;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Offers;

internal class OfferImageMappingConfiguration : IEntityTypeConfiguration<OfferImageMapping>
{
    public void Configure(EntityTypeBuilder<OfferImageMapping> builder)
    {
        builder
            .<PERSON><PERSON><PERSON>(om => om.Id);

        builder
            .HasOne(om => om.Offer)
            .WithMany(o => o.OfferImageMappings)
            .HasForeignKey(om => om.OfferId)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .Property(om => om.AsiOfferId)
            .IsRequired(false);
    }
}