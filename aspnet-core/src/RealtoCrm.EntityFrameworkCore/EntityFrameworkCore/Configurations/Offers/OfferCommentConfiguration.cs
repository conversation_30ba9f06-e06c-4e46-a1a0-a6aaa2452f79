namespace RealtoCrm.EntityFrameworkCore.Configurations.Offers;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Offers;

internal class OfferCommentConfiguration : IEntityTypeConfiguration<OfferComment>
{
    public void Configure(EntityTypeBuilder<OfferComment> builder)
    {
        builder
            .HasKey(oc => new { oc.OfferId, oc.CommentId });

        builder
            .HasOne(oc => oc.Offer)
            .WithMany(o => o.OffersComments)
            .HasForeignKey(oc => oc.OfferId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(oc => oc.Comment)
            .WithMany(c => c.OffersComments)
            .HasForeignKey(oc => oc.CommentId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}