namespace RealtoCrm.EntityFrameworkCore.Configurations.Offers;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Offers;

public class OfferMappingConfiguration : IEntityTypeConfiguration<OfferMapping>
{
    public void Configure(EntityTypeBuilder<OfferMapping> builder)
    {
        builder
            .HasKey(cm => cm.Id);
        
        builder
            .HasOne(om => om.Offer)
            .WithOne(o => o.OfferMapping)
            .HasForeignKey<OfferMapping>(om => om.OfferId)
            .OnDelete(DeleteBehavior.Restrict);
        
        builder.Property(om => om.AsiId)
            .IsRequired(false);

        builder.Property(om => om.EaId)
            .IsRequired(false);

        builder.Property(om => om.AdminId)
            .IsRequired(false);
    }
}