namespace RealtoCrm.EntityFrameworkCore.Configurations.Offers;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Offers;

internal class OfferImageConfiguration : IEntityTypeConfiguration<OfferImage>
{
    public void Configure(EntityTypeBuilder<OfferImage> builder)
    {
        builder
            .HasKey(oi => new { oi.OfferId, oi.ImageId });

        builder
            .HasOne(oi => oi.Offer)
            .WithMany(o => o.OffersImages)
            .HasForeignKey(oi => oi.OfferId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(oi => oi.Image)
            .WithMany(i => i.OffersImages)
            .HasForeignKey(oi => oi.ImageId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}