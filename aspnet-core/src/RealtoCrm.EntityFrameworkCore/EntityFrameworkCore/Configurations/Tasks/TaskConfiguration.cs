namespace RealtoCrm.EntityFrameworkCore.Configurations.Tasks;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Tasks;
using static RealtoCrm.Tasks.ModelConstants.Task;

internal class TaskConfiguration : IEntityTypeConfiguration<Task>
{
    public void Configure(EntityTypeBuilder<Task> builder)
    {
        builder
            .HasKey(t => t.Id);

        builder
            .Property(t => t.IsDone)
            .IsRequired();

        builder
            .Property(t => t.Text)
            .HasMaxLength(MaxTextLength)
            .IsRequired();

        builder
            .HasOne(t => t.Employee)
            .WithMany(e => e.Tasks)
            .HasForeignKey(t => t.EmployeeId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}