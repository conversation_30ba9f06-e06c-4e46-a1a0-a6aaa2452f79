namespace RealtoCrm.EntityFrameworkCore.Configurations.OfferEmployeesChangeHistory;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OffersEmployeesChangeHistory;

public class OfferEmployeesChangeHistoryConfiguration : IEntityTypeConfiguration<OfferEmployeesChangeHistory>
{
    public void Configure(EntityTypeBuilder<OfferEmployeesChangeHistory> builder)
    {
        builder.<PERSON><PERSON><PERSON>(oe => oe.Id);

        builder.HasOne(oe => oe.Offer)
            .WithMany(oe => oe.OffersEmployeesChangeHistory)
            .HasForeignKey(oe => oe.OfferId)
            .OnDelete(DeleteBehavior.Restrict);

    }
}