namespace RealtoCrm.EntityFrameworkCore.Configurations.Viewings;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Viewings;

internal class ViewingConfiguration : IEntityTypeConfiguration<Viewing>
{
    public void Configure(EntityTypeBuilder<Viewing> builder)
    {
        builder
            .HasKey(v => v.Id);

        builder
            .Property(v => v.StartDate)
            .IsRequired();

        builder
            .HasOne(v => v.Status)
            .WithMany(s => s.Viewings)
            .HasForeignKey(v => v.StatusId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(v => v.Match)
            .WithMany(m => m.Viewings)
            .HasForeignKey(v => v.MatchId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(v => v.Search)
            .WithMany(s => s.Viewings)
            .HasForeignKey(v => v.SearchId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(v => v.SearchClient)
            .WithMany(c => c.SearchViewings)
            .HasForeignKey(v => v.SearchClientId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(v => v.SearchEmployee)
            .WithMany(e => e.SearchViewings)
            .HasForeignKey(v => v.SearchEmployeeId)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(v => v.Offer)
            .WithMany(o => o.Viewings)
            .HasForeignKey(v => v.OfferId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(v => v.OfferClient)
            .WithMany(c => c.OfferViewings)
            .HasForeignKey(v => v.OfferClientId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(v => v.OfferEmployee)
            .WithMany(e => e.OfferViewings)
            .HasForeignKey(v => v.OfferEmployeeId)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(v => v.ExternalOffer)
            .WithMany(o => o.Viewings)
            .HasForeignKey(v => v.ExternalOfferId)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(v => v.ExternalSearch)
            .WithMany(o => o.Viewings)
            .HasForeignKey(v => v.ExternalSearchId)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(v => v.Project)
            .WithMany(o => o.Viewings)
            .HasForeignKey(v => v.ProjectId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}