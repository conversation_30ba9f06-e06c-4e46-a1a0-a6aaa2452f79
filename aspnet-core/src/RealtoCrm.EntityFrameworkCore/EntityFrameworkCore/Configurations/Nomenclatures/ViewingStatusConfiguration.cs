namespace RealtoCrm.EntityFrameworkCore.Configurations.Nomenclatures;

using Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Nomenclatures;

internal class ViewingStatusConfiguration : IEntityTypeConfiguration<ViewingStatus>
{
    public void Configure(EntityTypeBuilder<ViewingStatus> builder)
        => builder.HasNomenclature();
}