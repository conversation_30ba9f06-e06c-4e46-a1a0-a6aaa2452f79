namespace RealtoCrm.EntityFrameworkCore.Configurations.Nomenclatures;

using Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Nomenclatures;

internal class DistrictConfiguration : IEntityTypeConfiguration<District>
{
    public void Configure(EntityTypeBuilder<District> builder)
    {
        builder
            .HasNomenclature();
        
        builder
            .HasOne(n => n.PopulatedPlace)
            .WithMany(c => c.Districts)
            .HasForeignKey(n => n.PopulatedPlaceId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}