using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.EntityFrameworkCore.Extensions;
using RealtoCrm.Nomenclatures;

namespace RealtoCrm.EntityFrameworkCore.Configurations.Nomenclatures;

public class FloorPreferenceConfiguration : IEntityTypeConfiguration<FloorPreference>
{
    public void Configure(EntityTypeBuilder<FloorPreference> builder)
        => builder.HasNomenclature();
}