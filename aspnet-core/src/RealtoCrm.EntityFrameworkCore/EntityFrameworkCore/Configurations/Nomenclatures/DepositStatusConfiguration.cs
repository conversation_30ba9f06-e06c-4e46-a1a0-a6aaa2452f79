namespace RealtoCrm.EntityFrameworkCore.Configurations.Nomenclatures;

using Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Nomenclatures;

internal class DepositStatusConfiguration : IEntityTypeConfiguration<DepositStatus>
{
    public void Configure(EntityTypeBuilder<DepositStatus> builder)
        => builder.HasNomenclature();
}