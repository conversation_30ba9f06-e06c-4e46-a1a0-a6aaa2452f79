namespace RealtoCrm.EntityFrameworkCore.Configurations.Nomenclatures;

using Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Nomenclatures;

internal class MaritalStatusConfiguration : IEntityTypeConfiguration<MaritalStatus>
{
    public void Configure(EntityTypeBuilder<MaritalStatus> builder)
        => builder.HasNomenclature();
}