using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Nomenclatures;

namespace RealtoCrm.EntityFrameworkCore.Configurations.Nomenclatures;

public class ExternalAgencyMappingConfiguration: IEntityTypeConfiguration<ExternalAgencyMapping>
{
    public void Configure(EntityTypeBuilder<ExternalAgencyMapping> builder)
    {
        builder.<PERSON><PERSON><PERSON>(pm => pm.Id);

        builder
            .HasOne(o => o.ExternalAgency)
            .WithMany()
            .HasForeignKey(o => o.ExternalAgencyId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder.Property(pm => pm.AsiId)
            .IsRequired(false);

        builder.Property(pm => pm.EaId)
            .IsRequired(false);

        builder.Property(pm => pm.AdminIds)
            .IsRequired(false);
    }
}