using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Nomenclatures;

namespace RealtoCrm.EntityFrameworkCore.Configurations.Nomenclatures;

public class StreetMappingConfiguration : IEntityTypeConfiguration<StreetMapping>
{
    public void Configure(EntityTypeBuilder<StreetMapping> builder)
    {
        builder.HasKey(pm => pm.Id);
        
        builder
            .HasOne(o => o.Street)
            .WithMany()
            .HasForeignKey(o => o.StreetId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder.Property(pm => pm.AsiId)
            .IsRequired(false);
        
        builder.Property(pm => pm.EaId)
            .IsRequired(false);

        builder.Property(pm => pm.AdminId)
            .IsRequired(false);
    }
}