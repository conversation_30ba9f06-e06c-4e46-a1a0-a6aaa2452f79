namespace RealtoCrm.EntityFrameworkCore.Configurations.Nomenclatures;

using Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Nomenclatures;
using static RealtoCrm.Nomenclatures.ModelConstants.Address;

internal class CountryConfiguration : IEntityTypeConfiguration<Country>
{
    public void Configure(EntityTypeBuilder<Country> builder)
    {
        builder
            .HasNomenclature();

        builder
            .Property(c => c.CodeAlpha2)
            .HasMaxLength(MaxCodeAlpha2Length)
            .IsRequired();

        builder
            .Property(c => c.CodeAlpha3)
            .HasMaxLength(MaxCodeAlpha3Length)
            .IsRequired();
    }
}