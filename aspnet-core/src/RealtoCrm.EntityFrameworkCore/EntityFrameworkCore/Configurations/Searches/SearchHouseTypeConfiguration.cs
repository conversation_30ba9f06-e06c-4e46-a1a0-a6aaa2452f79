namespace RealtoCrm.EntityFrameworkCore.Configurations.Searches;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Searches;

internal class SearchHouseTypeConfiguration : IEntityTypeConfiguration<SearchHouseType>
{
    public void Configure(EntityTypeBuilder<SearchHouseType> builder)
    {
        builder
            .HasKey(sh => new { sh.SearchId, sh.HouseTypeId });

        builder
            .HasOne(sh => sh.Search)
            .WithMany(s => s.SearchesHouseTypes)
            .HasForeignKey(sh => sh.SearchId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(sh => sh.HouseType)
            .WithMany(h => h.SearchesHouseTypes)
            .HasForeignKey(sh => sh.HouseTypeId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}