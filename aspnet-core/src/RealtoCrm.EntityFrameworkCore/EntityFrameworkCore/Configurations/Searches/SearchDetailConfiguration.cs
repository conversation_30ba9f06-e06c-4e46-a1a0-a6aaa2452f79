using RealtoCrm.Searches;

namespace RealtoCrm.EntityFrameworkCore.Configurations.Searchs;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

internal class SearchDetailConfiguration : IEntityTypeConfiguration<SearchDetail>
{
    public void Configure(EntityTypeBuilder<SearchDetail> builder)
    {
        builder
            .<PERSON><PERSON><PERSON>(d => d.Id);

        builder
            .HasOne(d => d.LeaseTerm)
            .WithMany()
            .HasForeignKey(d => d.LeaseTermId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}