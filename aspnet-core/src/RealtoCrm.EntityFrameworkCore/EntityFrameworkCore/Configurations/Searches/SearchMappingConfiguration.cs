namespace RealtoCrm.EntityFrameworkCore.Configurations.Searches;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Searches;

public class SearchMappingConfiguration : IEntityTypeConfiguration<SearchMapping>
{
    public void Configure(EntityTypeBuilder<SearchMapping> builder)
    {
        builder
            .HasKey(cm => cm.Id);
        
        builder
            .HasOne(om => om.Search)
            .WithOne(o => o.SearchMapping)
            .HasForeignKey<SearchMapping>(om => om.SearchId)
            .OnDelete(DeleteBehavior.Restrict);
        
        builder.Property(om => om.AsiId)
            .IsRequired(false);

        builder.Property(om => om.EaId)
            .IsRequired(false);

        builder.Property(om => om.AdminId)
            .IsRequired(false);
    }
}