namespace RealtoCrm.EntityFrameworkCore.Configurations.Searches;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Searches;

internal class SearchDealMotiveConfiguration : IEntityTypeConfiguration<SearchDealMotive>
{
    public void Configure(EntityTypeBuilder<SearchDealMotive> builder)
    {
        builder
            .HasKey(sd => new { sd.SearchId, sd.DealMotiveId });

        builder
            .HasOne(sd => sd.Search)
            .WithMany(s => s.SearchesDealMotives)
            .HasForeignKey(sd => sd.SearchId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(sd => sd.DealMotive)
            .WithMany(p => p.SearchesDealMotives)
            .HasForeignKey(sd => sd.DealMotiveId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}