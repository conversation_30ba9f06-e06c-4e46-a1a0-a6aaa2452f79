namespace RealtoCrm.EntityFrameworkCore.Configurations.Searches;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Searches;

public class SearchContractTypeConfiguration : IEntityTypeConfiguration<SearchContractType>
{
    public void Configure(EntityTypeBuilder<SearchContractType> builder)
    {
        builder
            .HasKey(sc => new { sc.SearchId, sc.ContractTypeId });

        builder
            .HasOne(sc => sc.Search)
            .WithMany(s => s.SearchesContractTypes)
            .HasForeignKey(sc => sc.SearchId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(sc => sc.ContractType)
            .WithMany(c => c.SearchesContractTypes)
            .HasForeignKey(sc => sc.ContractTypeId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}