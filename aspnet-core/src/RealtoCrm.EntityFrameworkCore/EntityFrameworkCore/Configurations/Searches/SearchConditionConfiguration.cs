namespace RealtoCrm.EntityFrameworkCore.Configurations.Searches;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Searches;

internal class SearchConditionConfiguration : IEntityTypeConfiguration<SearchCondition>
{
    public void Configure(EntityTypeBuilder<SearchCondition> builder)
    {
        builder
            .HasKey(sc => new { sc.SearchId, sc.ConditionId });

        builder
            .HasOne(sc => sc.Search)
            .WithMany(s => s.SearchesConditions)
            .HasForeignKey(sc => sc.SearchId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(sc => sc.Condition)
            .WithMany(c => c.SearchesConditions)
            .HasForeignKey(sc => sc.ConditionId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}