namespace RealtoCrm.EntityFrameworkCore.Configurations.Searches;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Searches;

internal class SearchRegulationConfiguration : IEntityTypeConfiguration<SearchRegulation>
{
    public void Configure(EntityTypeBuilder<SearchRegulation> builder)
    {
        builder
            .HasKey(sr => new { sr.SearchId, sr.RegulationId });

        builder
            .HasOne(sr => sr.Search)
            .WithMany(s => s.SearchesRegulations)
            .HasForeignKey(sr => sr.SearchId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(sr => sr.Regulation)
            .WithMany(p => p.SearchesRegulations)
            .HasForeignKey(sr => sr.RegulationId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}