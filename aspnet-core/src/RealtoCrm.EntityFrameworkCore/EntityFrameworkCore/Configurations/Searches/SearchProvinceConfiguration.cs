namespace RealtoCrm.EntityFrameworkCore.Configurations.Searches;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Searches;

internal class SearchProvinceConfiguration : IEntityTypeConfiguration<SearchProvince>
{
    public void Configure(EntityTypeBuilder<SearchProvince> builder)
    {
        builder
            .HasKey(sp => new { sp.SearchId, sp.ProvinceId });

        builder
            .HasOne(sp => sp.Search)
            .WithMany(s => s.SearchesProvinces)
            .HasForeignKey(sp => sp.SearchId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(sp => sp.Province)
            .WithMany(p => p.SearchesProvinces)
            .HasForeignKey(sp => sp.ProvinceId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}