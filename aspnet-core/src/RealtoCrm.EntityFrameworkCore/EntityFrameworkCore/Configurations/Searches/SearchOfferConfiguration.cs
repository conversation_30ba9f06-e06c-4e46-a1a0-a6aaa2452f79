namespace RealtoCrm.EntityFrameworkCore.Configurations.Searches;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Searches;

internal class SearchOfferConfiguration : IEntityTypeConfiguration<SearchOffer>
{
    public void Configure(EntityTypeBuilder<SearchOffer> builder)
    {
        builder
            .HasKey(so => new { so.SearchId, so.OfferId });

        builder
            .HasOne(so => so.Search)
            .WithMany(s => s.SearchesOffers)
            .HasForeignKey(so => so.SearchId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(so => so.Offer)
            .WithMany(o => o.SearchesOffers)
            .HasForeignKey(so => so.OfferId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}