using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Primitives;
using RealtoCrm.Authorization.Users;
using RealtoCrm.Identity;
using RealtoCrm.Web.OpenIddict.Claims;
using RealtoCrm.Web.OpenIddict.ViewModels.Authorize;
using OpenIddict.Abstractions;
using OpenIddict.Server.AspNetCore;

namespace RealtoCrm.Web.OpenIddict.Controllers;

[Route("connect/authorize")]
[ApiExplorerSettings(IgnoreApi = true)]
public class AuthorizeController(
    SignInManager signInManager,
    UserManager userManager,
    IOpenIddictApplicationManager applicationManager,
    IOpenIddictAuthorizationManager authorizationManager,
    IOpenIddictScopeManager scopeManager,
    IOpenIddictTokenManager tokenManager,
    AbpOpenIddictClaimsPrincipalManager openIddictClaimsPrincipalManager)
    : AbpOpenIdDictControllerBase(signInManager, userManager,
        applicationManager, authorizationManager, scopeManager, tokenManager, openIddictClaimsPrincipalManager)
{
    [HttpGet, HttpPost]
    [IgnoreAntiforgeryToken]
    public virtual async Task<IActionResult> HandleAsync()
    {
        var request = await this.GetOpenIddictServerRequestAsync(this.HttpContext);

        // If prompt=login was specified by the client application,
        // immediately return the user agent to the login page.
        if (request.HasPrompt(OpenIddictConstants.Prompts.Login))
        {
            // To avoid endless login -> authorization redirects, the prompt=login flag
            // is removed from the authorization request payload before redirecting the user.
            var prompt = string.Join(" ", request.GetPrompts().Remove(OpenIddictConstants.Prompts.Login));

            var parameters = this.Request.HasFormContentType
                ? this.Request.Form.Where(parameter => parameter.Key != OpenIddictConstants.Parameters.Prompt).ToList()
                : this.Request.Query.Where(parameter => parameter.Key != OpenIddictConstants.Parameters.Prompt).ToList();

            parameters.Add(KeyValuePair.Create(OpenIddictConstants.Parameters.Prompt, new StringValues(prompt)));

            return this.Challenge(
                authenticationSchemes: IdentityConstants.ApplicationScheme,
                properties: new AuthenticationProperties
                {
                    RedirectUri = this.Request.PathBase + this.Request.Path + QueryString.Create(parameters)
                });
        }

        // Retrieve the user principal stored in the authentication cookie.
        // If a max_age parameter was provided, ensure that the cookie is not too old.
        // If the user principal can't be extracted or the cookie is too old, redirect the user to the login page.
        var result = await this.HttpContext.AuthenticateAsync(IdentityConstants.ApplicationScheme);
        if (result == null || !result.Succeeded ||
            (request.MaxAge != null && result.Properties?.IssuedUtc != null &&
             DateTimeOffset.UtcNow - result.Properties.IssuedUtc > TimeSpan.FromSeconds(request.MaxAge.Value)))
        {
            // If the client application requested promptless authentication,
            // return an error indicating that the user is not logged in.
            if (request.HasPrompt(OpenIddictConstants.Prompts.None))
            {
                return this.Forbid(
                    authenticationSchemes: OpenIddictServerAspNetCoreDefaults.AuthenticationScheme,
                    properties: new AuthenticationProperties(new Dictionary<string, string>
                    {
                        [OpenIddictServerAspNetCoreConstants.Properties.Error] =
                            OpenIddictConstants.Errors.LoginRequired,
                        [OpenIddictServerAspNetCoreConstants.Properties.ErrorDescription] =
                            "The user is not logged in."
                    }));
            }

            return this.Challenge(
                authenticationSchemes: IdentityConstants.ApplicationScheme,
                properties: new AuthenticationProperties
                {
                    RedirectUri = this.Request.PathBase + this.Request.Path + QueryString.Create(this.Request.HasFormContentType ? this.Request.Form.ToList() : this.Request.Query.ToList())
                });
        }

        // Retrieve the profile of the logged in user.
        var user = await this.UserManager.GetUserAsync(result.Principal) ??
                   throw new InvalidOperationException(this.L("TheUserDetailsCannotBbeRetrieved"));

        // Retrieve the application details from the database.
        var application = await this.ApplicationManager.FindByClientIdAsync(request.ClientId) ??
                          throw new InvalidOperationException(this.L("DetailsConcerningTheCallingClientApplicationCannotBeFound"));

        // Retrieve the permanent authorizations associated with the user and the calling client application.
        var authorizations = await this.AuthorizationManager.FindAsync(
            subject: await this.UserManager.GetUserIdAsync(user),
            client: await this.ApplicationManager.GetIdAsync(application),
            status: OpenIddictConstants.Statuses.Valid,
            type: OpenIddictConstants.AuthorizationTypes.Permanent,
            scopes: request.GetScopes()).ToListAsync();

        switch (await this.ApplicationManager.GetConsentTypeAsync(application))
        {
            // If the consent is external (e.g when authorizations are granted by a sysadmin),
            // immediately return an error if no authorization can be found in the database.
            case OpenIddictConstants.ConsentTypes.External when !authorizations.Any():
                return this.Forbid(
                    authenticationSchemes: OpenIddictServerAspNetCoreDefaults.AuthenticationScheme,
                    properties: new AuthenticationProperties(new Dictionary<string, string>
                    {
                        [OpenIddictServerAspNetCoreConstants.Properties.Error] =
                            OpenIddictConstants.Errors.ConsentRequired,
                        [OpenIddictServerAspNetCoreConstants.Properties.ErrorDescription] =
                            "The logged in user is not allowed to access this client application."
                    }));

            // If the consent is implicit or if an authorization was found,
            // return an authorization response without displaying the consent form.
            case OpenIddictConstants.ConsentTypes.Implicit:
            case OpenIddictConstants.ConsentTypes.External when authorizations.Any():
            case OpenIddictConstants.ConsentTypes.Explicit
                when authorizations.Any() && !request.HasPrompt(OpenIddictConstants.Prompts.Consent):
                var principal = await this.SignInManager.CreateUserPrincipalAsync(user);

                // Note: in this sample, the granted scopes match the requested scope
                // but you may want to allow the user to uncheck specific scopes.
                // For that, simply restrict the list of scopes before calling SetScopes.
                principal.SetScopes(request.GetScopes());
                principal.SetResources(await this.ScopeManager.ListResourcesAsync(principal.GetScopes()).ToListAsync());

                // Automatically create a permanent authorization to avoid requiring explicit consent
                // for future authorization or token requests containing the same scopes.
                var authorization = authorizations.LastOrDefault();
                if (authorization == null)
                {
                    authorization = await this.AuthorizationManager.CreateAsync(
                        principal: principal,
                        subject: await this.UserManager.GetUserIdAsync(user),
                        client: await this.ApplicationManager.GetIdAsync(application),
                        type: OpenIddictConstants.AuthorizationTypes.Permanent,
                        scopes: principal.GetScopes());
                }

                principal.SetAuthorizationId(await this.AuthorizationManager.GetIdAsync(authorization));

                await this.OpenIddictClaimsPrincipalManager.HandleAsync(request, principal);

                return this.SignIn(principal, OpenIddictServerAspNetCoreDefaults.AuthenticationScheme);

            // At this point, no authorization was found in the database and an error must be returned
            // if the client application specified prompt=none in the authorization request.
            case OpenIddictConstants.ConsentTypes.Explicit when request.HasPrompt(OpenIddictConstants.Prompts.None):
            case OpenIddictConstants.ConsentTypes.Systematic
                when request.HasPrompt(OpenIddictConstants.Prompts.None):
                return this.Forbid(
                    authenticationSchemes: OpenIddictServerAspNetCoreDefaults.AuthenticationScheme,
                    properties: new AuthenticationProperties(new Dictionary<string, string>
                    {
                        [OpenIddictServerAspNetCoreConstants.Properties.Error] =
                            OpenIddictConstants.Errors.ConsentRequired,
                        [OpenIddictServerAspNetCoreConstants.Properties.ErrorDescription] =
                            "Interactive user consent is required."
                    }));

            // In every other case, render the consent form.
            default:
                return this.View("Authorize", new AuthorizeViewModel
                {
                    ApplicationName = await this.ApplicationManager.GetDisplayNameAsync(application),
                    Scope = request.Scope
                });
        }
    }

    [HttpPost]
    [Authorize]
    [Route("callback")]
    public virtual async Task<IActionResult> HandleCallbackAsync()
    {
        if (await this.HasFormValueAsync("deny"))
        {
            // Notify OpenIddict that the authorization grant has been denied by the resource owner
            // to redirect the user agent to the client application using the appropriate response_mode.
            return this.Forbid(OpenIddictServerAspNetCoreDefaults.AuthenticationScheme);
        }

        var request = await this.GetOpenIddictServerRequestAsync(this.HttpContext);

        // Retrieve the profile of the logged in user.
        var user = await this.UserManager.GetUserAsync(this.User) ??
                   throw new InvalidOperationException(this.L("TheUserDetailsCannotBbeRetrieved"));

        // Retrieve the application details from the database.
        var application = await this.ApplicationManager.FindByClientIdAsync(request.ClientId) ??
                          throw new InvalidOperationException(this.L("DetailsConcerningTheCallingClientApplicationCannotBeFound"));

        // Retrieve the permanent authorizations associated with the user and the calling client application.
        var authorizations = await this.AuthorizationManager.FindAsync(
            subject: await this.UserManager.GetUserIdAsync(user),
            client: await this.ApplicationManager.GetIdAsync(application),
            status: OpenIddictConstants.Statuses.Valid,
            type: OpenIddictConstants.AuthorizationTypes.Permanent,
            scopes: request.GetScopes()).ToListAsync();

        // Note: the same check is already made in the other action but is repeated
        // here to ensure a malicious user can't abuse this POST-only endpoint and
        // force it to return a valid response without the external authorization.
        if (!authorizations.Any() &&
            await this.ApplicationManager.HasConsentTypeAsync(application, OpenIddictConstants.ConsentTypes.External))
        {
            return this.Forbid(
                authenticationSchemes: OpenIddictServerAspNetCoreDefaults.AuthenticationScheme,
                properties: new AuthenticationProperties(new Dictionary<string, string>
                {
                    [OpenIddictServerAspNetCoreConstants.Properties.Error] =
                        OpenIddictConstants.Errors.ConsentRequired,
                    [OpenIddictServerAspNetCoreConstants.Properties.ErrorDescription] =
                        "The logged in user is not allowed to access this client application."
                }));
        }

        var principal = await this.SignInManager.CreateUserPrincipalAsync(user);

        // Note: in this sample, the granted scopes match the requested scope
        // but you may want to allow the user to uncheck specific scopes.
        // For that, simply restrict the list of scopes before calling SetScopes.
        principal.SetScopes(request.GetScopes());
        principal.SetResources(await this.ScopeManager.ListResourcesAsync(principal.GetScopes()).ToListAsync());

        // Automatically create a permanent authorization to avoid requiring explicit consent
        // for future authorization or token requests containing the same scopes.
        var authorization = authorizations.LastOrDefault();
        if (authorization == null)
        {
            authorization = await this.AuthorizationManager.CreateAsync(
                principal: principal,
                subject: await this.UserManager.GetUserIdAsync(user),
                client: await this.ApplicationManager.GetIdAsync(application),
                type: OpenIddictConstants.AuthorizationTypes.Permanent,
                scopes: principal.GetScopes());
        }

        principal.SetAuthorizationId(await this.AuthorizationManager.GetIdAsync(authorization));
        principal.SetScopes(request.GetScopes());
        principal.SetResources(await this.GetResourcesAsync(request.GetScopes()));

        await this.OpenIddictClaimsPrincipalManager.HandleAsync(request, principal);

        // Returning a SignInResult will ask OpenIddict to issue the appropriate access/identity tokens.
        return this.SignIn(principal, OpenIddictServerAspNetCoreDefaults.AuthenticationScheme);
    }
}