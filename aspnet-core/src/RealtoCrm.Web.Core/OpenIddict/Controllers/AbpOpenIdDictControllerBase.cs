using System;
using System.Collections.Generic;
using System.Collections.Immutable;
using System.Linq;
using System.Threading.Tasks;
using Abp.AspNetCore.Mvc.Controllers;
using Microsoft.AspNetCore;
using Microsoft.AspNetCore.Http;
using RealtoCrm.Authorization.Users;
using RealtoCrm.Identity;
using RealtoCrm.Web.OpenIddict.Claims;
using OpenIddict.Abstractions;

namespace RealtoCrm.Web.OpenIddict.Controllers;

public abstract class AbpOpenIdDictControllerBase : AbpController
{
    protected readonly SignInManager SignInManager;
    protected readonly UserManager UserManager;
    protected readonly IOpenIddictApplicationManager ApplicationManager;
    protected readonly IOpenIddictAuthorizationManager AuthorizationManager;
    protected readonly IOpenIddictScopeManager ScopeManager;
    protected readonly IOpenIddictTokenManager TokenManager;
    protected readonly AbpOpenIddictClaimsPrincipalManager OpenIddictClaimsPrincipalManager;

    protected AbpOpenIdDictControllerBase(
        SignInManager signInManager, 
        UserManager userManager, 
        IOpenIddictApplicationManager applicationManager,
        IOpenIddictAuthorizationManager authorizationManager, 
        IOpenIddictScopeManager scopeManager, 
        IOpenIddictTokenManager tokenManager, 
        AbpOpenIddictClaimsPrincipalManager openIddictClaimsPrincipalManager)
    {
        this.SignInManager = signInManager;
        this.UserManager = userManager;
        this.ApplicationManager = applicationManager;
        this.AuthorizationManager = authorizationManager;
        this.ScopeManager = scopeManager;
        this.TokenManager = tokenManager;
        this.OpenIddictClaimsPrincipalManager = openIddictClaimsPrincipalManager;

        this.LocalizationSourceName = RealtoCrmConsts.LocalizationSourceName;
    }

    protected virtual Task<OpenIddictRequest> GetOpenIddictServerRequestAsync(HttpContext httpContext)
    {
        var request = this.HttpContext.GetOpenIddictServerRequest() ??
                      throw new InvalidOperationException(this.L("TheOpenIDConnectRequestCannotBeRetrieved"));

        return Task.FromResult(request);
    }

    protected virtual async Task<IEnumerable<string>> GetResourcesAsync(ImmutableArray<string> scopes)
    {
        var resources = new List<string>();
        if (!scopes.Any())
        {
            return resources;
        }

        await foreach (var resource in this.ScopeManager.ListResourcesAsync(scopes))
        {
            resources.Add(resource);
        }

        return resources;
    }

    protected virtual async Task<bool> HasFormValueAsync(string name)
    {
        if (this.Request.HasFormContentType)
        {
            var form = await this.Request.ReadFormAsync();
            if (!string.IsNullOrEmpty(form[name]))
            {
                return true;
            }
        }

        return false;
    }

    protected virtual async Task<bool> PreSignInCheckAsync(User user)
    {
        if (!await this.SignInManager.CanSignInAsync(user))
        {
            return false;
        }

        if (await this.UserManager.IsLockedOutAsync(user))
        {
            return false;
        }

        return true;
    }
}