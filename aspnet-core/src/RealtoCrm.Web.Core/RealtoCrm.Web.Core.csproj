<Project Sdk="Microsoft.NET.Sdk.Web">
  <Import Project="..\..\common.props" />
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <AssetTargetFallback>$(AssetTargetFallback);portable-net45+win8+wp8+wpa81;</AssetTargetFallback>
    <AssemblyName>RealtoCrm.Web.Core</AssemblyName>
    <PackageId>RealtoCrm.Web.Core</PackageId>
    <GenerateAssemblyConfigurationAttribute>false</GenerateAssemblyConfigurationAttribute>
    <GenerateAssemblyCompanyAttribute>false</GenerateAssemblyCompanyAttribute>
    <GenerateAssemblyProductAttribute>false</GenerateAssemblyProductAttribute>
    <OpenApiGenerateDocuments>false</OpenApiGenerateDocuments>
    <RootNamespace>RealtoCrm.Web</RootNamespace>
    <OutputType>Library</OutputType>
    <GenerateDocumentationFile>False</GenerateDocumentationFile>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  <ItemGroup>
    <ProjectReference Include="..\RealtoCrm.Application\RealtoCrm.Application.csproj" />
    <ProjectReference Include="..\RealtoCrm.EntityFrameworkCore\RealtoCrm.EntityFrameworkCore.csproj" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Abp.AspNetCore.SignalR" Version="9.0.0" />
    <PackageReference Include="Abp.AspNetZeroCore.Web" Version="4.1.0" />
    <PackageReference Include="Hangfire.SqlServer" Version="1.8.6" />
    <PackageReference Include="DotSwashbuckle.AspNetCore" Version="3.0.10" />
    <PackageReference Include="Owl.reCAPTCHA" Version="7.0.0" />
    <PackageReference Include="System.ValueTuple" Version="4.5.0" />
    <PackageReference Include="System.Net.Http" Version="4.3.4" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Abp.AspNetCore" Version="9.0.0" />
    <PackageReference Include="Abp.HangFire.AspNetCore" Version="9.0.0" />
    <PackageReference Include="Abp.RedisCache" Version="9.0.0" />
    <PackageReference Include="Abp.HtmlSanitizer" Version="9.0.0" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="System.Diagnostics.PerformanceCounter" Version="8.0.0" />
    <PackageReference Include="System.Security.Cryptography.Pkcs" Version="8.0.0" />
    <PackageReference Include="System.Data.SqlClient" Version="4.8.5" />
    <PackageReference Include="Microsoft.AspNetCore.DataProtection.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.OpenIdConnect" Version="8.0.0" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="AspNetCore.HealthChecks.UI" Version="7.0.2" />
    <PackageReference Include="AspNetCore.HealthChecks.UI.Client" Version="7.1.0" />
    <PackageReference Include="AspNetCore.HealthChecks.UI.InMemory.Storage" Version="7.0.0" />
    <PackageReference Include="OpenIddict.AspNetCore" Version="4.10.0" />
  </ItemGroup>
</Project>