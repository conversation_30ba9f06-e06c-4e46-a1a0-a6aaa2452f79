using System.Collections.Generic;
using System.Linq;
using Abp.Extensions;
using Microsoft.Extensions.Configuration;
using RealtoCrm.Configuration;

namespace RealtoCrm.Web.Url;

public abstract class WebUrlServiceBase(IAppConfigurationAccessor configurationAccessor)
{
    public const string TenancyNamePlaceHolder = "{TENANCY_NAME}";

    public abstract string WebSiteRootAddressFormatKey { get; }

    public abstract string ServerRootAddressFormatKey { get; }

    public string WebSiteRootAddressFormat => this.appConfiguration[this.WebSiteRootAddressFormatKey] ?? "https://localhost:44302/";

    public string ServerRootAddressFormat => this.appConfiguration[this.ServerRootAddressFormatKey] ?? "https://localhost:44302/";

    public bool SupportsTenancyNameInUrl
    {
        get
        {
            var siteRootFormat = this.WebSiteRootAddressFormat;
            return !siteRootFormat.IsNullOrEmpty() && siteRootFormat.Contains(TenancyNamePlaceHolder);
        }
    }

    readonly IConfigurationRoot appConfiguration = configurationAccessor.Configuration;

    public string GetSiteRootAddress(string tenancyName = null)
    {
        return this.ReplaceTenancyNameInUrl(this.WebSiteRootAddressFormat, tenancyName);
    }

    public string GetServerRootAddress(string tenancyName = null)
    {
        return this.ReplaceTenancyNameInUrl(this.ServerRootAddressFormat, tenancyName);
    }

    public List<string> GetRedirectAllowedExternalWebSites()
    {
        var values = this.appConfiguration["App:RedirectAllowedExternalWebSites"];
        return values?.Split(',').ToList() ?? new List<string>();
    }

    private string ReplaceTenancyNameInUrl(string siteRootFormat, string tenancyName)
    {
        if (!siteRootFormat.Contains(TenancyNamePlaceHolder))
        {
            return siteRootFormat;
        }

        if (siteRootFormat.Contains(TenancyNamePlaceHolder + "."))
        {
            siteRootFormat = siteRootFormat.Replace(TenancyNamePlaceHolder + ".", TenancyNamePlaceHolder);
        }

        if (tenancyName.IsNullOrEmpty())
        {
            return siteRootFormat.Replace(TenancyNamePlaceHolder, "");
        }

        return siteRootFormat.Replace(TenancyNamePlaceHolder, tenancyName + ".");
    }
}