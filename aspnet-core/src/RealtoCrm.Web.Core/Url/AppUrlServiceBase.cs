using Abp.Dependency;
using Abp.Extensions;
using Abp.MultiTenancy;
using RealtoCrm.Url;

namespace RealtoCrm.Web.Url;

public abstract class AppUrlServiceBase(IWebUrlService webUrlService, ITenantCache tenantCache) : IAppUrlService, ITransientDependency
{
    public abstract string EmailActivationRoute { get; }
        
    public abstract string EmailChangeRequestRoute { get; }

    public abstract string PasswordResetRoute { get; }

    protected readonly IWebUrlService WebUrlService = webUrlService;
    protected readonly ITenantCache TenantCache = tenantCache;

    public string CreateEmailActivationUrlFormat(int? tenantId)
    {
        return this.CreateEmailActivationUrlFormat(this.GetTenancyName(tenantId));
    }

    public string CreateEmailChangeRequestUrlFormat(int? tenantId)
    {
        return this.CreateEmailChangeRequestUrlFormat(this.GetTenancyName(tenantId));
    }
        
    public string CreateEmailChangeRequestUrlFormat(string tenancyName)
    {
        var activationLink = this.WebUrlService.GetSiteRootAddress(tenancyName).EnsureEndsWith('/') + this.EmailChangeRequestRoute + "?userId={userId}&emailAddress={emailAddress}&old={oldMailAddress}";

        if (tenancyName != null)
        {
            activationLink = activationLink + "&tenantId={tenantId}";
        }

        return activationLink;
    }

    public string CreatePasswordResetUrlFormat(int? tenantId)
    {
        return this.CreatePasswordResetUrlFormat(this.GetTenancyName(tenantId));
    }

    public string CreateEmailActivationUrlFormat(string tenancyName)
    {
        var activationLink = this.WebUrlService.GetSiteRootAddress(tenancyName).EnsureEndsWith('/') + this.EmailActivationRoute + "?userId={userId}&confirmationCode={confirmationCode}";

        if (tenancyName != null)
        {
            activationLink = activationLink + "&tenantId={tenantId}";
        }

        return activationLink;
    }

    public string CreatePasswordResetUrlFormat(string tenancyName)
    {
        var resetLink = this.WebUrlService.GetSiteRootAddress(tenancyName).EnsureEndsWith('/') + this.PasswordResetRoute +
                        $"?userId={{userId}}&resetCode={{resetCode}}&expireDate={{expireDate}}";

        if (tenancyName != null) 
        {
            resetLink += "&tenantId={tenantId}";
        }

        return resetLink;
    }


    private string GetTenancyName(int? tenantId)
    {
        return tenantId.HasValue ? this.TenantCache.Get(tenantId.Value).TenancyName : null;
    }
}