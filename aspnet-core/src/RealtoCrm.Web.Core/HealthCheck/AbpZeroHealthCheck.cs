using Microsoft.Extensions.DependencyInjection;
using RealtoCrm.HealthChecks;

namespace RealtoCrm.Web.HealthCheck;

public static class AbpZeroHealthCheck
{
    public static IHealthChecksBuilder AddAbpZeroHealthCheck(this IServiceCollection services)
    {
        var builder = services.AddHealthChecks();
        builder.AddCheck<RealtoCrmDbContextHealthCheck>("Database Connection");
        builder.AddCheck<RealtoCrmDbContextUsersHealthCheck>("Database Connection with user check");
        builder.AddCheck<CacheHealthCheck>("Cache");

        // add your custom health checks here
        // builder.AddCheck<MyCustomHealthCheck>("my health check");

        return builder;
    }
}