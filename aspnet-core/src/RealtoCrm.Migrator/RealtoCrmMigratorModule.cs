using Abp.AspNetZeroCore;
using Abp.Events.Bus;
using Abp.Modules;
using Abp.Reflection.Extensions;
using Castle.MicroKernel.Registration;
using Microsoft.Extensions.Configuration;
using RealtoCrm.Configuration;
using RealtoCrm.EntityFrameworkCore;
using RealtoCrm.Migrator.DependencyInjection;

namespace RealtoCrm.Migrator;

using Abp.AutoMapper;
using Mapping;
using Web;

[DependsOn(typeof(RealtoCrmEntityFrameworkCoreModule))]
public class RealtoCrmMigratorModule : AbpModule
{
    private static IConfigurationRoot AppConfiguration => AppConfigurations.Get(
        typeof(RealtoCrmMigratorModule).GetAssembly().GetDirectoryPathOrNull(),
        addUserSecrets: true
    );

    public RealtoCrmMigratorModule(RealtoCrmEntityFrameworkCoreModule abpZeroTemplateEntityFrameworkCoreModule)
        => abpZeroTemplateEntityFrameworkCoreModule.SkipDbSeed = true;

    public override void PreInitialize()
    {
        this.Configuration.DefaultNameOrConnectionString = AppConfiguration.GetConnectionString(
            RealtoCrmConsts.ConnectionStringName
        );
        this.Configuration.Modules.AspNetZero().LicenseCode = AppConfiguration["AbpZeroLicenseCode"];

        this.Configuration.BackgroundJobs.IsJobExecutionEnabled = false;
        this.Configuration.ReplaceService(typeof(IEventBus), () =>
        {
            this.IocManager.IocContainer.Register(
                Component.For<IEventBus>().Instance(NullEventBus.Instance)
            );
        });
        
        this.Configuration.MultiTenancy.IsEnabled = true;
    
        this.Configuration.EntityHistory.IsEnabled = false;
    
        this.AddAutoMapper();
    }

    public override void Initialize()
    {
        this.IocManager.RegisterAssemblyByConvention(typeof(RealtoCrmCoreModule).GetAssembly());
        this.IocManager.RegisterAssemblyByConvention(typeof(RealtoCrmWebCoreModule).GetAssembly());
        this.IocManager.RegisterAssemblyByConvention(typeof(RealtoCrmApplicationModule).GetAssembly());
        this.IocManager.RegisterAssemblyByConvention(typeof(RealtoCrmApplicationSharedModule).GetAssembly());
        this.IocManager.RegisterAssemblyByConvention(typeof(RealtoCrmMigratorModule).GetAssembly());
        // this.IocManager.Register<IUserAppService, UserAppService>();
        this.IocManager.Register();
    }

    private void AddAutoMapper()
    {
        var assemblies = new[]
        {
            typeof(RealtoCrmApplicationModule).Assembly,
            typeof(RealtoCrmApplicationSharedModule).Assembly,
            typeof(RealtoCrmMigratorModule).Assembly,
        };

        this.Configuration
            .Modules
            .AbpAutoMapper()
            .Configurators
            .Add(mapper => mapper.RegisterMappingsFrom(assemblies));
    }
}