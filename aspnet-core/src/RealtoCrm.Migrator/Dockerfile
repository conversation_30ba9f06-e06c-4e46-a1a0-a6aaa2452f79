#See https://aka.ms/containerfastmode to understand how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:7.0 AS base
WORKDIR /app

FROM mcr.microsoft.com/dotnet/sdk:7.0 AS build
WORKDIR /src
COPY ["src/RealtoCrm.Migrator/RealtoCrm.Migrator.csproj", "src/RealtoCrm.Migrator/"]
COPY ["src/RealtoCrm.EntityFrameworkCore/RealtoCrm.EntityFrameworkCore.csproj", "src/RealtoCrm.EntityFrameworkCore/"]
COPY ["src/RealtoCrm.Core/RealtoCrm.Core.csproj", "src/RealtoCrm.Core/"]
COPY ["src/RealtoCrm.Core.Shared/RealtoCrm.Core.Shared.csproj", "src/RealtoCrm.Core.Shared/"]
RUN dotnet restore "src/RealtoCrm.Migrator/RealtoCrm.Migrator.csproj"
COPY . .
WORKDIR "/src/src/RealtoCrm.Migrator"
RUN dotnet build "RealtoCrm.Migrator.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "RealtoCrm.Migrator.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "RealtoCrm.Migrator.dll", "-s"]
