using Abp.Dependency;
using Castle.Windsor.MsDependencyInjection;
using Microsoft.Extensions.DependencyInjection;
using RealtoCrm.Identity;

namespace RealtoCrm.Migrator.DependencyInjection;

public static class ServiceCollectionExtensions
{
    public static void Register(this IIocManager iocManager)
    {
        var services = new ServiceCollection();
        services.AddIdentity();

        WindsorRegistrationHelper.CreateServiceProvider(iocManager.IocContainer, services);
    }
}