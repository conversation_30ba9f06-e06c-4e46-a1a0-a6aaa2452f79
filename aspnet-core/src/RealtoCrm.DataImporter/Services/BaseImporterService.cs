using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Domain.Uow;
using Abp.EntityFrameworkCore;
using Abp.Extensions;
using Abp.MultiTenancy;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json.Linq;
using RealtoCrm.EntityFrameworkCore;
using RealtoCrm.Extensions;

namespace RealtoCrm.DataImporter.Services;

public abstract class BaseImporterService<TEntity, TModel>(
    Log log,
    IMapper mapper,
    IConfiguration configuration,
    IDbContextResolver dbContextResolver,
    IConnectionStringResolver connectionStringResolver,
    IJsonSerializerService<TModel> jsonSerializerService,
    IImportDocumentFetchService importDocumentFetchService,
    IAddressMappingService addressMappingService,
    IMapMoneyService mapMoneyService,
    IMapSourceCategoryService sourceCategoryService)
    where TEntity : class
    where TModel : class
{
    protected readonly IMapper Mapper = mapper;

    protected readonly IImportDocumentFetchService ImportDocumentFetchService = importDocumentFetchService;

    protected readonly IAddressMappingService AddressMappingService = addressMappingService;

    protected readonly IMapMoneyService MapMoneyService = mapMoneyService;

    protected readonly IMapSourceCategoryService MapSourceCategoryService = sourceCategoryService;

    protected virtual int BatchSize => 20;

    protected abstract string ImportDocumentUrl { get; }

    protected abstract string ApiKeyConfigurationKey { get; }

    protected string ApiKey => configuration.GetValue<string>(this.ApiKeyConfigurationKey)!;

    protected RealtoCrmDbContext DbContext { get; }
        = dbContextResolver.Resolve<RealtoCrmDbContext>(
            connectionStringResolver.GetNameOrConnectionString(
                new ConnectionStringResolveArgs(MultiTenancySides.Host)), null);

    protected virtual IEnumerable<TEntity> ApplySpecifics(IEnumerable<TEntity> entities) => entities;

    protected virtual async Task<IEnumerable<TModel>> GetEntitiesForPages(int startPage, int batchSize)
    {
        Console.WriteLine($"Fetching {typeof(TEntity).Name} for pages {startPage} to {startPage + batchSize - 1}");

        var pageQueries = Enumerable.Range(startPage, batchSize)
            .Select(pageNumber => this.ImportDocumentUrl + "?page=" + pageNumber);

        var jsonContents = await pageQueries
            .Select(pageQuery => this.ImportDocumentFetchService.FetchDocumentAsync(
                pageQuery,
                this.ApiKey)
            )
            .WhereAsync(x => !x.IsNullOrEmpty());

        return await jsonContents
            .Select(jsonSerializerService.DeserializeManyAsync)
            .SelectManyAsync(x => x ?? throw new ArgumentNullException(nameof(x)))
            .ToListAsync();
    }

    public virtual async Task<int> Import()
    {
        log.Write($"Started {typeof(TEntity).Name} import...");

        var count = 0;
        var pageNumber = 1;

        Console.WriteLine($"Fetching initial page for entity: {typeof(TEntity).Name}");

        var firstPageQueryString = await this.ImportDocumentFetchService.FetchDocumentAsync(
            this.ImportDocumentUrl,
            this.ApiKey);

        var totalPagesCount = GetTotalPageCount(firstPageQueryString);

        this.DbContext.ChangeTracker.AutoDetectChangesEnabled = false;

        while (pageNumber <= totalPagesCount)
        {
            var remainingPages = totalPagesCount - pageNumber + 1;
            var currentBatchSize = remainingPages < this.BatchSize ? remainingPages : this.BatchSize;
            var startTime = DateTime.Now;

            var batchData = await this.GetEntitiesForPages(pageNumber, currentBatchSize).ToListAsync();

            if (batchData.Count <= 0)
            {
                break;
            }
            
            batchData = await this.UpdateExistingMappingsAndFilterDuplicates(batchData);

            batchData = this.FilterAsiData(batchData);

            var batch = await this.MapBatch(batchData);

            this.DecorateBefore(batch, batchData);
            await this.DecorateBeforeAsync(batch, batchData);

            var batchToSave = this.ApplySpecifics(batch).ToList();

            await this.DbContext.AddRangeAsync(batchToSave);

            await this.DbContext.SaveChangesAsync();

            await this.DecorateAfter(batch, batchData);

            var duration = (DateTime.Now - startTime).TotalSeconds;

            Console.WriteLine($"{batchToSave.Count} {typeof(TEntity).Name}s inserted in {duration} seconds");

            count += batch.Count;
            pageNumber += currentBatchSize;
        }

        await this.DbContext.SaveChangesAsync();

        log.Write("Fetched count: " + count);

        log.Write($"Finished {typeof(TEntity).Name} import.");

        return count;
    }

    private async Task<List<TEntity>> MapBatch(List<TModel> batchData)
    {
        var mappingDependencies = await this.GetMappingDependencies();

        return this.Mapper
            .Map<List<TEntity>>(batchData, opt =>
                mappingDependencies.ForEach(x => opt.Items[x.Key] = x.Value))
            .ToList();
    }

    protected virtual async Task<IEnumerable<KeyValuePair<string, object>>> GetMappingDependencies()
        => [];

    protected virtual int GetTotalPageCount(string jsonString)
    {
        var jsonObject = JObject.Parse(jsonString);
        var lastPage = jsonObject["data"]?["last_page"];
        Console.WriteLine($"Last page number returned from query: {lastPage}");
        return lastPage?.Value<int>() ?? 0;
    }

    /// <summary>
    /// Used to populate foreign key relationships when they need to be populated from mapping table because of difference in asi/db ids
    /// </summary>
    /// <param name="entityList">The list of entities that have been processed and mapped to corresponding db model.</param>
    /// <param name="batchAsiData">The batch coming from the import source/ASI api.</param>
    protected virtual void DecorateBefore(List<TEntity> entityList, IEnumerable<TModel> batchAsiData)
    {
    }
    
    /// <summary>
    /// Used to populate foreign key relationships when they need to be populated from mapping table because of difference in asi/db ids
    /// </summary>
    /// <param name="entityList">The list of entities that have been processed and mapped to corresponding db model.</param>
    /// <param name="batchAsiData">The batch coming from the import source/ASI api.</param>
    protected virtual Task DecorateBeforeAsync(List<TEntity> entityList, IEnumerable<TModel> batchAsiData)
     => Task.CompletedTask;

    /// <summary>
    /// Used to populate mapping tables with ids - current db id and asiId
    /// </summary>
    /// <param name="entityList">The list of entities that have been processed and mapped to corresponding db model.</param>
    /// <param name="batchAsiData">The batch coming from the import source/ASI api.</param>
    protected virtual async Task DecorateAfter(IEnumerable<TEntity> entityList, List<TModel> batchAsiData)
    {
        await Task.CompletedTask;
    }

    /// <summary>
    /// Used to filter the data fetched from the api before processing it.
    /// </summary>
    /// <param name="batchAsiData">The batch coming from the import source/ASI api.</param>
    protected virtual List<TModel> FilterAsiData(List<TModel> batchAsiData)
    {
        return batchAsiData;
    }

    /// <summary>
    /// Used to filter the data fetched from the api before processing it.
    /// </summary>
    /// <param name="batchUesData">The batch coming from the import source/UES api.</param>
    protected virtual List<TModel> FilterUesData(List<TModel> batchUesData)
    {
        return batchUesData;
    }

    /// <summary>
    /// Used to get and map required data from the data
    /// </summary>
    protected virtual List<TMappedModel> GetDataFromDbAsNoTracking<TEntityModel, TMappedModel>()
        where TEntityModel : class
        where TMappedModel : class
        => this.Mapper
            .ProjectTo<TMappedModel>(this.DbContext.Set<TEntityModel>().AsNoTracking())
            .ToList();
    
    /// <summary>
    /// Goes through the MappingModels with external IDs, sets the Ids from the current batch
    /// and filters out entities that already exist.
    /// </summary>
    protected virtual async Task<List<TModel>> UpdateExistingMappingsAndFilterDuplicates(List<TModel> batchData)
    {
        return batchData;
    }
}