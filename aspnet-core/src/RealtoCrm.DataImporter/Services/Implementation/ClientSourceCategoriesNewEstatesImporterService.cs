namespace RealtoCrm.DataImporter.Services.Implementation;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Domain.Uow;
using Abp.EntityFrameworkCore;
using AutoMapper;
using Clients;
using Companies;
using Employees;
using Extensions;
using Mappings;
using Mappings.DataResolvers.Clients;
using Mappings.DataResolvers.Companies;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Models;
using Nomenclatures;
using SourceCategories;
using Tags;
using static CosherConsts;

public class ClientSourceCategoriesNewEstatesImporterService(
    Log log,
    IMapper mapper,
    IConfiguration configuration,
    IDbContextResolver dbContextResolver,
    IConnectionStringResolver connectionStringResolver,
    IJsonSerializerService<ClientsUesImportModel> jsonSerializerService,
    IImportDocumentFetchService importDocumentFetchService,
    IAddressMappingService addressMappingService,
    IMapMoneyService mapMoneyService,
    IMapSourceCategoryService mapSourceCategoryService)
    : BaseUesImporterService<Client, ClientsUesImportModel>(
        log,
        mapper,
        configuration,
        dbContextResolver,
        connectionStringResolver,
        jsonSerializerService,
        importDocumentFetchService,
        addressMappingService,
        mapMoneyService,
        mapSourceCategoryService)
{
     protected override string ImportDocumentUrl => this.DefaultUrl + "/new-estates-clients";
    private const int DefaultCategoryId = 2;
    private const int CategoryIdForSourceDetailExceptions = 12;

    private static readonly string[] SourceDetailsException =
        [UesSourceCategories.Letters, UesSourceCategories.Brochures, UesSourceCategories.DoorHangers];

    public override async Task<int> Import()
    {
        log.Write($"Started {nameof(ClientSourceCategory)} and {nameof(SourceDetailClient)} import...");

        var employeeMappings = await this.DbContext
            .Set<EmployeeMapping>()
            .ToListAsync();

        var clientMappings = await this.DbContext
            .Set<ClientMapping>()
            .ToListAsync();

        var socialMedias = await this.DbContext.Set<SocialMedia>().ToListAsync();

        var websites = await this.DbContext.Set<Website>().ToListAsync();

        var count = 0;
        var pageNumber = 1;

        log.Write($"Fetching initial page");

        var firstPageQueryString = await this.ImportDocumentFetchService.FetchDocumentAsync(
            this.ImportDocumentUrl,
            this.ApiKey);

        var totalPagesCount = GetTotalPageCount(firstPageQueryString);

        this.DbContext.ChangeTracker.AutoDetectChangesEnabled = false;

        while (pageNumber <= totalPagesCount)
        {
            var remainingPages = totalPagesCount - pageNumber + 1;
            var currentBatchSize = remainingPages < BatchSize ? remainingPages : BatchSize;

            var batchData =
                await EnumerableExtensions.ToListAsync(this.GetEntitiesForPages(pageNumber, currentBatchSize));

            if (batchData.Count <= 0)
            {
                break;
            }

            var mappingDependencies = await this.GetMappingDependencies();

            var batch = this.Mapper
                .Map<List<Client>>(batchData, opt =>
                    mappingDependencies.ForEach(x => opt.Items[x.Key] = x.Value))
                .ToList();

            CreateSourceCategoriesAndDetails(batch, batchData, socialMedias, websites, clientMappings,
                employeeMappings);

            var sourceCategories = batch.SelectMany(x => x.ClientsSourceCategories).ToList();
            var sourceDetails = batch.SelectMany(x => x.SourceDetails).ToList();

            await this.DbContext.ClientsSourceCategories.AddRangeAsync(sourceCategories);
            await this.DbContext.SourceDetailsClients.AddRangeAsync(sourceDetails);
            await this.DbContext.SaveChangesAsync();

            count += batch.Count;
            pageNumber += currentBatchSize;

            log.Write(
                $"Fetched count: source categories: {sourceCategories.Count}, source details: {sourceDetails.Count}");
        }

        log.Write($"Finished {nameof(ClientSourceCategory)} and {nameof(SourceDetailClient)} import.");
        return count;
    }

    private static void CreateSourceCategoriesAndDetails(List<Client> clientList, List<ClientsUesImportModel> batchData,
        List<SocialMedia> socialMedias, List<Website> uesSites,
        List<ClientMapping> clientMappings, List<EmployeeMapping> employeeMappings)
    {
        foreach (var clientFromBatch in batchData)
        {
            var currentClient = clientList.FirstOrDefault(c => c.ClientMapping.AdminId == clientFromBatch.ClientUesId);

            if (currentClient == null ||
                (clientFromBatch.UesSourceCategories == null || !clientFromBatch.UesSourceCategories.Any()))
            {
                continue;
            }

            clientFromBatch.UesSourceCategories!.ForEach(sourceCategory =>
            {
                if (SourceCategoryAndDetailsUesImporterMappings.UesCategoryToAsiCategoryWithDetailTypeInfo.TryGetValue(
                        sourceCategory.Name, out var categoryInfo))
                {
                    var correspondingEmployee =
                        employeeMappings.FirstOrDefault(x => x.AdminId == sourceCategory.AdminUserId);

                    var correspondingClientFromDb =
                        clientMappings.FirstOrDefault(x => x.AdminId == clientFromBatch.ClientUesId);

                    if (correspondingEmployee is not null && correspondingClientFromDb is not null)
                    {
                        var clientSourceCategory = new ClientSourceCategory
                        {
                            SourceCategoryId = categoryInfo.CategoryId,
                            EmployeeId = correspondingEmployee.EmployeeId,
                            ClientId = correspondingClientFromDb.ClientId,
                        };

                        currentClient.ClientsSourceCategories.Add(clientSourceCategory);

                        SetCategoryDetail(sourceCategory, clientSourceCategory, currentClient, categoryInfo.DetailInfo,
                            socialMedias,
                            uesSites, clientMappings);
                    }
                }
            });
        }
    }


    private static void SetCategoryDetail(UesSourceCategoryImportModel uesCategory,
        ClientSourceCategory currentNewSourceCategory,
        Client client, string? detailNote, List<SocialMedia> socialMedias, List<Website> uesSites,
        List<ClientMapping> clientMappings)
    {
        if (string.IsNullOrEmpty(detailNote)) return;

        switch (detailNote)
        {
            case Websites.UesSite:
                client.SourceDetails.Add(
                    new SourceDetailClient
                    {
                        ClientId = currentNewSourceCategory.ClientId,
                        CategoryId = currentNewSourceCategory.SourceCategoryId,
                        Website = uesSites
                            .FirstOrDefault(m => m.Name.ToLower() == Websites.UesSite.ToLower())
                    });
                break;
            case SocialMedias.LinkedIn:
                client.SourceDetails.Add(
                    new SourceDetailClient
                    {
                        ClientId = currentNewSourceCategory.ClientId,
                        CategoryId = currentNewSourceCategory.SourceCategoryId,
                        SocialMedia = socialMedias
                            .FirstOrDefault(m => m.Name.ToLower() == SocialMedias.LinkedIn.ToLower())
                    });
                break;
            case SocialMedias.Viber:
                client.SourceDetails.Add(new SourceDetailClient
                {
                    ClientId = currentNewSourceCategory.ClientId,
                    CategoryId = currentNewSourceCategory.SourceCategoryId,
                    SocialMedia = socialMedias.FirstOrDefault(m => m.Name.ToLower() == SocialMedias.Viber.ToLower())
                });
                break;
            case Websites.Google:
                client.SourceDetails.Add(
                    new SourceDetailClient
                    {
                        ClientId = currentNewSourceCategory.ClientId,
                        CategoryId = currentNewSourceCategory.SourceCategoryId,
                        Website = uesSites
                            .FirstOrDefault(m => m.Name.ToLower() == Websites.Google.ToLower())
                    });
                break;
            case UesSourceCategories.SourceDetail:
                if (uesCategory.RecommendedBy is null or 0)
                {
                    currentNewSourceCategory.SourceCategoryId = DefaultCategoryId;
                    break;
                }

                var recommendingClient = clientMappings
                    .FirstOrDefault(m => m.AdminId == uesCategory.RecommendedBy);

                if (recommendingClient != null)
                {
                    client.SourceDetails.Add(new SourceDetailClient
                    {
                        ClientId = currentNewSourceCategory.ClientId,
                        CategoryId = currentNewSourceCategory.SourceCategoryId,
                        DetailClientId = recommendingClient.ClientId
                    });
                }

                break;
            case SocialMedias.Facebook:
                client.SourceDetails.Add(
                    new SourceDetailClient
                    {
                        ClientId = currentNewSourceCategory.ClientId,
                        CategoryId = currentNewSourceCategory.SourceCategoryId,
                        SocialMedia =
                            socialMedias.FirstOrDefault(m => m.Name.ToLower() == SocialMedias.Facebook.ToLower())
                    });
                break;
            case SocialMedias.Instagram:
                client.SourceDetails.Add(
                    new SourceDetailClient
                    {
                        ClientId = currentNewSourceCategory.ClientId,
                        CategoryId = currentNewSourceCategory.SourceCategoryId,
                        SocialMedia =
                            socialMedias.FirstOrDefault(m => m.Name.ToLower() == SocialMedias.Instagram.ToLower())
                    });
                break;
            case UesSourceCategories.ExplicitSourceDetail:
            {
                HandleExplicitSourceDetail(uesCategory, currentNewSourceCategory, client, uesSites);
                break;
            }
        }
    }

    private static void HandleExplicitSourceDetail(
        UesSourceCategoryImportModel uesCategory,
        ClientSourceCategory currentNewSourceCategory,
        Client client,
        List<Website> uesSites)
    {
        if (string.IsNullOrEmpty(uesCategory.SourceDetailName))
        {
            return;
        }

        if (SourceDetailsException.Contains(uesCategory.SourceDetailName))
        {
            currentNewSourceCategory.SourceCategoryId = CategoryIdForSourceDetailExceptions;
            return;
        }

        var matchedWebsite = uesSites
            .FirstOrDefault(m => m.Name.Equals(uesCategory.SourceDetailName, StringComparison.OrdinalIgnoreCase));

        if (matchedWebsite != null)
        {
            client.SourceDetails.Add(new SourceDetailClient
            {
                ClientId = currentNewSourceCategory.ClientId,
                CategoryId = currentNewSourceCategory.SourceCategoryId,
                Website = matchedWebsite
            });
        }
    }

    protected override async Task<IEnumerable<KeyValuePair<string, object>>> GetMappingDependencies()
    {
        var tagCategories = await this.DbContext.Set<TagCategory>().ToListAsync();

        var tags = await this.DbContext.Set<Tag>().ToListAsync();

        var company = await this.DbContext
            .Set<Company>()
            .Include(c => c.Tenant)
            .Where(c => c.Name == NewEstatesCompanyName)
            .SingleAsync();

        var jobPositions = await this.DbContext.Set<JobPosition>().ToListAsync();

        var workplaces = await this.DbContext.Set<Workplace>().ToListAsync();

        var clientPreferences = await this.DbContext.Set<ClientPreference>().ToListAsync();

        var nationalities = await this.DbContext.Set<Nationality>().ToListAsync();

        var titles = await this.DbContext.Set<Title>().ToListAsync();

        var maritalStatuses = await this.DbContext.Set<MaritalStatus>().ToListAsync();

        var sectorMappings = this.DbContext.SectorMappings.AsNoTracking().ToList();

        var municipalityMappings = this.DbContext.MunicipalityMappings
            .Include(m => m.Municipality).AsNoTracking().ToList();

        return new Dictionary<string, object>
        {
            { ClientTagsValueResolver.TagCategoriesItemKey, tagCategories },
            { ClientTagsValueResolver.ExistingTagsItemKey, tags },
            { ClientJobPositionsValueResolver.JobPositionsItemsKey, jobPositions },
            { TenantValueResolver<ClientsUesImportModel, Client>.CompanyNameItemsKey, company },
            { ClientWorkplaceValueResolver.WorkplacesItemsKey, workplaces },
            { ClientPreferencesValueResolver.ClientPreferencesItemsKey, clientPreferences },
            { NationalityValueResolver.NationalitiesItemsKey, nationalities },
            { TitleValueResolver.TitlesItemsKey, titles },
            { MaritalStatusesValueResolver.MaritalStatusesItemsKey, maritalStatuses },
            { ClientSectorsValueResolver.SectorMappingsItemsKey, sectorMappings },
            { DocumentAuthorityValueResolver.MunicipalityMappingsItemsKey, municipalityMappings },
        };
    }
}