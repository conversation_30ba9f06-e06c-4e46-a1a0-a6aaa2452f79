namespace RealtoCrm.DataImporter.Services.Implementation;

using System;
using System.Linq;
using System.Threading.Tasks;
using Abp.Domain.Uow;
using Abp.EntityFrameworkCore;
using AutoMapper;
using Companies;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Models;
using Models.ResponseModels;
using static CosherConsts.Tenants;

public class DivisionsFortonImporterService(
    Log log,
    IMapper mapper,
    IConfiguration configuration,
    IDbContextResolver dbContextResolver,
    IConnectionStringResolver connectionStringResolver,
    IJsonSerializerService<DivisionFortonImportModel> jsonSerializerService,
    IImportDocumentFetchService importDocumentFetchService,
    IAddressMappingService addressMappingService,
    IMapMoneyService mapMoneyService,
    IMapSourceCategoryService mapSourceCategoryService)
    : BaseUesImporterService<Division, DivisionFortonImportModel>(
        log,
        mapper,
        configuration,
        dbContextResolver,
        connectionStringResolver,
        jsonSerializerService,
        importDocumentFetchService,
        addressMappingService,
        mapMoneyService,
        mapSourceCategoryService)
{
    protected override string ImportDocumentUrl => "Not used for now";

    public override async Task<int> Import()
    {
        log.Write($"Started {nameof(Division)}s import...");

        var fortonCompany = await this.Mapper
            .ProjectTo<CompaniesImporterResponseModel>(this.DbContext
                .Companies
                .AsNoTracking()
                .Where(c => c.Name == CWFortonTenantName))
            .FirstOrDefaultAsync();

        if (fortonCompany is null)
        {
            throw new InvalidOperationException($"Tenant with name '{CWFortonTenantName}' not found");
        }

        var sofiaDivision = await this.DbContext
            .Divisions
            .Where(d =>
                d.Name == "София" &&
                d.TenantId == fortonCompany.TenantId &&
                d.CompanyId == fortonCompany.Id)
            .FirstOrDefaultAsync();

        if (sofiaDivision is null)
        {
            await this.DbContext.Divisions.AddAsync(new Division
            {
                Name = "София",
                IsActive = true,
                TenantId = fortonCompany.TenantId,
                CompanyId = fortonCompany.Id
            });
        }

        var count = await this.DbContext.SaveChangesAsync();

        log.Write($"Finished {nameof(Division)}s import.");

        return count;
    }
}