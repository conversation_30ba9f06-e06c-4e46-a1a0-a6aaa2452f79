namespace RealtoCrm.DataImporter.Services.Implementation;

using System;
using System.Linq;
using System.Threading.Tasks;
using Abp.Domain.Uow;
using Abp.EntityFrameworkCore;
using AutoMapper;
using Companies;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Models.ResponseModels;
using static CosherConsts.Tenants;

public class DivisionsRealtoGroupImporterService(
    Log log,
    IMapper mapper,
    IConfiguration configuration,
    IDbContextResolver dbContextResolver,
    IConnectionStringResolver connectionStringResolver,
    IJsonSerializerService<DivisionsRealtoGroupImporterService> jsonSerializerService,
    IImportDocumentFetchService importDocumentFetchService,
    IAddressMappingService addressMappingService,
    IMapMoneyService mapMoneyService,
    IMapSourceCategoryService mapSourceCategoryService)
    : BaseUesImporterService<Division, DivisionsRealtoGroupImporterService>(
        log,
        mapper,
        configuration,
        dbContextResolver,
        connectionStringResolver,
        jsonSerializerService,
        importDocumentFetchService,
        addressMappingService,
        mapMoneyService,
        mapSourceCategoryService)
{
    protected override string ImportDocumentUrl => "Not used for now";

    public override async Task<int> Import()
    {
        log.Write($"Started {nameof(Division)}s Realto Group import...");

        var realtoCompany = await this.Mapper
            .ProjectTo<CompaniesImporterResponseModel>(this.DbContext
                .Companies
                .AsNoTracking()
                .Where(c => c.Name == RealtoTenantName))
            .FirstOrDefaultAsync();

        if (realtoCompany is null)
        {
            throw new InvalidOperationException($"Tenant with name '{CWFortonTenantName}' not found");
        }

        var bulgariaDivision = await this.DbContext
            .Divisions
            .Where(d =>
                d.Name == "България (в компания Realto)" &&
                d.TenantId == realtoCompany.TenantId &&
                d.CompanyId == realtoCompany.Id)
            .FirstOrDefaultAsync();

        if (bulgariaDivision is null)
        {
            await this.DbContext.Divisions.AddAsync(new Division
            {
                Name = "България",
                IsActive = true,
                TenantId = realtoCompany.TenantId,
                CompanyId = realtoCompany.Id
            });
        }

        var count = await this.DbContext.SaveChangesAsync();

        log.Write($"Finished Realto Group {nameof(Division)}s import.");

        return count;
    }
}