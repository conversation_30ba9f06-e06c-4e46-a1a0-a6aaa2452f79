using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Domain.Uow;
using Abp.EntityFrameworkCore;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using RealtoCrm.DataImporter.Services.Models;
using RealtoCrm.DataImporter.Services.Models.ResponseModels;
using RealtoCrm.Nomenclatures;
using RealtoCrm.Nomenclatures.Streets.Models;

namespace RealtoCrm.DataImporter.Services.Implementation;

public class StreetsUesImporterService(
    Log log,
    IMapper mapper,
    IConfiguration configuration,
    IDbContextResolver dbContextResolver,
    IConnectionStringResolver connectionStringResolver,
    IJsonSerializerService<StreetsUesImportModel> jsonSerializerService,
    IImportDocumentFetchService importDocumentFetchService,
    IAddressMappingService addressMappingService,
    IMapMoneyService mapMoneyService,
    IMapSourceCategoryService mapSourceCategoryService)
    : BaseUesImporterService<Street, StreetsUesImportModel>(
        log,
        mapper,
        configuration,
        dbContextResolver,
        connectionStringResolver,
        jsonSerializerService,
        importDocumentFetchService,
        addressMappingService,
        mapMoneyService,
        mapSourceCategoryService)
{
    protected override string ImportDocumentUrl => this.DefaultUrl + "/streets";

    protected override void DecorateBefore(List<Street> entityList,
        IEnumerable<StreetsUesImportModel> batchAsiData)
    {
        var populatedPlaceMappings = this.Mapper
            .ProjectTo<PopulatedPlaceMapResponseModel>(this
                .DbContext
                .PopulatedPlaceMappings)
            .ToList();
        
        var streetMappings = this.DbContext
            .StreetMappings
            .ToList();
        
        var currentStreets = this.Mapper
            .ProjectTo<StreetResponseModel>(this
                .DbContext
                .Streets)
            .ToList();

        var streetsToRemove = new List<Street>();
        
        entityList.ForEach(street =>
        {
            var mapping = populatedPlaceMappings.FirstOrDefault(pm => pm.AdminId == street.PopulatedPlaceId);
            
            if (mapping != null)
            {
                street.PopulatedPlaceId = mapping.PopulatedPlaceId;
                
                var currentStreet = currentStreets.FirstOrDefault(cd =>
                    cd.PopulatedPlaceId == street.PopulatedPlaceId &&
                    cd.Name == street.Name);

                if(currentStreet == null)
                {
                    return;
                }
                
                var currentStreetMapping = streetMappings
                    .FirstOrDefault(dm => dm.StreetId == currentStreet.Id);
                
                if (currentStreetMapping != null)
                {
                    var importModel = batchAsiData.FirstOrDefault(s => s.Name == street.Name && mapping.AdminId == s.LocationId);
                    
                    currentStreetMapping.AdminId = importModel?.Id;
                    
                    this.DbContext.StreetMappings.Attach(currentStreetMapping);
                    this.DbContext.Entry(currentStreetMapping).State = EntityState.Modified;
                    
                    streetsToRemove.Add(street);
                }
            }
            else
            {
                Console.WriteLine(
                    $"No mapping found for entity {street.Name} with populated place id from admin: {street.PopulatedPlaceId}");
                
                streetsToRemove.Add(street);
            }
        });
        
        if (streetsToRemove.Count > 0)
        {
            foreach (var street in streetsToRemove)
            {
                entityList.Remove(street);
            }
        }
        
        this.DbContext.SaveChanges();
    }

    protected override async Task DecorateAfter(IEnumerable<Street> entityList,
        List<StreetsUesImportModel> batchAsiData)
    {
        var namesFromAsi = batchAsiData
            .Select(p => p.Name)
            .ToHashSet();

        var currentStreets = this.Mapper
            .ProjectTo<DistrictIAndStreetImporterResponseModel>(this
                .DbContext
                .Streets
                .Where(s => namesFromAsi.Contains(s.Name)))
            .ToList();

        var populatedPlacesIds = currentStreets.Select(p => p.PopulatedPlaceId);

        var populatedPlaceMappings = this.Mapper
            .ProjectTo<PopulatedPlaceMapResponseModel>(this
                .DbContext
                .PopulatedPlaceMappings
                .Where(p => populatedPlacesIds.Contains(p.PopulatedPlaceId)))
            .ToList();

        var streetMappings = entityList
            .Select(street =>
            {
                var populatedPlaceMapping =
                    populatedPlaceMappings.FirstOrDefault(p => p.PopulatedPlaceId == street.PopulatedPlaceId);
                var correspondingStreet = batchAsiData.FirstOrDefault(s =>
                    s.Name == street.Name &&
                    (populatedPlaceMapping != null && populatedPlaceMapping.AdminId == s.LocationId));

                if (correspondingStreet == null)
                {
                    return null;
                }

                var currentStreet =
                    currentStreets.FirstOrDefault(s =>
                        s.Name == street.Name &&
                        (populatedPlaceMapping != null &&
                         populatedPlaceMapping.PopulatedPlaceId == s.PopulatedPlaceId));

                if (currentStreet == null)
                {
                    return null;
                }

                return new StreetMapping
                {
                    StreetId = currentStreet.Id,
                    AdminId = correspondingStreet.Id
                };
            })
            .Where(mapping => mapping != null)
            .ToList();

        if (streetMappings.Any())
        {
            await this.DbContext.StreetMappings.AddRangeAsync(streetMappings!);
            await this.DbContext.SaveChangesAsync();
        }
    }
}