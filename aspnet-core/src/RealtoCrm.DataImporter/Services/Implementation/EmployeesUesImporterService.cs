using RealtoCrm.DataImporter.Services.Models.Helpers;

namespace RealtoCrm.DataImporter.Services.Implementation;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Domain.Uow;
using Abp.EntityFrameworkCore;
using Abp.Extensions;
using Authorization.Roles;
using AutoMapper;
using Companies;
using Employees;
using Employees.Models;
using Expressions;
using Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Models;
using Models.Mappings;
using RealtoCrm.MultiTenancy.Models;
using ModelConstants = RealtoCrm.ModelConstants;
using static CosherConsts.Companies;

public class EmployeesUesImporterService(
    Log log,
    IMapper mapper,
    IConfiguration configuration,
    IDbContextResolver dbContextResolver,
    IEmployeesAppService employeesAppService,
    IConnectionStringResolver connectionStringResolver,
    IJsonSerializerService<EmployeeUesImportModel> jsonSerializerService,
    IImportDocumentFetchService importDocumentFetchService,
    IAddressMappingService addressMappingService,
    IMapMoneyService mapMoneyService,
    IMapSourceCategoryService mapSourceCategoryService)
    : BaseUesImporterService<Employee, EmployeeUesImportModel>(
        log,
        mapper,
        configuration,
        dbContextResolver,
        connectionStringResolver,
        jsonSerializerService,
        importDocumentFetchService,
        addressMappingService,
        mapMoneyService,
        mapSourceCategoryService)
{
    private const int UesConsultantRoleId = 2;
    private const int UesManagerRoleId = 13;

    protected override string ImportDocumentUrl => this.DefaultUrl + "/users";

    protected override List<EmployeeUesImportModel> FilterUesData(List<EmployeeUesImportModel> batchUesData)
        => batchUesData
            .Where(e =>
                !e.Username.IsNullOrWhiteSpace() &&
                !e.Email.IsNullOrWhiteSpace())
            .DistinctBy(e => e.Email)
            .ToList();

    public override async Task<int> Import()
    {
        var uesTenantId = await this.DbContext
            .Companies
            .AsNoTracking()
            .Where(c => c.Name == UniqueCompanyName)
            .Select(c => c.TenantId)
            .SingleAsync();
        
        var divisionId = await this.DbContext
            .Divisions
            .AsNoTracking()
            .Where(x => x.TenantId == uesTenantId)
            .Select(d => d.Id)
            .SingleAsync();
        
        var companyId = await this.DbContext
            .Companies
            .AsNoTracking()
            .Where(c => c.Name == UniqueCompanyName)
            .Select(c => c.Id)
            .SingleAsync();

        var consultantRoleId = await this.DbContext
            .Roles
            .AsNoTracking()
            .Where(r => r.Name == StaticRoleNames.Tenants.Consultant)
            .Where(r => r.TenantId == uesTenantId)
            .Select(r => r.Id)
            .FirstAsync();

        var managerRoleId = await this.DbContext
            .Roles
            .AsNoTracking()
            .Where(r => r.Name == StaticRoleNames.Tenants.Manager)
            .Where(r => r.TenantId == uesTenantId)
            .Select(r => r.Id)
            .FirstAsync();

        var unallocatedRoleId = await this.DbContext
            .Roles
            .AsNoTracking()
            .Where(r => r.Name == StaticRoleNames.Tenants.Unallocated)
            .Where(r => r.TenantId == uesTenantId)
            .Select(r => r.Id)
            .FirstAsync();

        var existingUsers = await this.DbContext.Users
            .AsNoTracking()
            .Select(u => u.EmailAddress.ToLower())
            .ToListAsync();

        var rolesDictionary = new Dictionary<int, int>
        {
            { UesConsultantRoleId, consultantRoleId },
            { UesManagerRoleId, managerRoleId }
        };

        var officeId = this.DbContext
            .Set<Office>()
            .FirstOrDefault(c => c.Name.ToLower().Contains(CosherConsts.Offices.OborishteOfficeName.ToLower())
                                 && c.Company.Name == UniqueEstatesCompanyName)?.Id;

        var uesTeamMappings = await this.Mapper
            .ProjectTo<TeamTeamLeaderUsersUesMappingModel>(this.DbContext.TeamTeamLeaderUsersUesMapping)
            .ToListAsync();

        var uesDepartmentMappings = await this.Mapper
            .ProjectTo<DepartmentManagerUsersUesMappingModel>(this.DbContext.DepartmentManagerUsersUesMapping)
            .ToListAsync();

        log.Write($"Started {nameof(Employee)}s import...");

        var count = 0;
        var pageNumber = 1;

        log.Write($"Fetching initial page for entity: {nameof(Employee)}");

        var firstPageQueryString = await this.ImportDocumentFetchService.FetchDocumentAsync(
            this.ImportDocumentUrl,
            this.ApiKey);

        var totalPagesCount = GetTotalPageCount(firstPageQueryString);

        this.DbContext.ChangeTracker.AutoDetectChangesEnabled = false;

        while (pageNumber <= totalPagesCount)
        {
            var remainingPages = totalPagesCount - pageNumber + 1;
            var currentBatchSize = remainingPages < BatchSize ? remainingPages : BatchSize;
            var startTime = DateTime.Now;

            var batchData = await this.GetEntitiesForPages(pageNumber, currentBatchSize).ToListAsync();

            if (batchData.Count <= 0)
            {
                break;
            }

            batchData = this.FilterUesData(batchData);

            var batch = this.Mapper
                .Map<List<UserWithEmployeeCreateRequestModel>>(batchData)
                .Select((userEmployee, index) =>
                {
                    var username = userEmployee.User.UserName;

                    if (existingUsers.Contains(username.ToLower()) || !username.IsValidUsername())
                    {
                        userEmployee.User.UserName = $"sys_username_{index}";
                    }

                    userEmployee.TenantId = uesTenantId;
                    userEmployee.Employee.CompanyId = companyId;

                    userEmployee.User.RoleIds = userEmployee.User.RoleIds
                        .Select(roleId => roleId is not UesConsultantRoleId and not UesManagerRoleId
                            ? unallocatedRoleId
                            : rolesDictionary[roleId])
                        .ToList();

                    if (officeId != null)
                    {
                        userEmployee.Employee.OfficeId = officeId;
                    }

                    userEmployee.Employee.EmployeeMapping = new EmployeeMappingsRequestModel
                    {
                        AdminId = batchData[index].AdminId
                    };

                    return userEmployee;
                })
                .ToList();

            foreach (var userWithEmployee in batch)
            {
                var emailLower = userWithEmployee.User.EmailAddress.ToLower();

                if (existingUsers.Contains(emailLower) || !emailLower.Contains("ues.bg"))
                {
                    Console.WriteLine(
                        $"Skipping employee with duplicate/not @ues.bg email: {userWithEmployee.User.EmailAddress}");
                    continue;
                }

                if (!string.IsNullOrEmpty(userWithEmployee.Employee.PhoneNumber) &&
                    userWithEmployee.Employee.PhoneNumber.Length < ModelConstants.Common.MinPhoneNumberLength)
                {
                    Console.WriteLine(
                        $"Employee with email {userWithEmployee.User.EmailAddress} has invalid phone number length with phone number: {userWithEmployee.Employee.PhoneNumber}");
                    continue;
                }

                if (!string.IsNullOrEmpty(userWithEmployee.Employee.PhoneNumber) &&
                    userWithEmployee.Employee.PhoneNumber.Length > ModelConstants.Common.MaxPhoneNumberLength)
                {
                    Console.WriteLine(
                        $"Employee with email {userWithEmployee.User.EmailAddress} has invalid phone number length with phone number: {userWithEmployee.Employee.PhoneNumber}");
                    continue;
                }
                
                if (!string.IsNullOrEmpty(userWithEmployee.Employee.PhoneNumber))
                {
                    userWithEmployee.Employee.PhoneNumber = PhoneHelper.NormalizePhoneNumber(userWithEmployee.Employee.PhoneNumber);
                }

                await employeesAppService.CreateAsync(userWithEmployee);
            }
            
            this.DbContext.ChangeTracker.AutoDetectChangesEnabled = true;

            var insertedEmployees = this.DbContext.Employees.Where(employee =>
                    employee.EmployeeMapping != null && employee.EmployeeMapping.AdminId != null)
                .Include(e => e.EmployeeMapping).ToList();

            SetDepartmentTeamAndManger(insertedEmployees, uesDepartmentMappings, uesTeamMappings, divisionId);
            
            await this.DbContext.SaveChangesAsync();

            var duration = (DateTime.Now - startTime).TotalSeconds;

            log.Write($"{batch.Count} {nameof(Employee)}s inserted in {duration} seconds");

            count += batch.Count;
            pageNumber += currentBatchSize;
        }

        log.Write("Fetched count: " + count);

        log.Write($"Finished {nameof(Employee)}s import.");

        return count;
    }

    private void SetDepartmentTeamAndManger(List<Employee> employees,
        List<DepartmentManagerUsersUesMappingModel> departmentMappings,
        List<TeamTeamLeaderUsersUesMappingModel> teamMappings,
        int divisionId)
    {
        foreach (var employee in employees)
        {
            employee.DivisionId = divisionId;
            var adminId = (int)employee.EmployeeMapping!.AdminId!;

            var employeeTeam = teamMappings
                .FirstOrDefault(m => m.UserIds.Contains(adminId) || m.TeamLeaderId == adminId);

            if (employeeTeam != null)
            {
                employee.TeamId = employeeTeam.TeamId;
                employee.ManagerId = GetManagerId(employees, employeeTeam.TeamLeaderId,adminId);
            }

            var employeeDepartment = departmentMappings
                .FirstOrDefault(m => m.UserIds.Contains(adminId) || m.ManagerId == adminId);

            if (employeeDepartment != null)
            {
                employee.DepartmentId = employeeDepartment.DepartmentId;
                employee.ManagerId ??= GetManagerId(employees, employeeDepartment.ManagerId,adminId);
            }
            
        }
    }

    private int? GetManagerId(List<Employee> employees, int managerOrLeadUesId, int employeeAdminId)
    {
        if (managerOrLeadUesId == employeeAdminId)
        {
            return null;
        }
        
        var cosherManagerEmployee = employees.FirstOrDefault(e => e.EmployeeMapping.AdminId == managerOrLeadUesId);
        return cosherManagerEmployee?.Id;
    }
}