namespace RealtoCrm.DataImporter.Services.Implementation;

using System;
using System.Linq;
using System.Threading.Tasks;
using Abp.Domain.Uow;
using Abp.EntityFrameworkCore;
using AutoMapper;
using Companies;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Models.ResponseModels;
using static CosherConsts.Tenants;

public class TeamsUesAdditionsImporterService(
    Log log,
    IMapper mapper,
    IConfiguration configuration,
    IDbContextResolver dbContextResolver,
    IConnectionStringResolver connectionStringResolver,
    IJsonSerializerService<TeamsUesAdditionsImporterService> jsonSerializerService,
    IImportDocumentFetchService importDocumentFetchService,
    IAddressMappingService addressMappingService,
    IMapMoneyService mapMoneyService,
    IMapSourceCategoryService mapSourceCategoryService)
    : BaseUesImporterService<Team, TeamsUesAdditionsImporterService>(
        log,
        mapper,
        configuration,
        dbContextResolver,
        connectionStringResolver,
        jsonSerializerService,
        importDocumentFetchService,
        addressMappingService,
        mapMoneyService,
        mapSourceCategoryService)
{
    protected override string ImportDocumentUrl => "Not used for now";

    public override async Task<int> Import()
    {
        log.Write($"Started {nameof(Team)}s import...");

        var uesCompany = await this.Mapper
            .ProjectTo<CompaniesImporterResponseModel>(this.DbContext
                .Companies
                .AsNoTracking()
                .Where(c => c.Name == UniqueEstatesTenantName))
            .FirstOrDefaultAsync();

        if (uesCompany is null)
        {
            throw new InvalidOperationException($"Tenant with name '{UniqueEstatesCompanyName}' not found");
        }

        var callCenterDepartment = await this.DbContext
            .Departments
            .AsNoTracking()
            .Where(d =>
                d.Name == "Call Center" &&
                d.TenantId == uesCompany.TenantId &&
                d.CompanyId == uesCompany.Id)
            .Select(d => d.Id)
            .FirstOrDefaultAsync();
        
        var administrationDepartment = await this.DbContext
            .Departments
            .AsNoTracking()
            .Where(d =>
                d.Name == "Администрация" &&
                d.TenantId == uesCompany.TenantId &&
                d.CompanyId == uesCompany.Id)
            .Select(d => d.Id)
            .FirstOrDefaultAsync();
        
        var lawDepartment = await this.DbContext
            .Departments
            .AsNoTracking()
            .Where(d =>
                d.Name == "Юридическа" &&
                d.TenantId == uesCompany.TenantId &&
                d.CompanyId == uesCompany.Id)
            .Select(d => d.Id)
            .FirstOrDefaultAsync();

        if (callCenterDepartment is 0 || administrationDepartment is 0 || lawDepartment is 0)
        {
            throw new InvalidOperationException("Additional Department is missing");
        }
        
        var teams = new[]
        {
            new Team
            {
                Name = "Тийм Лидер Ива Зарева",
                IsActive = true,
                DepartmentId = callCenterDepartment,
                TenantId = uesCompany.TenantId,
                CompanyId = uesCompany.Id
            },
            new Team
            {
                Name = "Тийм Лидер Силвия Неделва",
                IsActive = true,
                DepartmentId = administrationDepartment,
                TenantId = uesCompany.TenantId,
                CompanyId = uesCompany.Id
            },
            new Team
            {
                Name = "Тийм Лидер Виолина Станкова",
                IsActive = true,
                DepartmentId = lawDepartment,
                TenantId = uesCompany.TenantId,
                CompanyId = uesCompany.Id
            },
        };

        await this.DbContext.Teams.AddRangeAsync(teams);

        var count = await this.DbContext.SaveChangesAsync();

        log.Write($"Finished UES Additional {nameof(Team)}s import.");

        return count;
    }
}