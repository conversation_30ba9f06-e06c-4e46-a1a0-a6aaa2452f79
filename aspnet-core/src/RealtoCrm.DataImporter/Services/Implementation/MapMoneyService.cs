namespace RealtoCrm.DataImporter.Services.Implementation;

using System;

public class MapMoneyService : IMapMoneyService
{
    public decimal MapMoneyAmount(double amount, int currencyId)
    {
        if (amount == 0)
        {
            return 0;
        }
        
        const double euroToLevBnbCourse = 0.511292;
        const double euroToUsdBnbCourse = 0.92659087;
        return currencyId switch
        {
            2 => (decimal)(amount * euroToLevBnbCourse),
            3 => (decimal)(amount * euroToUsdBnbCourse),
            _ => (decimal)amount
        };
    }
    
    public decimal MapMoneyAndRoundAmount(double amount, int currencyId)
    {
        var mappedAmount = this.MapMoneyAmount(amount, currencyId);
    
        return Math.Round(mappedAmount, 0, MidpointRounding.AwayFromZero);
    }
}