using System;
using System.Linq;
using System.Threading.Tasks;
using Abp.Domain.Uow;
using Abp.EntityFrameworkCore;
using AutoMapper;
using Microsoft.Extensions.Configuration;
using Microsoft.EntityFrameworkCore;
using RealtoCrm.Extensions;
using RealtoCrm.Nomenclatures;
using RealtoCrm.DataImporter.Services.Models;
using RealtoCrm.Nomenclatures.Municipalities.Models;

namespace RealtoCrm.DataImporter.Services.Implementation;

public class MunicipalitiesUesImporterService(
    Log log,
    IMapper mapper,
    IConfiguration configuration,
    IDbContextResolver dbContextResolver,
    IConnectionStringResolver connectionStringResolver,
    IJsonSerializerService<MunicipalitiesUesImportModel> jsonSerializerService,
    IImportDocumentFetchService importDocumentFetchService,
    IAddressMappingService addressMappingService,
    IMapMoneyService mapMoneyService,
    IMapSourceCategoryService mapSourceCategoryService)
    : BaseUesImporterService<Municipality, MunicipalitiesUesImportModel>(
        log,
        mapper,
        configuration,
        dbContextResolver,
        connectionStringResolver,
        jsonSerializerService,
        importDocumentFetchService,
        addressMappingService,
        mapMoneyService,
        mapSourceCategoryService)
{
    protected override string ImportDocumentUrl => this.DefaultUrl + "/municipalities";
    
    public override async Task<int> Import()
    {
        log.Write("Started MunicipalitiesMapping import...");

        var count = 0;
        var pageNumber = 1;

        Console.WriteLine("Fetching initial page");

        var firstPageQueryString = await this.ImportDocumentFetchService.FetchDocumentAsync(
            this.ImportDocumentUrl,
            this.ApiKey);
        var totalPagesCount = GetTotalPageCount(firstPageQueryString);

        this.DbContext.ChangeTracker.AutoDetectChangesEnabled = false;

        var provinceMappings = await this.DbContext.ProvinceMappings
            .Where(pm => pm.AdminId != null)
            .ToDictionaryAsync(pm => pm.AdminId, pm => pm.ProvinceId);

        var municipalitiesList = this.Mapper
            .ProjectTo<MunicipalityResponseModel>(this.DbContext.Municipalities)
            .ToList();

        while (pageNumber <= totalPagesCount)
        {
            var remainingPages = totalPagesCount - pageNumber + 1;
            var currentBatchSize = remainingPages < BatchSize ? remainingPages : BatchSize;
            var startTime = DateTime.Now;

            var batchData = await this.GetEntitiesForPages(pageNumber, currentBatchSize).ToListAsync();

            if (!batchData.Any())
            {
                break;
            }

            foreach (var municipality in batchData)
            {
                if (!municipality.RegionId.HasValue || !provinceMappings.TryGetValue(municipality.RegionId.Value, out var provinceId))
                {
                    continue;
                }

                var existingMunicipality = municipalitiesList
                    .FirstOrDefault(m => m.Name == municipality.Name && m.ProvinceId == provinceId);

                if (existingMunicipality != null)
                {
                    var existingMapping = await this.DbContext.MunicipalityMappings
                        .FirstOrDefaultAsync(mm => mm.MunicipalityId == existingMunicipality.Id);

                    if (existingMapping != null && existingMapping.AdminId == null)
                    {
                        existingMapping.AdminId = municipality.Id;
                        this.DbContext.Entry(existingMapping).State = EntityState.Modified;
                    }
                    else if (existingMapping == null)
                    {
                        var newMapping = new MunicipalityMapping
                        {
                            MunicipalityId = existingMunicipality.Id,
                            AdminId = municipality.Id
                        };
                        await this.DbContext.MunicipalityMappings.AddAsync(newMapping);
                    }
                }
                else
                {
                    var newMunicipality = new Municipality
                    {
                        Name = municipality.Name ?? string.Empty,
                        ProvinceId = provinceId,
                        Ekatte = municipality.Ekatte ?? string.Empty,
                        Code = municipality.Code ?? string.Empty,
                        CreationTime = DateTime.Now,
                        IsActive = municipality.IsVisible
                    };
                    await this.DbContext.Municipalities.AddAsync(newMunicipality);
                    await this.DbContext.SaveChangesAsync();

                    var newMapping = new MunicipalityMapping
                    {
                        MunicipalityId = newMunicipality.Id,
                        AdminId = municipality.Id
                    };
                    await this.DbContext.MunicipalityMappings.AddAsync(newMapping);
                }
            }

            await this.DbContext.SaveChangesAsync();

            var duration = (DateTime.Now - startTime).TotalSeconds;
            Console.WriteLine($"{batchData.Count} Municipalities processed in {duration} seconds");

            count += batchData.Count;
            pageNumber += currentBatchSize;
        }

        log.Write("Fetched count: " + count);
        log.Write("Finished import.");

        return count;
    }
}
