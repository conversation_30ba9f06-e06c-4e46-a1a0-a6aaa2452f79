namespace RealtoCrm.DataImporter.Services.Implementation;

using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Domain.Uow;
using Abp.EntityFrameworkCore;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Models;
using Models.ResponseModels;
using Nomenclatures;

public class ExternalAgenciesUesImporterService(
    Log log,
    IMapper mapper,
    IConfiguration configuration,
    IDbContextResolver dbContextResolver,
    IConnectionStringResolver connectionStringResolver,
    IJsonSerializerService<ExternalAgencyUesImportModel> jsonSerializerService,
    IImportDocumentFetchService importDocumentFetchService,
    IAddressMappingService addressMappingService,
    IMapMoneyService mapMoneyService,
    IMapSourceCategoryService mapSourceCategoryService)
    : BaseUesImporterService<ExternalAgency, ExternalAgencyUesImportModel>(
        log,
        mapper,
        configuration,
        dbContextResolver,
        connectionStringResolver,
        jsonSerializerService,
        importDocumentFetchService,
        addressMappingService,
        mapMoneyService,
        mapSourceCategoryService)
{
    protected override string ImportDocumentUrl => this.DefaultUrl + "/agencies";

    protected override async Task<List<ExternalAgencyUesImportModel>> UpdateExistingMappingsAndFilterDuplicates(
        List<ExternalAgencyUesImportModel> batchData)
    {
        var batchDataNames = batchData.Select(x => x.Name).ToHashSet();
        var processedItems = new HashSet<string>();

        var existingMappings = await this.DbContext.ExternalAgencyMappings
            .Include(wm => wm.ExternalAgency)
            .Where(wm => batchDataNames.Contains(wm.ExternalAgency.Name))
            .ToListAsync();

        foreach (var mapping in existingMappings)
        {
            var matchedModel = batchData.FirstOrDefault(x => x.Name == mapping.ExternalAgency.Name);

            if (matchedModel != null)
            {
                var existingAdminIds = (mapping.AdminIds ?? "")
                    .Split(',', System.StringSplitOptions.RemoveEmptyEntries)
                    .ToHashSet();

                existingAdminIds.Add(matchedModel.AdminId.ToString());

                mapping.AdminIds = string.Join(",", existingAdminIds);
                processedItems.Add(matchedModel.Name);
                this.DbContext.Entry(mapping).State = EntityState.Modified;
            }
        }

        return batchData
            .Where(x => !processedItems.Contains(x.Name))
            .ToList();
    }

    protected override void DecorateBefore(List<ExternalAgency> entityList,
        IEnumerable<ExternalAgencyUesImportModel> batchAsiData)
    {
        var nameToEntities = entityList
            .GroupBy(e => e.Name)
            .Where(g => g.Count() > 1)
            .ToDictionary(g => g.Key, g => g.ToList());

        foreach (var kvp in nameToEntities)
        {
            var name = kvp.Key;
            var duplicatesList = kvp.Value;
            var primary = duplicatesList.First();

            var adminIds = batchAsiData
                .Where(x => x.Name == name)
                .Select(x => x.AdminId.ToString())
                .ToHashSet();

            var mapping = new ExternalAgencyMapping
            {
                ExternalAgency = primary,
                AdminIds = string.Join(",", adminIds)
            };

            this.DbContext.ExternalAgencyMappings.Add(mapping);

            for (int i = 1; i < duplicatesList.Count; i++)
            {
                entityList.Remove(duplicatesList[i]);
            }
        }
    }

    protected override async Task DecorateAfter(IEnumerable<ExternalAgency> entityList,
        List<ExternalAgencyUesImportModel> adminData)
    {
        var namesFromAdmin = adminData
            .Select(p => p.Name)
            .ToHashSet();

        var currentAgencies = this.Mapper
            .ProjectTo<ImporterResponseModel>(this
                .DbContext
                .ExternalAgencies
                .Where(wp => namesFromAdmin.Contains(wp.Name)))
            .ToList();

        var agencyMappings = entityList
            .Where(agency => this.DbContext.ExternalAgencyMappings
                .All(m => m.ExternalAgencyId != agency.Id))
            .Select(agency =>
            {
                var correspondingModel = adminData.FirstOrDefault(p => p.Name == agency.Name);
                if (correspondingModel == null)
                {
                    return null;
                }

                var currentModel = currentAgencies.FirstOrDefault(m => m.Name == correspondingModel.Name);
                if (currentModel == null)
                {
                    return null;
                }

                return new ExternalAgencyMapping()
                {
                    ExternalAgencyId = currentModel.Id,
                    AdminIds = currentModel.Id.ToString()
                };
            })
            .Where(mapping => mapping != null)
            .ToList();

        if (agencyMappings.Count > 0)
        {
            await this.DbContext.ExternalAgencyMappings.AddRangeAsync(agencyMappings!);
            await this.DbContext.SaveChangesAsync();
        }
    }
}