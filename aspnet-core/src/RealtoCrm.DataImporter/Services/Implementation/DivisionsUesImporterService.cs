namespace RealtoCrm.DataImporter.Services.Implementation;

using System;
using System.Linq;
using System.Threading.Tasks;
using Abp.Domain.Uow;
using Abp.EntityFrameworkCore;
using AutoMapper;
using Companies;
using Constants;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Models;
using Models.ResponseModels;

public class DivisionsUesImporterService(
    Log log,
    IMapper mapper,
    IConfiguration configuration,
    IDbContextResolver dbContextResolver,
    IConnectionStringResolver connectionStringResolver,
    IJsonSerializerService<DivisionUesImportModel> jsonSerializerService,
    IImportDocumentFetchService importDocumentFetchService,
    IAddressMappingService addressMappingService,
    IMapMoneyService mapMoneyService,
    IMapSourceCategoryService mapSourceCategoryService)
    : BaseUesImporterService<Division, DivisionUesImportModel>(
        log,
        mapper,
        configuration,
        dbContextResolver,
        connectionStringResolver,
        jsonSerializerService,
        importDocumentFetchService,
        addressMappingService,
        mapMoneyService,
        mapSourceCategoryService)
{
    protected override string ImportDocumentUrl => "Not used for now";

    public override async Task<int> Import()
    {
        log.Write($"Started {nameof(Division)}s import...");

        var uniqueEstatesCompany = await Mapper
            .ProjectTo<CompaniesImporterResponseModel>(this.DbContext
                .Companies
                .AsNoTracking()
                .Where(c => c.Name == ImporterConstants.UniqueEstatesTenantName))
            .FirstOrDefaultAsync();

        if (uniqueEstatesCompany is null)
        {
            throw new InvalidOperationException(
                $"Tenant with name '{ImporterConstants.UniqueEstatesTenantName}' not found");
        }

        var bulgariaDivision = await this.DbContext
            .Divisions
            .Where(d =>
                d.Name == "България" &&
                d.TenantId == uniqueEstatesCompany.TenantId &&
                d.CompanyId == uniqueEstatesCompany.Id)
            .FirstOrDefaultAsync();

        if (bulgariaDivision is null)
        {
            await this.DbContext.Divisions.AddAsync(new Division
            {
                Name = "България",
                IsActive = true,
                TenantId = uniqueEstatesCompany.TenantId,
                CompanyId = uniqueEstatesCompany.Id
            });
        }

        var count = await this.DbContext.SaveChangesAsync();

        log.Write($"Finished {nameof(Division)}s import.");

        return count;
    }
}