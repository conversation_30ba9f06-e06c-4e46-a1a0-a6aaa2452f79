namespace RealtoCrm.DataImporter.Services.Implementation;

using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Domain.Uow;
using Abp.EntityFrameworkCore;
using AutoMapper;
using Microsoft.Extensions.Configuration;
using Models;
using Models.ResponseModels;
using Nomenclatures;

public class WorkplacesAsiImporterService(
    Log log,
    IMapper mapper,
    IConfiguration configuration,
    IDbContextResolver dbContextResolver,
    IConnectionStringResolver connectionStringResolver,
    IJsonSerializerService<WorkplacesAsiImportModel> jsonSerializerService,
    IImportDocumentFetchService importDocumentFetchService,
    IAddressMappingService addressMappingService,
    IMapMoneyService mapMoneyService,
    IMapSourceCategoryService mapSourceCategoryService)
    : BaseAsiImporterService<Workplace, WorkplacesAsiImportModel>(
        log,
        mapper,
        configuration,
        dbContextResolver,
        connectionStringResolver,
        jsonSerializerService,
        importDocumentFetchService,
        addressMappingService,
        mapMoneyService,
        mapSourceCategoryService)
{
    protected override string ImportDocumentUrl => this.DefaultUrl + "/company";

    protected override async Task DecorateAfter(IEnumerable<Workplace> entityList,
        List<WorkplacesAsiImportModel> batchAsiData)
    {
        var namesFromAsi = batchAsiData
            .Select(p => p.Name)
            .ToHashSet();

        var currentWorkplaces = this.Mapper
            .ProjectTo<ImporterResponseModel>(this
                .DbContext
                .Workplaces
                .Where(wp => namesFromAsi.Contains(wp.Name)))
            .ToList();

        var workplaceMappings = entityList
            .Select(municipality =>
            {
                var correspondingWorkplace = batchAsiData.FirstOrDefault(p => p.Name == municipality.Name);
                if (correspondingWorkplace == null)
                {
                    return null;
                }

                var currentWorkplace = currentWorkplaces.FirstOrDefault(m => m.Name == correspondingWorkplace.Name);
                if (currentWorkplace == null)
                {
                    return null;
                }

                return new WorkplaceMapping
                {
                    //we assign the same id since there is no id coming from ASi
                    WorkplaceId = currentWorkplace.Id,
                    AsiId = currentWorkplace.Id
                };
            })
            .Where(mapping => mapping != null)
            .ToList();

        if (workplaceMappings.Count > 0)
        {
            await this.DbContext.WorkplaceMappings.AddRangeAsync(workplaceMappings!);
            await this.DbContext.SaveChangesAsync();
        }
    }
}