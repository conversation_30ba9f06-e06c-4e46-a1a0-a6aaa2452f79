using Microsoft.EntityFrameworkCore;
using RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Addresses;

namespace RealtoCrm.DataImporter.Services.Implementation;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Domain.Uow;
using Abp.EntityFrameworkCore;
using AutoMapper;
using Microsoft.Extensions.Configuration;
using RealtoCrm.Extensions;
using RealtoCrm.Nomenclatures;
using Models;

public class ProvinceMappingsUesImporterService(
    Log log,
    IMapper mapper,
    IConfiguration configuration,
    IDbContextResolver dbContextResolver,
    IConnectionStringResolver connectionStringResolver,
    IJsonSerializerService<ProvinceUesImportModel> jsonSerializerService,
    IImportDocumentFetchService importDocumentFetchService,
    IAddressMappingService addressMappingService,
    IMapMoneyService mapMoneyService,
    IMapSourceCategoryService mapSourceCategoryService)
    : BaseUesImporterService<Province, ProvinceUesImportModel>(
        log,
        mapper,
        configuration,
        dbContextResolver,
        connectionStringResolver,
        jsonSerializerService,
        importDocumentFetchService,
        addressMappingService,
        mapMoneyService,
        mapSourceCategoryService)
{
    protected override string ImportDocumentUrl => this.DefaultUrl + "/regions";
    private readonly Dictionary<string, string> exceptionMappings = new()
    {
        { "София", "София (област)" },
    };

    public override async Task<int> Import()
    {
        log.Write("Started ProvinceMapping import...");

        var count = 0;
        var pageNumber = 1;

        Console.WriteLine("Fetching initial page");

        var firstPageQueryString = await this.ImportDocumentFetchService.FetchDocumentAsync(
            this.ImportDocumentUrl,
            this.ApiKey);
        var totalPagesCount = GetTotalPageCount(firstPageQueryString);

        this.DbContext.ChangeTracker.AutoDetectChangesEnabled = false;

        var provincesList = this.Mapper
            .ProjectTo<UesAdministrativeDivisionImporterResponseModel>(this.DbContext.Provinces)
            .ToList();

        while (pageNumber <= totalPagesCount)
        {
            var remainingPages = totalPagesCount - pageNumber + 1;
            var currentBatchSize = remainingPages < BatchSize ? remainingPages : BatchSize;
            var startTime = DateTime.Now;

            var batchData = await this.GetEntitiesForPages(pageNumber, currentBatchSize).ToListAsync();
            
            var mappingDependencies = await this.GetMappingDependencies();
            
            if (!batchData.Any())
            {
                break;
            }

            foreach (var province in batchData)
            {
                if (exceptionMappings.TryGetValue(province.Name, out var mappedName))
                {
                    province.Name = mappedName;
                }
                
                var mappedProvince = this.Mapper.Map<Province>(province, opt =>
                    mappingDependencies.ForEach(x => opt.Items[x.Key] = x.Value));

                var correspondingProvince = provincesList.FirstOrDefault(p => p.Name == province.Name);
                
                if (correspondingProvince == null)
                {
                    var newProvince = new Province
                    {
                        Name = mappedProvince.Name,
                        Code = "UNMPD",
                        CountryId = mappedProvince.Country.Id,
                        CreationTime = DateTime.Now,
                        IsActive = mappedProvince.IsActive,
                    };
                    await this.DbContext.Provinces.AddAsync(newProvince);
                    await this.DbContext.SaveChangesAsync();

                    correspondingProvince = new UesAdministrativeDivisionImporterResponseModel
                    {
                        Id = newProvince.Id,
                        Name = newProvince.Name
                    };
                    provincesList.Add(correspondingProvince);

                    log.Write($"New province '{province.Name}' added with ID {newProvince.Id}");
                }

                var existingMapping = await this.DbContext.ProvinceMappings
                    .FirstOrDefaultAsync(m => m.ProvinceId == correspondingProvince.Id);

                if (existingMapping != null)
                {
                    if (existingMapping.AdminId == null)
                    {
                        existingMapping.AdminId = province.AdminId;
                        this.DbContext.Entry(existingMapping).State = EntityState.Modified;
                    }
                }
                else
                {
                    var newMapping = new ProvinceMapping
                    {
                        ProvinceId = correspondingProvince.Id,
                        AdminId = province.AdminId
                    };
                    await this.DbContext.ProvinceMappings.AddAsync(newMapping);
                }
            }

            await this.DbContext.SaveChangesAsync();

            var duration = (DateTime.Now - startTime).TotalSeconds;
            Console.WriteLine($"{batchData.Count} Provinces processed in {duration} seconds");

            count += batchData.Count;
            pageNumber += currentBatchSize;
        }

        log.Write("Fetched count: " + count);
        log.Write("Finished import.");

        return count;
    }
    
    protected override async Task<IEnumerable<KeyValuePair<string, object>>> GetMappingDependencies()
    {
        var countries = await this.DbContext
            .Set<Country>()
            .ToListAsync();

        return new Dictionary<string, object>
        {
            { CountryValueResolver.CountriesItemsKey, countries },
        };
    }
}
