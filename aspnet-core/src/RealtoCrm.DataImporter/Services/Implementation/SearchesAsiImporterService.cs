namespace RealtoCrm.DataImporter.Services.Implementation;

using System;
using Employees;
using System.Collections.Generic;
using System.Linq;
using Abp.Domain.Uow;
using Abp.EntityFrameworkCore;
using Models.ResponseModels;
using Extensions;
using AutoMapper;
using Microsoft.EntityFrameworkCore.Design;
using Microsoft.Extensions.Configuration;
using Clients;
using Models;
using RealtoCrm.DataImporter.Services.Models.Mappings;
using Constants;
using Nomenclatures;
using Searches;
using Money;

public class SearchesAsiImporterService(
    Log log,
    IMapper mapper,
    IConfiguration configuration,
    IDbContextResolver dbContextResolver,
    IConnectionStringResolver connectionStringResolver,
    IJsonSerializerService<SearchAsiImportModel> jsonSerializerService,
    IImportDocumentFetchService importDocumentFetchService,
    IAddressMappingService addressMappingService,
    IMapMoneyService mapMoneyService,
    IMapSourceCategoryService mapSourceCategoryService)
    : BaseAsiImporterService<Search, SearchAsiImportModel>(
        log,
        mapper,
        configuration,
        dbContextResolver,
        connectionStringResolver,
        jsonSerializerService,
        importDocumentFetchService,
        addressMappingService,
        mapMoneyService,
        mapSourceCategoryService)
{
    protected override string ImportDocumentUrl => this.DefaultUrl + "/clientSearch";

    private const int SearchStatusActiveId = 1;

    private const int SearchStatusArchiveId = 2;
    
    protected override IEnumerable<Search> ApplySpecifics(IEnumerable<Search> entities)
    {
        var totalEntitiesCount = entities.Count();

        var entitiesToSave = entities
            .Where(x => x.ClientId > 0)
            .Where(x => x.EmployeeId is null or > 0);

        Console.WriteLine(
            $"There are {totalEntitiesCount - entitiesToSave.Count()} searches which have invalid client ID or employee ID and will not be saved.");

        return entitiesToSave;
    }

    protected override List<SearchAsiImportModel> FilterAsiData(List<SearchAsiImportModel> batchAsiData)
        => batchAsiData
            .Where(s =>
                s.Countries?.Any() == true &&
                s.EstateTypes?.Any() == true &&
                s.Regions?.Any() == true &&
                s.Municipalities?.Any() == true &&
                s.Locations?.Any() == true)
            .ToList();

    protected override void DecorateBefore(List<Search> entityList, IEnumerable<SearchAsiImportModel> batchAsiData)
    {
        var externalAgencies = this.GetDataFromDbAsNoTracking<ExternalAgency, ExternalAgenciesMappingModel>();

        var clientMappings = this.GetDataFromDbAsNoTracking<ClientMapping, ClientMappingModel>();
        
        var employeeMappings = this.GetDataFromDbAsNoTracking<EmployeeMapping, EmployeeMappingModel>();
        
        var defaultEmployeeForTenantSetting = this.DbContext.Employees.FirstOrDefault(employee 
            => employee.UserAccount.EmailAddress.ToLower().Contains(ImporterConstants.DefaultEmployeeEmailForTenantSetting.ToLower()));

        var populatedPlaceMappings =
            this.GetDataFromDbAsNoTracking<PopulatedPlaceMapping, PopulatedPlaceMapResponseModel>();

        var provinceMappings = this.GetDataFromDbAsNoTracking<ProvinceMapping, ProvinceMapResponseModel>();

        var municipalityMappings = this.GetDataFromDbAsNoTracking<MunicipalityMapping, MunicipalityMapResponseModel>();

        var districtMappings = this.GetDataFromDbAsNoTracking<DistrictMapping, DistrictMapResponseModel>();

        entityList.ForEach(search =>
        {
            var correspondingModel = batchAsiData.FirstOrDefault(x => x.SearchAsiId == search.SearchMapping.AsiId);

            if (correspondingModel is null)
            {
                throw new OperationException("Unable to map properties since there is no corresponding model.");
            }

            SetMoneyRange(search, correspondingModel);
            SetExternalAgency(externalAgencies, correspondingModel, search);
            SetPopulatedPlace(populatedPlaceMappings, correspondingModel, search);
            SetProvinces(provinceMappings, correspondingModel, search);
            SetMunicipalities(municipalityMappings, correspondingModel, search);
            SetDistricts(districtMappings, correspondingModel, search);
            SetClientId(clientMappings, correspondingModel, search);
            SetEmployeeId(employeeMappings, correspondingModel, search);
            SetCreatorUser(employeeMappings, correspondingModel, search);
            SetLastModifierUser(employeeMappings, correspondingModel, search);
            SetSearchDetail(correspondingModel, search);
            SetSearchStatus(correspondingModel, search);
            SetTenantId(correspondingModel, search, employeeMappings, defaultEmployeeForTenantSetting);
        });
    }

    private static void SetExternalAgency(IEnumerable<ExternalAgenciesMappingModel> externalAgencies,
        SearchAsiImportModel correspondingModel, Search search)
    {
        if (!search.ExternalAgencyId.HasValue)
        {
            return;
        }

        search.ExternalAgencyId = externalAgencies.FirstOrDefault(ea => ea.AsiId == correspondingModel.AgencyId)?.Id;
    }

    private static void SetPopulatedPlace(IEnumerable<PopulatedPlaceMapResponseModel> populatedPlaces,
        SearchAsiImportModel correspondingModel, Search search)
    {
        if (correspondingModel.Locations != null && !correspondingModel.Locations.Any())
        {
            return;
        }

        correspondingModel.Locations!.ForEach(asiLocation =>
            search.SearchesPopulatedPlaces.Add(new SearchPopulatedPlace
            {
                PopulatedPlaceId = populatedPlaces.FirstOrDefault(pp => pp.AsiId == asiLocation.Id)!.PopulatedPlaceId
            }));
    }

    private static void SetProvinces(IEnumerable<ProvinceMapResponseModel> provinces,
        SearchAsiImportModel correspondingModel, Search search)
    {
        if (correspondingModel.Regions != null && !correspondingModel.Regions.Any())
        {
            return;
        }

        correspondingModel.Regions!.ForEach(asiRegion =>
            search.SearchesProvinces.Add(new SearchProvince
            {
                ProvinceId = provinces.FirstOrDefault(p => p.AsiId == asiRegion.Id)!.ProvinceId
            }));
    }

    private static void SetMunicipalities(IEnumerable<MunicipalityMapResponseModel> municipalities,
        SearchAsiImportModel correspondingModel, Search search)
    {
        if (correspondingModel.Municipalities != null && !correspondingModel.Municipalities.Any())
        {
            return;
        }

        correspondingModel.Municipalities!.ForEach(asiMunicipality =>
            search.SearchesMunicipalities.Add(new SearchMunicipality()
            {
                MunicialityId = municipalities.FirstOrDefault(m => m.AsiId == asiMunicipality.Id)!.MunicipalityId
            }));
    }

    private static void SetDistricts(IEnumerable<DistrictMapResponseModel> districts,
        SearchAsiImportModel correspondingModel, Search search)
    {
        if (correspondingModel.Quarters == null || !correspondingModel.Quarters.Any())
        {
            return;
        }

        correspondingModel.Quarters!.ForEach(asiQuarter =>
        {
            //Todo: Handles duplicate districts with ids 1346/690
            //Remove if statement when deleted district (ID:690) is removed from ASI 
            if (asiQuarter.Id == 1346)
            {
                search.SearchesDistricts.Add(new SearchDistrict()
                {
                    DistrictId = 793
                });

                return;
            }

            //Todo: Handles duplicate districts with ids 1693/1694
            //Remove if statement when deleted district (ID:1693) is removed from ASI 
            if (asiQuarter.Id == 1694)
            {
                search.SearchesDistricts.Add(new SearchDistrict()
                {
                    DistrictId = 1036
                });

                return;
            }

            search.SearchesDistricts.Add(new SearchDistrict()
            {
                DistrictId = districts.FirstOrDefault(d => d.AsiId == asiQuarter.Id)!.DistrictId
            });
        });
    }

    private static void SetClientId(List<ClientMappingModel> clientMappings, SearchAsiImportModel correspondingModel,
        Search search)
    {
        try
        {
            var correspondingClient = clientMappings.FirstOrDefault(x =>
                correspondingModel.ClientId != null
                && x.AsiId == correspondingModel.ClientId);

            var isValidClientId = correspondingClient != null && correspondingModel is { ClientId: > 0 };

            search.ClientId = isValidClientId
                ? correspondingClient!.ClientId
                : 0;
        }
        catch (Exception e)
        {
            Console.WriteLine($"Something went wrong while setting client ID: {search.ClientId} " + e.Message);
        }
    }

    private static void SetEmployeeId(List<EmployeeMappingModel> employeeMappings,
        SearchAsiImportModel correspondingModel,
        Search search)
    {
        try
        {
            var correspondingEmployee = employeeMappings.FirstOrDefault(x =>
                correspondingModel.UserId != null
                && x.AsiId == correspondingModel.UserId);

            var isValidClientId = correspondingEmployee is { EmployeeId: > 0 };

            if (correspondingEmployee != null)
            {
                search.EmployeeId = isValidClientId
                    ? correspondingEmployee.EmployeeId
                    : 0;
            }
        }
        catch (Exception e)
        {
            Console.WriteLine($"Something went wrong while setting client ID: {search.EmployeeId} " + e.Message);
        }
    }

    private static void SetCreatorUser(List<EmployeeMappingModel> employeeMappings,
        SearchAsiImportModel correspondingModel,
        Search search)
    {
        try
        {
            var correspondingEmployee = employeeMappings.FirstOrDefault(x =>
                x.AsiId == correspondingModel.CreatedBy);

            var isValidEmployeeId = correspondingEmployee is { EmployeeId: > 0 };

            if (correspondingEmployee != null)
            {
                search.CreatorUserId = isValidEmployeeId
                    ? correspondingEmployee.EmployeeId
                    : null;
            }
        }
        catch (Exception e)
        {
            Console.WriteLine($"Something went wrong while setting CreatorUser ID: {search.CreatorUserId} " +
                              e.Message);
        }
    }

    private static void SetLastModifierUser(List<EmployeeMappingModel> employeeMappings,
        SearchAsiImportModel correspondingModel,
        Search search)
    {
        try
        {
            var correspondingEmployee = employeeMappings.FirstOrDefault(x =>
                x.AsiId == correspondingModel.UpdatedBy);

            var isValidEmployeeId = correspondingEmployee is { EmployeeId: > 0 };

            if (correspondingEmployee != null)
            {
                search.LastModifierUserId = isValidEmployeeId
                    ? correspondingEmployee.EmployeeId
                    : null;
            }
        }
        catch (Exception e)
        {
            Console.WriteLine($"Something went wrong while setting LastModifierUserId: {search.CreatorUserId} " +
                              e.Message);
        }
    }

    private static void SetSearchDetail(SearchAsiImportModel correspondingModel, Search search)
    {
        search.SearchDetail = new SearchDetail
        {
            HasAttic = MapBooleanFieldFromInt(correspondingModel.Attic),
            HasElevator = MapBooleanFieldFromInt(correspondingModel.Elevator)
        };
    }

    private static void SetSearchStatus(SearchAsiImportModel correspondingModel, Search search)
    {
        if (correspondingModel.ArchiveReasonId != null && correspondingModel.ArchiveReasonId > 0)
        {
            search.SearchStatusId = SearchStatusArchiveId;
            return;
        }

        search.SearchStatusId = SearchStatusActiveId;
    }

    private void SetMoneyRange(Search search, SearchAsiImportModel correspondingModel)
    {
        if (correspondingModel.PriceFrom != null)
        {
            search.MoneyFrom.Amount =
                this.MapMoneyService.MapMoneyAndRoundAmount((double)search.MoneyFrom.Amount,
                    correspondingModel.CurrencyId ?? (int)Currency.BGN);
        }

        if (correspondingModel.PriceTo != null)
        {
            search.MoneyTo.Amount =
                this.MapMoneyService.MapMoneyAndRoundAmount((double)search.MoneyTo.Amount,
                    correspondingModel.CurrencyId ?? (int)Currency.BGN);
        }
    }

    private void SetTenantId(SearchAsiImportModel correspondingModel, Search search,
        IEnumerable<EmployeeMappingModel> employees, Employee? defaultEmployee)
    {
        var correspondingEmployee = employees.FirstOrDefault(x =>
            x.AsiId == correspondingModel.UserId);
        
        if (correspondingEmployee != null)
        {
            search.TenantId = correspondingEmployee.CompanyId > 0
                ? (correspondingEmployee.CompanyId == ImporterConstants.ImotekaCompanyId
                    ? ImporterConstants.ImotekaTenantId
                    : ImporterConstants.AddressTenantId)
                : correspondingEmployee.TenantId > 0
                    ? correspondingEmployee.TenantId
                    : search.TenantId;
        }
        else if (defaultEmployee != null)
        {
            search.EmployeeId = defaultEmployee.Id;
            search.TenantId = ImporterConstants.AddressTenantId;
        }
    }

    private static bool? MapBooleanFieldFromInt(int? value)
    {
        return value switch
        {
            1 => true,
            0 => false,
            _ => null
        };
    }
}