using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Domain.Uow;
using Abp.EntityFrameworkCore;
using AutoMapper;
using Microsoft.Extensions.Configuration;
using RealtoCrm.DataImporter.Services.Models;
using RealtoCrm.DataImporter.Services.Models.ResponseModels;
using RealtoCrm.Nomenclatures;

namespace RealtoCrm.DataImporter.Services.Implementation;

public class StreetsAsiImporterService(
    Log log,
    IMapper mapper,
    IConfiguration configuration,
    IDbContextResolver dbContextResolver,
    IConnectionStringResolver connectionStringResolver,
    IJsonSerializerService<StreetsAsiImportModel> jsonSerializerService,
    IImportDocumentFetchService importDocumentFetchService,
    IAddressMappingService addressMappingService,
    IMapMoneyService mapMoneyService,
    IMapSourceCategoryService mapSourceCategoryService)
    : BaseAsiImporterService<Street, StreetsAsiImportModel>(
        log,
        mapper,
        configuration,
        dbContextResolver,
        connectionStringResolver,
        jsonSerializerService,
        importDocumentFetchService,
        addressMappingService,
        mapMoneyService,
        mapSourceCategoryService)
{
    protected override string ImportDocumentUrl => this.DefaultUrl + "/street";

    protected override void DecorateBefore(List<Street> entityList,
        IEnumerable<StreetsAsiImportModel> batchAsiData)
    {
        var populatedPlaceMappings = this.Mapper
            .ProjectTo<PopulatedPlaceMapResponseModel>(this
                .DbContext
                .PopulatedPlaceMappings)
            .ToList();

        entityList.ForEach(street =>
        {
            var mapping = populatedPlaceMappings.FirstOrDefault(pm => pm.AsiId == street.PopulatedPlaceId);
            if (mapping != null)
            {
                street.PopulatedPlaceId = mapping.PopulatedPlaceId;
            }
            else
            {
                Console.WriteLine(
                    $"No mapping found for entity {street.Name} with populated place id from asi: {street.PopulatedPlaceId}");
            }
        });
    }

    protected override async Task DecorateAfter(IEnumerable<Street> entityList,
        List<StreetsAsiImportModel> batchAsiData)
    {
        var namesFromAsi = batchAsiData
            .Select(p => p.Name)
            .ToHashSet();

        var currentStreets = this.Mapper
            .ProjectTo<DistrictIAndStreetImporterResponseModel>(this
                .DbContext
                .Streets
                .Where(s => namesFromAsi.Contains(s.Name)))
            .ToList();

        var populatedPlacesIds = currentStreets.Select(p => p.PopulatedPlaceId);

        var populatedPlaceMappings = this.Mapper
            .ProjectTo<PopulatedPlaceMapResponseModel>(this
                .DbContext
                .PopulatedPlaceMappings
                .Where(p => populatedPlacesIds.Contains(p.PopulatedPlaceId)))
            .ToList();

        var streetMappings = entityList
            .Select(street =>
            {
                var populatedPlaceMapping =
                    populatedPlaceMappings.FirstOrDefault(p => p.PopulatedPlaceId == street.PopulatedPlaceId);
                var correspondingStreet = batchAsiData.FirstOrDefault(s =>
                    s.Name == street.Name &&
                    (populatedPlaceMapping != null && populatedPlaceMapping.AsiId == s.LocationId));

                if (correspondingStreet == null)
                {
                    return null;
                }

                var currentStreet =
                    currentStreets.FirstOrDefault(s =>
                        s.Name == street.Name &&
                        (populatedPlaceMapping != null &&
                         populatedPlaceMapping.PopulatedPlaceId == s.PopulatedPlaceId));

                if (currentStreet == null)
                {
                    return null;
                }

                return new StreetMapping
                {
                    StreetId = currentStreet.Id,
                    AsiId = correspondingStreet.Id
                };
            })
            .Where(mapping => mapping != null)
            .ToList();

        if (streetMappings.Any())
        {
            await this.DbContext.StreetMappings.AddRangeAsync(streetMappings!);
            await this.DbContext.SaveChangesAsync();
        }
    }
}