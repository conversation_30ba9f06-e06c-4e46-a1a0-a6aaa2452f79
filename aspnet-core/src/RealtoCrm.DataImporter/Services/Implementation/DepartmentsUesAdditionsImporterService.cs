namespace RealtoCrm.DataImporter.Services.Implementation;

using System;
using System.Linq;
using System.Threading.Tasks;
using Abp.Domain.Uow;
using Abp.EntityFrameworkCore;
using AutoMapper;
using Companies;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Models.ResponseModels;
using static CosherConsts.Tenants;

public class DepartmentsUesAdditionsImporterService(
    Log log,
    IMapper mapper,
    IConfiguration configuration,
    IDbContextResolver dbContextResolver,
    IConnectionStringResolver connectionStringResolver,
    IJsonSerializerService<DepartmentsUesAdditionsImporterService> jsonSerializerService,
    IImportDocumentFetchService importDocumentFetchService,
    IAddressMappingService addressMappingService,
    IMapMoneyService mapMoneyService,
    IMapSourceCategoryService mapSourceCategoryService)
    : BaseUesImporterService<Department, DepartmentsUesAdditionsImporterService>(
        log,
        mapper,
        configuration,
        dbContextResolver,
        connectionStringResolver,
        jsonSerializerService,
        importDocumentFetchService,
        addressMappingService,
        mapMoneyService,
        mapSourceCategoryService)
{
    protected override string ImportDocumentUrl => "Not used for now";

    public override async Task<int> Import()
    {
        log.Write($"Started {nameof(Department)}s import...");

        var uesCompany = await this.Mapper
            .ProjectTo<CompaniesImporterResponseModel>(this.DbContext
                .Companies
                .AsNoTracking()
                .Where(c => c.Name == UniqueEstatesCompanyName))
            .FirstOrDefaultAsync();

        if (uesCompany is null)
        {
            throw new InvalidOperationException($"Tenant with name '{UniqueEstatesCompanyName}' not found");
        }

        var bulgariaDivisionId = await this.DbContext
            .Divisions
            .Where(d =>
                d.Name == "България" &&
                d.TenantId == uesCompany.TenantId &&
                d.CompanyId == uesCompany.Id)
            .Select(d => d.Id)
            .FirstOrDefaultAsync();

        if (bulgariaDivisionId is 0)
        {
            throw new InvalidOperationException("Division with name 'България' not found");
        }

        var oborishteOffice = await this.DbContext
            .Offices
            .AsNoTracking()
            .Where(d =>
                d.Name == "Unique Estate Оборище" &&
                d.TenantId == uesCompany.TenantId &&
                d.CompanyId == uesCompany.Id)
            .Select(d => d.Id)
            .FirstOrDefaultAsync();

        var departments = new[]
        {
            new Department
            {
                Name = "Call Center",
                IsActive = true,
                OfficeId = oborishteOffice,
                DivisionId = bulgariaDivisionId,
                TenantId = uesCompany.TenantId,
                CompanyId = uesCompany.Id
            },
            new Department
            {
                Name = "Администрация",
                IsActive = true,
                OfficeId = oborishteOffice,
                DivisionId = bulgariaDivisionId,
                TenantId = uesCompany.TenantId,
                CompanyId = uesCompany.Id
            },
            new Department
            {
                Name = "Бизнес партньори",
                IsActive = true,
                OfficeId = oborishteOffice,
                DivisionId = bulgariaDivisionId,
                TenantId = uesCompany.TenantId,
                CompanyId = uesCompany.Id
            },
            new Department
            {
                Name = "Юридическа",
                IsActive = true,
                OfficeId = oborishteOffice,
                DivisionId = bulgariaDivisionId,
                TenantId = uesCompany.TenantId,
                CompanyId = uesCompany.Id
            }
        };

        await this.DbContext.Departments.AddRangeAsync(departments);

        var count = await this.DbContext.SaveChangesAsync();

        log.Write($"Finished New Estates {nameof(Department)}s import.");

        return count;
    }
}