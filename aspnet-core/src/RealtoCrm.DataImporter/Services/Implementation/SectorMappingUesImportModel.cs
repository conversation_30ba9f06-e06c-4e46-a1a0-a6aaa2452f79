namespace RealtoCrm.DataImporter.Services.Implementation;

using Newtonsoft.Json;
using System;
using RealtoCrm.Mapping;
using RealtoCrm.Nomenclatures;

public class SectorMappingUesImportModel : IMapTo<SectorMapping>
{
    [JsonProperty("id")]
    public int AdminId { get; set; }

    public string Name { get; set; } = default!;

    [JsonProperty("created_at")]
    public DateTime? CreatedAt { get; set; }
    
    [JsonProperty("created_by")]
    public DateTime? CreatedBy { get; set; }
}