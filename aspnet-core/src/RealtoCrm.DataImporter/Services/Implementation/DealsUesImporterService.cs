using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Domain.Uow;
using Abp.EntityFrameworkCore;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using RealtoCrm.Companies;
using RealtoCrm.DataImporter.Services.Mappings.DataResolvers;
using RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Companies;
using RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Deals;
using RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Offers;
using RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Searches;
using RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Viewings;
using RealtoCrm.DataImporter.Services.Models;
using RealtoCrm.Deals;
using RealtoCrm.Employees;
using RealtoCrm.ExternalSearches;
using RealtoCrm.Nomenclatures;
using RealtoCrm.Offers;

namespace RealtoCrm.DataImporter.Services.Implementation;

public class DealsUesImporterService(
    Log log,
    IMapper mapper,
    IConfiguration configuration,
    IDbContextResolver dbContextResolver,
    IConnectionStringResolver connectionStringResolver,
    IJsonSerializerService<DealsUesImporterModel> jsonSerializerService,
    IImportDocumentFetchService importDocumentFetchService,
    IAddressMappingService addressMappingService,
    IMapMoneyService mapMoneyService,
    IMapSourceCategoryService mapSourceCategoryService)
    : BaseUesImporterService<Deal, DealsUesImporterModel>(
        log,
        mapper,
        configuration,
        dbContextResolver,
        connectionStringResolver,
        jsonSerializerService,
        importDocumentFetchService,
        addressMappingService,
        mapMoneyService,
        mapSourceCategoryService)
{
    protected override string ImportDocumentUrl => this.DefaultUrl + "/deals";

    protected override int GetTotalPageCount(string jsonString) => 1;

    protected override IEnumerable<Deal> ApplySpecifics(IEnumerable<Deal> entities)
        => entities
            .Where(x => x.OfferId > 0)
            .Where(x => x.EmployeeId > 0)
            .Where(x => x.DealParticipations.All(dp => dp.Tenant.Id > 0) && x.DealParticipations.All(dp =>
                dp.DealParticipants.All(p => p.Employee.Id > 0)));

    protected override async Task<IEnumerable<DealsUesImporterModel>> GetEntitiesForPages(int startPage, int batchSize)
    {
        log.Write($"Fetching UES Deals");

        string jsonContent = await this.ImportDocumentFetchService
            .FetchDocumentAsync(this.ImportDocumentUrl, this.ApiKey);

        var data = await jsonSerializerService.DeserializeSinglePageWithDataAsArrayAsync(jsonContent);

        var fetchedDataCount = data?.Count() ?? 0;
        log.Write($"Fetched {fetchedDataCount} deals");

        return data ?? [];
    }

    protected override async Task<IEnumerable<KeyValuePair<string, object>>> GetMappingDependencies()
    {
        var sourceCategoriesMapping = await this.DbContext.SourceCategories
            .ToListAsync();

        var offerMappings =
            await this.DbContext.OfferMappings
                .Where(x => x.AdminId != null)
                .ToListAsync();

        var searchMappings =
            await this.DbContext.SearchMappings.Include(x => x.Search)
                .Where(x => x.AdminId != null)
                .ToListAsync();

        var operationTypes = await this.DbContext.OperationTypes
            .ToListAsync();

        var contractTypes = await this.DbContext.ContractTypes
            .ToListAsync();

        var banks = await this.DbContext.Banks
            .ToListAsync();

        var clientsMappings = await this.DbContext.ClientMappings
            .Where(x => x.AdminId != null)
            .ToListAsync();

        var employeesMappings = await this.DbContext.EmployeeMappings.Include(x => x.Employee)
            .Where(x => x.AdminId != null)
            .ToListAsync();

        var companies = await this.DbContext
            .Set<Company>()
            .Include(c => c.Tenant)
            .ToListAsync();

        var company = companies.Single(c => c.Name == UniqueEstatesCompanyName);

        var offers = await this.DbContext.Offers
            .Where(x => offerMappings
                .Select(om => om.OfferId)
                .Contains(x.Id))
            .ToListAsync();
        
        var systemEmployee = await this.DbContext
            .Set<Employee>()
            .Where(e => e.IsSystem && e.UserAccount.TenantId == company.TenantId)
            .SingleAsync();
        
        var externalAgencyMappings = await DbContext
            .Set<ExternalAgencyMapping>()
            .Include(cm => cm.ExternalAgency)
            .ToListAsync();
        
        var externalSearchesMappings = await DbContext.Set<ExternalSearchesMapping>()
            .Include(cm => cm.ExternalSearch)
            .ToListAsync();
        
        var externalOfferMappings = await DbContext.Set<ExternalOfferMapping>()
            .Include(cm => cm.ExternalOffer)
            .ToListAsync();

        return new Dictionary<string, object>
        {
            { SourceCategoryValueResolver.SourceCategoriesItemsKey, sourceCategoriesMapping },
            { OffersByOfferMappingsValueResolver.OffersByOfferMappingsItemsKey, offerMappings },
            { SearchBySearchesMappingValueResolver.SearchesMappingsItemsKey, searchMappings },
            { OperationTypeValueResolver.OperationTypesItemsKey, operationTypes },
            { ContractTypeValueResolver.ContractTypesItemsKey, contractTypes },
            { OfferEmployeeIdByOfferMappings.OffersByOfferMappingsItemsKey, offerMappings },
            { OfferEmployeeIdByOfferMappings.OfferEmployeeIdByOfferMappingsOffersKeys, offers },
            { SearchEmployeeBySearchMappings.SearchEmployeeBySearchMappingsItemsKey, searchMappings },
            { BanksValueResolver.BanksItemsKey, banks },
            { SearchClientIdBySearchMappingValueResolver.SearchesMappingItemsKey, searchMappings },
            { OfferClientIdByOfferMappingValueResolver.OffersByOfferMappingsItemsKey, offerMappings },
            { OfferClientIdByOfferMappingValueResolver.OffersItemsKey, offers },
            { ClientValueResolver.ClientsMappingsItemsKey, clientsMappings },
            { EmployeeValueResolver.EmployeeMappingsItemsKey, employeesMappings },
            { TenantValueResolver<DealsUesImporterModel, Deal>.CompanyNameItemsKey, company },
            { TenantByIdValueResolver.TenantByIdItemsKey, companies },
            { EmployeesByDealValueResolver.EmployeeByDealValueResolverItemsKey, employeesMappings },
            { OppositeEmployeesByDealValueResolver.OppositeSideByParticipationResolverItemKeys, employeesMappings },
            { EmployeeValueResolver.SystemEmployeeItemsKey, systemEmployee },
            { ExternalAgencyValueResolver.ExternalAgenciesItemsKey, externalAgencyMappings },
            { ExternalSearchesValueResolver.ExternalSearchesMappingsItemsKey, externalSearchesMappings },
            { ExternalOffersValueResolver.ExternalOffersMappingsItemsKey, externalOfferMappings },
        };
    }
}