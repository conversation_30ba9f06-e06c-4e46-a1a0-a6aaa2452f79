namespace RealtoCrm.DataImporter.Services.Implementation;

using System;
using System.Linq;
using System.Threading.Tasks;
using Abp.Domain.Uow;
using Abp.EntityFrameworkCore;
using Addresses;
using AutoMapper;
using Companies;
using Constants;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Models;
using MultiTenancy;
using Nomenclatures;

public class OfficesUesImporterService(
    Log log,
    IMapper mapper,
    IConfiguration configuration,
    IDbContextResolver dbContextResolver,
    IConnectionStringResolver connectionStringResolver,
    IJsonSerializerService<OfficeUesImportModel> jsonSerializerService,
    IImportDocumentFetchService importDocumentFetchService,
    IAddressMappingService addressMappingService,
    IMapMoneyService mapMoneyService,
    IMapSourceCategoryService mapSourceCategoryService)
    : BaseUesImporterService<Office, OfficeUesImportModel>(
        log,
        mapper,
        configuration,
        dbContextResolver,
        connectionStringResolver,
        jsonSerializerService,
        importDocumentFetchService,
        addressMappingService,
        mapMoneyService,
        mapSourceCategoryService)
{
    protected override string ImportDocumentUrl => "Not used for now";

    public override async Task<int> Import()
    {
        log.Write($"Started {nameof(Office)}s import...");

        var uniqueEstatesTenantId = await this.DbContext
            .Set<Tenant>()
            .AsNoTracking()
            .Where(c => c.Name == ImporterConstants.UniqueEstatesTenantName)
            .Select(c => c.Id)
            .FirstOrDefaultAsync();

        if (uniqueEstatesTenantId == 0)
        {
            throw new InvalidOperationException(
                $"Tenant with name '{ImporterConstants.UniqueEstatesTenantName}' not found");
        }

        var uniqueEstatesCompanyId = await this.DbContext
            .Set<Company>()
            .AsNoTracking()
            .Where(c => c.Name == ImporterConstants.UniqueEstatesTenantName)
            .Select(c => c.Id)
            .FirstOrDefaultAsync();

        if (uniqueEstatesCompanyId == 0)
        {
            throw new InvalidOperationException(
                $"Company with name '{ImporterConstants.UniqueEstatesTenantName}' not found");
        }

        var bulgariaCountryId = await this.DbContext
            .Set<Country>()
            .AsNoTracking()
            .Where(c => c.Name == "България")
            .Select(c => c.Id)
            .FirstOrDefaultAsync();

        if (bulgariaCountryId == 0)
        {
            throw new InvalidOperationException("Country with name 'България' not found");
        }

        var sofiaProvinceId = await this.DbContext
            .Set<Province>()
            .AsNoTracking()
            .Where(p => p.Name == "София (столица)" && p.CountryId == bulgariaCountryId)
            .Select(c => c.Id)
            .FirstOrDefaultAsync();

        if (sofiaProvinceId == 0)
        {
            throw new InvalidOperationException("Province with name 'София (столица)' not found");
        }

        var sofiaMunicipalityId = await this.DbContext
            .Set<Municipality>()
            .AsNoTracking()
            .Where(m => m.Name == "Столична" && m.ProvinceId == sofiaProvinceId)
            .Select(c => c.Id)
            .FirstOrDefaultAsync();

        if (sofiaMunicipalityId == 0)
        {
            throw new InvalidOperationException("Municipality with name 'Столична' not found");
        }

        var sofiaPopulatedPlaceId = await this.DbContext
            .Set<PopulatedPlace>()
            .AsNoTracking()
            .Where(p => p.Name == "София" && p.MunicipalityId == sofiaMunicipalityId)
            .Select(c => c.Id)
            .FirstOrDefaultAsync();

        if (sofiaPopulatedPlaceId == 0)
        {
            throw new InvalidOperationException("Populated place with name 'София' not found");
        }

        var bulgariaDivisionId = await this.DbContext
            .Set<Division>()
            .AsNoTracking()
            .Where(d =>
                d.Name == "България" &&
                d.TenantId == uniqueEstatesTenantId &&
                d.CompanyId == uniqueEstatesCompanyId)
            .Select(c => c.Id)
            .FirstOrDefaultAsync();

        if (bulgariaDivisionId == 0)
        {
            throw new InvalidOperationException("Division with name 'София' not found");
        }

        var oborishteDistrictId = await this.DbContext
            .Set<District>()
            .AsNoTracking()
            .Where(d => d.Name == "Оборище" && d.PopulatedPlaceId == sofiaPopulatedPlaceId)
            .Select(c => c.Id)
            .FirstOrDefaultAsync();

        if (oborishteDistrictId == 0)
        {
            throw new InvalidOperationException("District with name 'Оборище' not found");
        }

        var oborishteStreet
            = await this.DbContext
                  .Set<Street>()
                  .FirstOrDefaultAsync(s => s.Name == "ул. Оборище" && s.DistrictId == oborishteDistrictId)
              ?? new Street
              {
                  Name = "ул. Оборище",
                  IsActive = true,
                  DistrictId = oborishteDistrictId,
                  PopulatedPlaceId = sofiaPopulatedPlaceId,
              };

        var oborishteOffice = new Office
        {
            Name = "Unique Estate Оборище",
            Description = "Офиса на Оборище",
            Latitude = 42.694683531794986,
            Longitude = 23.34115610275505,
            IsActive = true,
            TenantId = uniqueEstatesTenantId,
            CompanyId = uniqueEstatesCompanyId,
            DivisionId = bulgariaDivisionId,
            Address = new Address
            {
                CountryId = bulgariaCountryId,
                ProvinceId = sofiaProvinceId,
                MunicipalityId = sofiaMunicipalityId,
                PopulatedPlaceId = sofiaPopulatedPlaceId,
                DistrictId = oborishteDistrictId,
                Street = oborishteStreet,
                StreetNumber = "10",
                TenantId = uniqueEstatesTenantId
            },
        };

        await this.DbContext.Offices.AddAsync(oborishteOffice);

        var centreDistrictId = await this.DbContext
            .Set<District>()
            .AsNoTracking()
            .Where(d => d.Name == "Център" && d.PopulatedPlaceId == sofiaPopulatedPlaceId)
            .Select(c => c.Id)
            .FirstOrDefaultAsync();

        if (centreDistrictId == 0)
        {
            throw new InvalidOperationException("District with name 'Център' not found");
        }

        var patriarhStreet
            = await this.DbContext
                  .Set<Street>()
                  .FirstOrDefaultAsync(s => s.Name == "ул. Патриарх Евтимий" && s.DistrictId == centreDistrictId)
              ?? new Street
              {
                  Name = "ул. Патриарх Евтимий",
                  IsActive = true,
                  DistrictId = centreDistrictId,
                  PopulatedPlaceId = sofiaPopulatedPlaceId,
              };

        var patriarhOffice = new Office
        {
            Name = "Unique Estate Патриарха",
            Description = "Офиса на Патриарха",
            Latitude = 42.6882454650848,
            Longitude = 23.325761403201778,
            IsActive = true,
            TenantId = uniqueEstatesTenantId,
            CompanyId = uniqueEstatesCompanyId,
            DivisionId = bulgariaDivisionId,
            Address = new Address
            {
                CountryId = bulgariaCountryId,
                ProvinceId = sofiaProvinceId,
                MunicipalityId = sofiaMunicipalityId,
                PopulatedPlaceId = sofiaPopulatedPlaceId,
                DistrictId = centreDistrictId,
                Street = patriarhStreet,
                StreetNumber = "17",
                TenantId = uniqueEstatesTenantId
            },
        };

        await this.DbContext.Offices.AddRangeAsync(oborishteOffice, patriarhOffice);

        var count = await this.DbContext.SaveChangesAsync();

        log.Write($"Finished {nameof(Office)}s import.");

        return count;
    }
}