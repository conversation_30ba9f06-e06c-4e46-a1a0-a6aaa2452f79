namespace RealtoCrm.DataImporter.Services.Implementation;

using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Domain.Uow;
using Abp.EntityFrameworkCore;
using AutoMapper;
using Companies;
using Mappings.DataResolvers.Companies;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Models;
using static CosherConsts.Offices;


public class DepartmentsUesImporterService(
    Log log,
    IMapper mapper,
    IConfiguration configuration,
    IDbContextResolver dbContextResolver,
    IConnectionStringResolver connectionStringResolver,
    IJsonSerializerService<DepartmentsUesImportModel> jsonSerializerService,
    IImportDocumentFetchService importDocumentFetchService,
    IAddressMappingService addressMappingService,
    IMapMoneyService mapMoneyService,
    IMapSourceCategoryService mapSourceCategoryService)
    : BaseUesImporterService<Department, DepartmentsUesImportModel>(
        log,
        mapper,
        configuration,
        dbContextResolver,
        connectionStringResolver,
        jsonSerializerService,
        importDocumentFetchService,
        addressMappingService,
        mapMoneyService,
        mapSourceCategoryService)
{
    protected override string ImportDocumentUrl => this.DefaultUrl + "/departments";

    protected override int GetTotalPageCount(string jsonString) => 1;

    protected override async Task<IEnumerable<DepartmentsUesImportModel>> GetEntitiesForPages(int startPage,
        int batchSize)
    {
        log.Write($"Fetching UES Departments");

        string jsonContent = await this.ImportDocumentFetchService
            .FetchDocumentAsync(this.ImportDocumentUrl, this.ApiKey);

        var data = await jsonSerializerService.DeserializePageAsync(jsonContent);

        var fetchedDataCount = data?.Count() ?? 0;
        log.Write($"Fetched {fetchedDataCount} departments");

        return data ?? [];
    }

    protected override async Task<IEnumerable<KeyValuePair<string, object>>> GetMappingDependencies()
    {
        var divisions = await this.DbContext
            .Set<Division>()
            .ToListAsync();

        var company = await this.DbContext
            .Set<Company>()
            .Include(c => c.Tenant)
            .Where(c => c.Name == UniqueEstatesCompanyName)
            .SingleAsync();

        var office = this.DbContext
            .Set<Office>()
            .FirstOrDefault(c => c.Name.ToLower().Contains(OborishteOfficeName.ToLower())
                                 && c.Company.Name == UniqueEstatesCompanyName);


        return new Dictionary<string, object>
        {
            { DivisionValueResolver.DivisionsItemsKey, divisions },
            { TenantValueResolver<DepartmentsUesImportModel, Department>.CompanyNameItemsKey, company },
            { OfficeValueResolver<DepartmentsUesImportModel, Department>.OfficeNameItemsKey, office },
            { CompanyValueResolver<DepartmentsUesImportModel, Department>.CompanyItemsKey, company },
        };
    }

    protected override async Task DecorateAfter(IEnumerable<Department> entityList,
        List<DepartmentsUesImportModel> batchAsiData)
    {
        var departmentMappings = entityList
            .Select(department =>
            {
                var correspondingDepartment = batchAsiData
                    .FirstOrDefault(o => o.DepartmentName == department.Name);

                if (correspondingDepartment == null)
                {
                    return null;
                }

                var currentDepartment = entityList.FirstOrDefault(o => o.Name == department.Name);

                if (currentDepartment == null)
                {
                    return null;
                }

                return new DepartmentManagerUsersUesMapping
                {
                    DepartmentId = currentDepartment.Id,
                    ManagerId = correspondingDepartment.ManagerId,
                    UserIds = correspondingDepartment.Users.ToList(),
                };
            })
            .Where(mapping => mapping != null)
            .ToList();

        if (departmentMappings.Any())
        {
            await this.DbContext.DepartmentManagerUsersUesMapping.AddRangeAsync(departmentMappings!);
            await this.DbContext.SaveChangesAsync();
        }
    }
}