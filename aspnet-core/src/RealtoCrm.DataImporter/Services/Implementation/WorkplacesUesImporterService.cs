namespace RealtoCrm.DataImporter.Services.Implementation;

using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Domain.Uow;
using Abp.EntityFrameworkCore;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Models;
using Models.ResponseModels;
using Nomenclatures;

public class WorkplacesUesImporterService(
    Log log,
    IMapper mapper,
    IConfiguration configuration,
    IDbContextResolver dbContextResolver,
    IConnectionStringResolver connectionStringResolver,
    IJsonSerializerService<WorkplacesUesImportModel> jsonSerializerService,
    IImportDocumentFetchService importDocumentFetchService,
    IAddressMappingService addressMappingService,
    IMapMoneyService mapMoneyService,
    IMapSourceCategoryService mapSourceCategoryService)
    : BaseUesImporterService<Workplace, WorkplacesUesImportModel>(
        log,
        mapper,
        configuration,
        dbContextResolver,
        connectionStringResolver,
        jsonSerializerService,
        importDocumentFetchService,
        addressMappingService,
        mapMoneyService,
        mapSourceCategoryService)
{
    protected override string ImportDocumentUrl => this.DefaultUrl + "/companies";

    protected override async Task<List<WorkplacesUesImportModel>> UpdateExistingMappingsAndFilterDuplicates(
        List<WorkplacesUesImportModel> batchData)
    {
        var batchDataNames = batchData.Select(x => x.Name).ToHashSet();
        var processedItems = new HashSet<string>();

        var existingMappings = await this.DbContext.WorkplaceMappings
            .Include(wm => wm.Workplace)
            .Where(wm => batchDataNames.Contains(wm.Workplace.Name))
            .ToListAsync();

        foreach (var mapping in existingMappings)
        {
            var matchedModel = batchData.FirstOrDefault(x => x.Name == mapping.Workplace.Name);

            if (matchedModel != null)
            {
                mapping.AdminId = matchedModel.AdminId;
                processedItems.Add(matchedModel.Name);
                this.DbContext.Entry(mapping).State = EntityState.Modified;
            }
        }

        return batchData
            .Where(x => !processedItems.Contains(x.Name))
            .ToList();
    }

    protected override async Task DecorateAfter(IEnumerable<Workplace> entityList,
        List<WorkplacesUesImportModel> adminData)
    {
        var namesFromAdmin = adminData
            .Select(p => p.Name)
            .ToHashSet();
        var currentWorkplaces = this.Mapper
            .ProjectTo<ImporterResponseModel>(this
                .DbContext
                .Workplaces
                .Where(wp => namesFromAdmin.Contains(wp.Name)))
            .ToList();

        var workplaceMappings = entityList
            .Select(workplace =>
            {
                var correspondingWorkplace = adminData.FirstOrDefault(p => p.Name == workplace.Name);
                if (correspondingWorkplace == null)
                {
                    return null;
                }

                var currentWorkplace = currentWorkplaces.FirstOrDefault(m => m.Name == correspondingWorkplace.Name);
                if (currentWorkplace == null)
                {
                    return null;
                }

                return new WorkplaceMapping
                {
                    WorkplaceId = currentWorkplace.Id,
                    AdminId = currentWorkplace.Id
                };
            })
            .Where(mapping => mapping != null)
            .ToList();

        if (workplaceMappings.Count > 0)
        {
            await this.DbContext.WorkplaceMappings.AddRangeAsync(workplaceMappings!);
            await this.DbContext.SaveChangesAsync();
        }
    }
}