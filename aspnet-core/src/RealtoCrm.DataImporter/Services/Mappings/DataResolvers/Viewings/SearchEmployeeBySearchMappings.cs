using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using RealtoCrm.DataImporter.Services.Models;
using RealtoCrm.Deals;
using RealtoCrm.Deposits;
using RealtoCrm.Searches;
using RealtoCrm.Viewings;

namespace RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Viewings;

public class SearchEmployeeBySearchMappings :
    IValueResolver<ViewingsUesImportModel, Viewing, int?>,
    IValueResolver<DealsUesImporterModel, Deal, int?>,
    IValueResolver<DepositsUesImporterModel, Deposit, int>
{
    public const string SearchEmployeeBySearchMappingsItemsKey = nameof(SearchEmployeeBySearchMappings);

    public int? Resolve(ViewingsUesImportModel source, Viewing destination, int? destMember, ResolutionContext context)
    {
        if (source.Match?.ClientSearchId is null)
        {
            return null;
        }
        return GetSearchEmployeeId(source.Match.ClientSearchId.Value,
            context.Items[SearchEmployeeBySearchMappingsItemsKey] as List<SearchMapping>);
    }

    public int? Resolve(DealsUesImporterModel source, Deal destination, int? destMember, ResolutionContext context)
        => GetSearchEmployeeId(source.SearchId,
            context.Items[SearchEmployeeBySearchMappingsItemsKey] as List<SearchMapping>);
    public int Resolve(DepositsUesImporterModel source, Deposit destination, int destMember, ResolutionContext context)
        => GetSearchEmployeeId(source.SearchId, context.Items[SearchEmployeeBySearchMappingsItemsKey] as List<SearchMapping>) ?? 0;

    private static int? GetSearchEmployeeId(int? searchId, List<SearchMapping> searchMappings)
    {
        if (searchId == null)
        {
            return null;
        }
        return searchMappings.FirstOrDefault(x => x.AdminId == searchId)?.Search?.EmployeeId;
    }
}