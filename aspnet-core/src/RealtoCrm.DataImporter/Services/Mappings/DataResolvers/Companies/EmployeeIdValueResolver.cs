namespace RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Companies;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using AutoMapper;
using Employees;
using Models;
using RealtoCrm.Searches;

public class EmployeeIdValueResolver(string propertyName) : IValueResolver<SearchUesImportModel, Search, long?>
{
    public const string EmployeeMappingsItemsKey = nameof(EmployeeIdValueResolver);

    public long? Resolve(SearchUesImportModel source, Search destination, long? destMember,
        ResolutionContext context)
    {
        var propInfo =
            typeof(SearchUesImportModel).GetProperty(propertyName, BindingFlags.Public | BindingFlags.Instance);

        if (propInfo == null)
        {
            throw new ArgumentException($"Property '{propertyName}' not found on {nameof(SearchUesImportModel)}.");
        }

        var adminIdValue = propInfo.GetValue(source) as int?;

        if (adminIdValue == null)
        {
            return null;
        }

        var employeeMappings = context.Items[EmployeeMappingsItemsKey] as List<EmployeeMapping>;
        var correspondingEmployee = employeeMappings?.FirstOrDefault(x => x.AdminId == adminIdValue);

        var isValidEmployee = correspondingEmployee is { EmployeeId: > 0 };

        return isValidEmployee
            ? correspondingEmployee?.EmployeeId
            : null;
    }
}