namespace RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Clients;

using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using Models;
using Nomenclatures;
using RealtoCrm.Clients;
using static CosherConsts.Titles;

public class TitleValueResolver : IValueResolver<ClientsUesImportModel, ClientPersonalData, Title?>
{
    public const string TitlesItemsKey = nameof(TitleValueResolver);
    
    private static Dictionary<int, string> UesTitleIdToCosherTitleName =>
        new()
        {
            { 1, Lawyer },
            { 2, Engineer },
            { 3, Notary },
            { 4, Doctor },
            { 5, <PERSON> },
            { 6, <PERSON> },
            { 7, <PERSON> },
            { 8, <PERSON> },
            { 9, <PERSON><PERSON><PERSON>fesso<PERSON> },
            { 11, <PERSON> },
            { 13, <PERSON> }
        };
    

    public Title? Resolve(ClientsUesImportModel source, ClientPersonalData destination,
        Title? destMember, ResolutionContext context)
    {
        var titles = context.Items[TitlesItemsKey] as List<Title>;

        if (source.TitleId is not null && UesTitleIdToCosherTitleName.TryGetValue(source.TitleId.Value, out var cosherTitleName))
        {
            return titles.FirstOrDefault(t => t.Name == cosherTitleName);
        }

        return null;
    }
}