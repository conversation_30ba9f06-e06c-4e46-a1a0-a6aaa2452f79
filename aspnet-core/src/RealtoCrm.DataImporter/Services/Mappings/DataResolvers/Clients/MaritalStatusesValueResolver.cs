namespace RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Clients;

using System;
using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using Models;
using Nomenclatures;
using RealtoCrm.Clients;
using static CosherConsts.MaritalStatuses;

public class MaritalStatusesValueResolver : IValueResolver<ClientsUesImportModel, ClientPersonalData, MaritalStatus?>
{
    public const string MaritalStatusesItemsKey = nameof(MaritalStatusesValueResolver);


    public MaritalStatus? Resolve(ClientsUesImportModel source, ClientPersonalData destination,
        MaritalStatus? destMember, ResolutionContext context)
    {
        var maritalStatuses = context.Items[MaritalStatusesItemsKey] as List<MaritalStatus>;

        if (source.MaritalStatus is 0)
        {
            return maritalStatuses.FirstOrDefault(t =>
                string.Equals(t.Name, NotMarried, StringComparison.CurrentCultureIgnoreCase));
        }

        return null;
    }
}