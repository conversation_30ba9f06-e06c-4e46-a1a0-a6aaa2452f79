namespace RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Clients;

using System;
using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using Models;
using Nomenclatures;
using RealtoCrm.Clients;

public class ClientWorkplaceValueResolver : IValueResolver<ClientsUesImportModel, Client, ICollection<ClientWorkplace>>
{
    public const string WorkplacesItemsKey = nameof(ClientWorkplaceValueResolver);

    public ICollection<ClientWorkplace> Resolve(ClientsUesImportModel source, Client destination,
        ICollection<ClientWorkplace> destMember, ResolutionContext context)
    {
        var workplaces = context.Items[WorkplacesItemsKey] as List<Workplace>;

        if (string.IsNullOrWhiteSpace(source.Company) || !source.Company.Any(char.IsLetter))
        {
            return new List<ClientWorkplace>();
        }

        var workplace = workplaces.FirstOrDefault(wp =>
            string.Equals(wp.Name, source.Company, StringComparison.CurrentCultureIgnoreCase));

        if (workplace != null)
        {
            return new List<ClientWorkplace>
            {
                new ClientWorkplace
                {
                    WorkplaceId = workplace.Id
                }
            };
        }

        return new List<ClientWorkplace>
        {
            new ClientWorkplace
            {
                Workplace = new Workplace
                {
                    Name = source.Company,
                    IsActive = true,
                    Description = "Default description"
                }
            }
        };
    }
}