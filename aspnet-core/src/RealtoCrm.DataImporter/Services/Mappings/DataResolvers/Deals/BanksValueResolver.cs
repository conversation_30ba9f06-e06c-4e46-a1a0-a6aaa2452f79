using System;
using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using RealtoCrm.DataImporter.Services.Models;
using RealtoCrm.Deals;
using RealtoCrm.Nomenclatures;

namespace RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Deals;

public class BanksValueResolver : IValueResolver<DealsUesImporterModel, Deal, Bank?>
{
    public const string BanksItemsKey = nameof(BanksValueResolver);

    public Bank? Resolve(DealsUesImporterModel source, Deal destination, Bank? destMember, ResolutionContext context)
    {
        var banks = context.Items[BanksItemsKey] as List<Bank>;

        if (source.Bank == null)
        {
            return null;
        }

        return banks.FirstOrDefault(b =>
            string.Compare(b.Name, source.Bank, StringComparison.CurrentCultureIgnoreCase) == 0);
    }
}