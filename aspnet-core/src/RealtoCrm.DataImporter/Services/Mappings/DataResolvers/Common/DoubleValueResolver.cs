namespace RealtoCrm.DataImporter.Services.Mappings.DataResolvers;

using AutoMapper;

public class DoubleValueResolver<TSource, TDest> : IMemberValueResolver<TSource, TDest, string, double>
{
    public double Resolve(TSource source, TDest destination, string sourceMember, double destMember,
        ResolutionContext context)
    {
        return string.IsNullOrWhiteSpace(sourceMember?.Trim()) || !double.TryParse(sourceMember, out var validDouble)
            ? 0
            : validDouble;
    }
}