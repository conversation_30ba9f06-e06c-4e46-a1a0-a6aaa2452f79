namespace RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Offers;

using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using Models;
using RealtoCrm.Offers;

public class EstateAdvantagesResolver(string propertyName) : IValueResolver<OfferUesImportModel, OfferDetail, bool?>
{
    private static readonly Dictionary<int, string> IdToPropertyMap = new()
    {
        { 1, nameof(OfferDetail.IsGatedComplex) },
        { 2, nameof(OfferDetail.HasFitness) },
        { 3, nameof(OfferDetail.HasPool) },
        { 4, nameof(OfferDetail.HasView) },
        { 5, nameof(OfferDetail.IsPetFriendly) },
        { 6, nameof(OfferDetail.HasGarden) },
        { 7, nameof(OfferDetail.HasPublicTransportNearby) },
        { 8, nameof(OfferDetail.HasPublicTransportNearby) },
    };

    public bool? Resolve(OfferUesImportModel source, OfferDetail destination, bool? destMember, ResolutionContext context)
    {
        var ids = source.Meta?.EstateAdvantages?.Select(a => a.Id).ToList();
        if (ids == null || ids.Count == 0) return null;

        var matchFound = ids
            .Select(id => IdToPropertyMap.TryGetValue((int)id!, out var prop) ? prop : null)
            .Any(mapped => mapped == propertyName);

        return matchFound ? true : null;
    }
}