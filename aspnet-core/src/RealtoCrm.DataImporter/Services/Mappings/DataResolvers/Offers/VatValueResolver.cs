namespace RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Offers;

using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using Models;
using Nomenclatures;
using RealtoCrm.Offers;
using static CosherConsts.Vats;

public class VatValueResolver : IValueResolver<OfferUesImportModel, Offer, Vat?>
{
    public const string VatsItemsKey = nameof(VatValueResolver);

    private readonly Dictionary<int?, string> UesFaceingDirectionIdsToFaceingDirectionsNames = new()
    {
        { 0, NotSubjectToVatName },
        { 1, WithoutVatName }
    };

    public Vat? Resolve(OfferUesImportModel source, Offer destination, Vat? destMember, ResolutionContext context)
    {
        var vatNomenclaturesList = context.Items[VatsItemsKey] as List<Vat>;

        return this.UesFaceingDirectionIdsToFaceingDirectionsNames.TryGetValue(source.WithoutVat, out var vatName)
            ? vatNomenclaturesList?.FirstOrDefault(v => v.Name == vatName)
            : null;
    }
}