using RealtoCrm.Deals;

namespace RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Offers;

using System.Collections.Generic;
using System.Linq;
using Abp.Collections.Extensions;
using AutoMapper;
using RealtoCrm.DataImporter.Services.Models;
using RealtoCrm.Nomenclatures;
using RealtoCrm.Offers;
using static RealtoCrm.CosherConsts.ContractTypes;

public class ContractTypeValueResolver
    : IValueResolver<OfferUesImportModel, Offer, ContractType?>,
        IValueResolver<OfferAsiImportModel, Offer, ContractType>,
        IValueResolver<DealsUesImporterModel, Deal, ContractType?>
{
    public const string ContractTypesItemsKey = nameof(ContractTypeValueResolver);

    private static readonly Dictionary<int, string> UesContractTypeNameToContractTypeName =
        new()
        {
            { 1, CommissionContractTypeName },
            { 2, ExclusiveContractTypeName },
            { 3, GuaranteeContractTypeName },
            { 4, PreliminaryForPPContractTypeName },
            { 5, FinalContractTypeName },
            { 6, RentContractTypeName },
            { 7, SpecificEstateContractTypeName },
        };

    private static Dictionary<string, string> AsiContractTypeIdToContractTypeName =>
        new()
        {
            { "commission", CommissionContractTypeName },
            { "executive", ExclusiveContractTypeName },
        };

    // Verified
    public ContractType? Resolve(OfferUesImportModel source, Offer destination, ContractType? destMember,
        ResolutionContext context)
    {
        var contractTypes = context.Items[ContractTypesItemsKey] as List<ContractType>;
        if (source.Contract?.TypeId == null)
        {
            return null;
        }

        var contractTypeName = source.Contract.IsCoexclusive == 1 && source.Contract.TypeId == 2
            ? CoexclusiveContractTypeName
            : UesContractTypeNameToContractTypeName
                  .GetOrDefault(source.Contract.TypeId.Value)
              ?? CommissionContractTypeName;

        return contractTypeName.IsNullOrEmpty()
            ? null
            : contractTypes.FirstOrDefault(ct => ct.Name == contractTypeName);
    }

    public ContractType Resolve(OfferAsiImportModel source, Offer destination, ContractType destMember,
        ResolutionContext context)
        => GetContractType(source.ContractType, context.Items[ContractTypesItemsKey] as List<ContractType>,
            AsiContractTypeIdToContractTypeName);

    private static ContractType? GetContractType(string contractType, List<ContractType> contractTypes,
        Dictionary<string, string> contractTypeNameToContractTypeName)
        => string.IsNullOrEmpty(contractType)
            ? null
            : contractTypes.FirstOrDefault(ct => ct.Name == contractTypeNameToContractTypeName[contractType]);

    public ContractType? Resolve(DealsUesImporterModel source, Deal destination, ContractType? destMember,
        ResolutionContext context)
    {
        var contractTypes = context.Items[ContractTypesItemsKey] as List<ContractType>;
        if (source.ContractType == null)
        {
            return null;
        }

        var contractTypeName = UesContractTypeNameToContractTypeName.FirstOrDefault(x => x.Key == source.ContractType).Value;

        return contractTypeName.IsNullOrEmpty()
            ? null
            : contractTypes.FirstOrDefault(ct => ct.Name == contractTypeName);
    }
}