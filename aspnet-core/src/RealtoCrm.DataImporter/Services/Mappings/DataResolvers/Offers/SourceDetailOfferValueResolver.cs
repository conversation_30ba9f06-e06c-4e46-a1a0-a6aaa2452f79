namespace RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Offers;

using System;
using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using Models;
using Nomenclatures;
using RealtoCrm.Clients;
using RealtoCrm.Offers;
using SourceCategories;

public class SourceDetailOfferValueResolver : IValueResolver<OfferUesImportModel, Offer, SourceDetailOffer?>
{
    public const string SourceCategoriesItemsKey = nameof(SourceDetailOfferValueResolver);
    public const string WebsitesItemsKey = "WebsitesItemsKey";
    public const string SocialMediasItemsKey = "SocialMediasItemsKey";
    public const string ClientMappingsItemsKey = "ClientMappingsItemsKey";

    public SourceDetailOffer? Resolve(OfferUesImportModel source, Offer destination, SourceDetailOffer? destMember,
        ResolutionContext context)
    {
        var sourceCategories = context.Items[SourceCategoriesItemsKey] as List<SourceCategory>;
        var websites = context.Items[WebsitesItemsKey] as List<Website>;
        var socialMedias = context.Items[SocialMediasItemsKey] as List<SocialMedia>;
        var clientMappings = context.Items[ClientMappingsItemsKey] as List<ClientMapping>;

        if (string.IsNullOrWhiteSpace(source.SourceName) ||
            (source.SourceName == CosherConsts.UesSourceCategories.Recommendation && source.RecommendedBy is null or 0))
        {
            return null;
        }

        if (SourceCategoryAndDetailsUesImporterMappings.UesCategoryToAsiCategoryWithDetailTypeInfo.TryGetValue(
                source.SourceName, out var categoryInfo))
        {
            var sourceCategory = sourceCategories.FirstOrDefault(sc => sc.Id == categoryInfo.CategoryId);

            if (sourceCategory is not null)
            {
                var detail = GetCategoryDetail(sourceCategory, categoryInfo.DetailInfo, socialMedias, websites,
                    clientMappings, source);
                return detail;
            }
        }

        return null;
    }

    private SourceDetailOffer? GetCategoryDetail(SourceCategory sourceCategory, string? detailNote,
        List<SocialMedia> socialMedias, List<Website> sites,
        List<ClientMapping> clientMappings, OfferUesImportModel importModel)
    {
        if (string.IsNullOrEmpty(detailNote)) return null;

        switch (detailNote)
        {
            case CosherConsts.Websites.UesSite:
                return new SourceDetailOffer
                {
                    CategoryId = sourceCategory.Id,
                    Website = sites.FirstOrDefault(m => 
                        m.Name.ToLower() == CosherConsts.Websites.UesSite.ToLower())
                };
            case CosherConsts.SocialMedias.LinkedIn:
                return new SourceDetailOffer
                {
                    CategoryId = sourceCategory.Id,
                    SocialMedia = socialMedias
                        .FirstOrDefault(m => m.Name.ToLower() == CosherConsts.SocialMedias.LinkedIn.ToLower())
                };
            case CosherConsts.SocialMedias.Viber:
                return new SourceDetailOffer
                {
                    CategoryId = sourceCategory.Id,
                    SocialMedia = socialMedias.FirstOrDefault(m =>
                        m.Name.ToLower() == CosherConsts.SocialMedias.Viber.ToLower())
                };
            case CosherConsts.Websites.Google:
                return new SourceDetailOffer
                {
                    CategoryId = sourceCategory.Id,
                    Website = sites
                        .FirstOrDefault(m => m.Name.ToLower() == CosherConsts.Websites.Google.ToLower())
                };
            case CosherConsts.UesSourceCategories.SourceDetail:
                var recommendingClient = clientMappings
                    .FirstOrDefault(m => m.AdminId == importModel.RecommendedBy);

                if (recommendingClient != null)
                {
                    return new SourceDetailOffer
                    {
                        CategoryId = sourceCategory.Id,
                        DetailClientId = recommendingClient.ClientId
                    };
                }

                break;
            case CosherConsts.SocialMedias.Facebook:
                return new SourceDetailOffer
                {
                    CategoryId = sourceCategory.Id,
                    SocialMedia = socialMedias
                        .FirstOrDefault(m => m.Name.ToLower() == CosherConsts.SocialMedias.Facebook.ToLower())
                };
            case CosherConsts.SocialMedias.Instagram:
                return new SourceDetailOffer
                {
                    CategoryId = sourceCategory.Id,
                    SocialMedia = socialMedias
                        .FirstOrDefault(m => m.Name.ToLower() == CosherConsts.SocialMedias.Instagram.ToLower())
                };
            case CosherConsts.Websites.HomesBg:
                return new SourceDetailOffer
                {
                    CategoryId = sourceCategory.Id,
                    Website = sites.FirstOrDefault(m => 
                        m.Name.ToLower() == CosherConsts.Websites.HomesBg.ToLower())
                };
            case CosherConsts.Websites.AloBg:
                return new SourceDetailOffer
                {
                    CategoryId = sourceCategory.Id,
                    Website = sites.FirstOrDefault(m => 
                        m.Name.ToLower() == CosherConsts.Websites.AloBg.ToLower())
                };
            case CosherConsts.Websites.ChristieS:
                return new SourceDetailOffer
                {
                    CategoryId = sourceCategory.Id,
                    Website = sites.FirstOrDefault(m => 
                        m.Name.ToLower() == CosherConsts.Websites.ChristieS.ToLower())
                };
            case CosherConsts.UesSourceCategories.ExplicitSourceDetail:
            {
                return ExplicitSourceDetail(importModel, sourceCategory, sites);
            }
        }

        return null;
    }

    private SourceDetailOffer? ExplicitSourceDetail(
        OfferUesImportModel uesImportOffer,
        SourceCategory modelCategory,
        List<Website> sites)
    {
        if (string.IsNullOrEmpty(uesImportOffer.SourceDetailName)) return null;

        var matchedWebsite = sites
            .FirstOrDefault(m => m.Name.Equals(uesImportOffer.SourceDetailName, StringComparison.OrdinalIgnoreCase));

        if (matchedWebsite != null)
        {
            return new SourceDetailOffer
            {
                CategoryId = modelCategory.Id,
                Website = matchedWebsite
            };
        }

        return null;
    }
}