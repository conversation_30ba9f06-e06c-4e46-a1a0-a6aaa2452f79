namespace RealtoCrm.DataImporter.Services.Mappings.DataResolvers;

using System;
using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using RealtoCrm.DataImporter.Services.Models;
using RealtoCrm.Estates;
using RealtoCrm.Offers;
using static RealtoCrm.CosherConsts.EstateTypes;

public class EstateTypeValueResolver
    : IValueResolver<OfferUesImportModel, Estate, EstateType>,
        IValueResolver<OfferAsiImportModel, Estate, EstateType>
{
    public const string EstateTypesItemsKey = nameof(EstateTypeValueResolver);

    private static Dictionary<int, string> UesEstateTypeToEstateTypeName =>
        new()
        {
            { 1, HouseOrVillaEstateTypeName },
            // { 2, Apartment } - Handled in GetApartmentEstateType
            { 5, PlotOrLandEstateTypeName },
            { 6, OfficeEstateTypeName },
            { 7, StoreEstateTypeName },
            { 8, RestaurantEstateTypeName },
            { 11, MaisonetteEstateTypeName },
            { 15, HotelOrMotelEstateTypeName },
            { 16, OfficeBuildingOrShoppingCenterEstateTypeName },
            // { 19, Penthouse} - Handled in GetApartmentEstateType
            { 20, MedicalOfficeEstateTypeName },
            { 22, ResidentialBuildingEstateTypeName },
            { 24, WorkshopOrWarehouseEstateTypeName },
        };

    private static readonly Dictionary<int, string> UesApartmentsByBedroomsCount = new()
    {
        { 0, OneBedroomEstateTypeName },
        { 1, TwoBedroomEstateTypeName },
        { 2, ThreeBedroomEstateTypeName },
        { 3, FourBedroomEstateTypeName },
        { 4, MultiBedroomEstateTypeName },
    };

    private static Dictionary<int, string> AsiEstateTypeToEstateTypeName =
        new()
        {
            { 1, StoreEstateTypeName },
            { 2, MultiBedroomEstateTypeName },
            { 3, ThreeBedroomEstateTypeName },
            { 4, TwoBedroomEstateTypeName },
            { 5, OneBedroomEstateTypeName },
            { 6, PlotOrLandEstateTypeName },
            { 8, OfficeEstateTypeName },
            { 9, HouseFloorEstateTypeName },
            { 10, AgriculturalLandEstateTypeName },
            { 11, ParkingSlotEstateTypeName },
            { 12, ThreeBedroomEstateTypeName },
            { 13, StoreEstateTypeName },
            { 14, OutbuildingOrFarmEstateTypeName },
            { 15, HouseOrVillaEstateTypeName },
            { 16, StoreEstateTypeName },
            { 17, RestaurantEstateTypeName },
            { 18, StudioEstateTypeName },
            { 19, StoreEstateTypeName },
            { 20, RestaurantEstateTypeName },
            { 21, OfficeBuildingOrShoppingCenterEstateTypeName },
            { 22, OfficeBuildingOrShoppingCenterEstateTypeName },
            { 23, GarageEstateTypeName },
            { 25, HotelOrMotelEstateTypeName },
            { 26, StoreEstateTypeName },
            { 27, HouseOrVillaEstateTypeName },
            { 28, WorkshopOrWarehouseEstateTypeName },
        };

    private static EstateType GetApartmentEstateType(OfferUesImportModel sourceImportModel, List<EstateType> estateTypes)
    {
        var bedrooms = sourceImportModel.Bedrooms ?? 0;
        var bedroomsCount = bedrooms <= 3 ? bedrooms : 4;
        return estateTypes.FirstOrDefault(et => et.Name == UesApartmentsByBedroomsCount[bedroomsCount]);
    }
    
    private static EstateType GetNonApartmentEstateType(EstateForOfferUesImportModel sourceImportModel,
        IEnumerable<EstateType> estateTypes)
    {
        var estateTypeName = sourceImportModel.EstateTypeId.HasValue &&
                             UesEstateTypeToEstateTypeName.TryGetValue(sourceImportModel.EstateTypeId.Value,
                                 out var value)
            ? value
            : null;

        return estateTypes.FirstOrDefault(et => et.Name == estateTypeName);
    }

    public EstateType Resolve(OfferUesImportModel source, Estate destination, EstateType destMember,
        ResolutionContext context)
    {
        var estateTypes = context.Items[EstateTypesItemsKey] as List<EstateType>;
        var defaultEstateType = estateTypes.FirstOrDefault(x => x.Name == UnspecifiedEstateTypeName);

        var result = source.Estate?.EstateTypeId switch
        {
            2 or 19 => GetApartmentEstateType(source, estateTypes),
            null => defaultEstateType,
            var _ => GetNonApartmentEstateType(source.Estate, estateTypes),
        };

        return result ?? defaultEstateType;
    }

    public EstateType Resolve(OfferAsiImportModel source, Estate destination, EstateType destMember,
        ResolutionContext context)
    {
        var estateTypes = context.Items[EstateTypesItemsKey] as List<EstateType>;
        var defaultEstateType = estateTypes.FirstOrDefault(x => x.Name == UnspecifiedEstateTypeName);
        if (source.Estate?.EstateTypeId is null)
        {
            return defaultEstateType;
        }

        var result = AsiEstateTypeToEstateTypeName.TryGetValue(source.Estate.EstateTypeId.Value, out var estateTypeName)
            ? estateTypes.FirstOrDefault(x => x.Name == estateTypeName)
            : defaultEstateType;

        return result ?? defaultEstateType;
    }
}