namespace RealtoCrm.DataImporter.Services.Mappings.DataResolvers;

using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using RealtoCrm.DataImporter.Services.Models;
using RealtoCrm.Estates;
using RealtoCrm.Nomenclatures;
using static RealtoCrm.CosherConsts.CompletionLevels;

public class CompletionLevelValueResolver
    : IValueResolver<OfferUesImportModel, Estate, CompletionLevel?>
{
    public const string CompletionLevelsItemsKey = nameof(CompletionLevelValueResolver);

    private static Dictionary<int, string> UesCompletenessTypeIdToUesCompletionLevelName =>
        new()
        {
            { 1, AfterConstructionAllowedCompletionLevelName },
            { 2, RoughConstructionCompletionLevelName },
            { 3, Act15CompletionLevelName },
            { 4, Act16CompletionLevelName },
            { 5, Act16CompletionLevelName },
        };

    public CompletionLevel? Resolve(OfferUesImportModel source, Estate destination, CompletionLevel? destMember, ResolutionContext context)
    {
        if (!source.CompletenessId.HasValue)
        {
            return null;
        }

        var completionLevels = context.Items[CompletionLevelsItemsKey] as List<CompletionLevel>;

        var completionLevelName =
            UesCompletenessTypeIdToUesCompletionLevelName
                .FirstOrDefault(x => x.Key == source.CompletenessId.Value).Value;

        return completionLevels.FirstOrDefault(x => x.Name == completionLevelName);
    }
}