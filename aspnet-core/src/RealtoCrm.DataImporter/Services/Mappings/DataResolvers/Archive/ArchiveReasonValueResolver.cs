namespace RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Archive;

using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using Models;
using Nomenclatures;
using RealtoCrm.Offers;
using RealtoCrm.Searches;

public class ArchiveReasonValueResolver : IValueResolver<OfferUesImportModel, Offer, ArchiveReason?>,
    IValueResolver<SearchUesImportModel, Search, ArchiveReason?>
{
    public const string ArchiveReasonsItemsKey = nameof(ArchiveReasonValueResolver);

    public ArchiveReason? Resolve(OfferUesImportModel source, Offer destination, ArchiveReason? destMember,
        ResolutionContext context)
    {
        var archiveReasons = context.Items[ArchiveReasonsItemsKey] as List<ArchiveReason>;

        if (source.ArchiveReasonUesId is null or 0)
        {
            return null;
        }

        var archiveReasonExists = ArchiveReasonsUesImporterMappings
            .ArchiveReasonUesIdToCosherName.TryGetValue((int)source.ArchiveReasonUesId!, out var archiveName);

        return archiveReasonExists
            ? archiveReasons.FirstOrDefault(n => n.Name == archiveName)
            : null;
    }

    public ArchiveReason? Resolve(SearchUesImportModel source, Search destination, ArchiveReason? destMember,
        ResolutionContext context)
    {
        var archiveReasons = context.Items[ArchiveReasonsItemsKey] as List<ArchiveReason>;

        if (source.ArchiveReasonUesId is null or 0)
        {
            return null;
        }

        var archiveReasonExists = ArchiveReasonsUesImporterMappings
            .SearchArchiveReasonUesIdToCosherName.TryGetValue((int)source.ArchiveReasonUesId!, out var archiveName);

        return archiveReasonExists
            ? archiveReasons.FirstOrDefault(n => n.Name == archiveName)
            : null;
    }
}