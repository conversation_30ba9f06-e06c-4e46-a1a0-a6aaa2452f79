namespace RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Archive;

using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using RealtoCrm.DataImporter.Services.Models;
using RealtoCrm.Employees;
using RealtoCrm.Offers;
using RealtoCrm.Searches;

public class ArchivedByValueResolver : IValueResolver<OfferUesImportModel, Offer, int?>,
    IValueResolver<SearchUesImportModel, Search, int?>
{
    public const string EmployeeMappingsItemsKey = nameof(ArchivedByValueResolver);

    public int? Resolve(OfferUesImportModel source, Offer destination, int? destMember,
        ResolutionContext context)
    {
        var employeeMappings = context.Items[EmployeeMappingsItemsKey] as List<EmployeeMapping>;

        if (source.ArchiveReasonUesId is null or 0 || source.ArchiveBy is null or 0 ||
            !ArchiveReasonsUesImporterMappings.ArchiveReasonUesIdToCosherName
                .ContainsKey((int)source.ArchiveReasonUesId))
        {
            return null;
        }

        var employee = employeeMappings.FirstOrDefault(em => em.AdminId == source.ArchiveBy);

        return employee?.EmployeeId;
    }

    public int? Resolve(SearchUesImportModel source, Search destination, int? destMember,
        ResolutionContext context)
    {
        var employeeMappings = context.Items[EmployeeMappingsItemsKey] as List<EmployeeMapping>;

        if (source.ArchiveReasonUesId is null or 0 || source.ArchiveBy is null or 0 ||
            !ArchiveReasonsUesImporterMappings.SearchArchiveReasonUesIdToCosherName
                .ContainsKey((int)source.ArchiveReasonUesId))
        {
            return null;
        }

        var employee = employeeMappings.FirstOrDefault(em => em.AdminId == source.ArchiveBy);

        return employee?.EmployeeId;
    }
}