namespace RealtoCrm.DataImporter.Services.Mappings;

using System.Collections.Generic;

public class SourceCategoriesAsiImporterMappings
{
    public static IDictionary<int, int> AsiSourceIdToSourceCategoryId =>
        new Dictionary<int, int>
        {
            { 1, 2 },
            { 2, 3 },
            { 3, 4 },
            { 4, 23 },
            { 5, 24 },
            { 6, 25 },
            { 7, 26 },
            { 8, 27 },
            { 9, 28 },
            { 10, 30 },
            { 11, 7 },
            { 12, 8 },
            { 13, 9 },
            { 14, 10 },
            { 15, 11 },
            { 16, 12 },
            { 17, 13 },
            { 18, 14 },
            { 19, 15 },
            { 20, 16 },
            { 21, 17 },
            { 22, 18 },
            { 23, 5 },
            { 24, 20 },
            { 25, 21 },
            { 26, 30 },
            { 27, 30 },
            { 29, 29 },
            { 30, 29 },
            { 31, 30 },
        };
}