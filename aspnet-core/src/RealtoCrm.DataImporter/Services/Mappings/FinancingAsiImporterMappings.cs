namespace RealtoCrm.DataImporter.Services.Mappings;

using System.Collections.Generic;

public class FinancingAsiImporterMappings
{
    public static IDictionary<int, int> AsiFinancingTypeIdToFinancingId =>
        new Dictionary<int, int>
        {
            { 1, 1 },
            { 2, 2 },
            { 3, 3 },
            { 4, 4 },
            { 5, 5 },
            { 6, 6 },
            { 7, 7 },
        };
}