// codacy-disable

namespace RealtoCrm.DataImporter.Services.Mappings;

using System.Collections.Generic;

public static class OfferUesImporterMappings
{
    public static IDictionary<int, int> UesEstateTypeIdToEstateTypeId =>
        new Dictionary<int, int>
        {
            { 1, 8 },  // Къща → Къща/Вила
            { 2, -1 }, // Апартамент → Needs to be determined dynamically
            { 3, 3 },  // Имения → Имения
            { 5, 9 },  // Парцел → Парцел/Терен
            { 6, 13 }, // Офис → Офис
            { 7, 16 }, // Търговски обект → Офис сграда/Търговски център
            { 8, 17 }, // Заведение → Заведение/Ресторант
            { 10, 6 }, // Курорт → Other category (consider matching manually)
            { 11, 22 }, // Мезонет → Мезонет
            { 12, 14 }, // Магазин → Магазин
            { 13, 17 }, // Заведение → Заведение/Ресторант
            { 14, 19 }, // Резиденция → Стопанска сграда/Ферма (Closest match)
            { 15, 15 }, // Хотел/Apartment house → Хотел/Мотел
            { 16, 16 }, // Офис сграда/Сграда → Офис сграда/Търговски център
            { 17, 6 },  // СПА → Other category
            { 18, 6 },  // СПА Център → Other category
            { 19, 4 },  // Пентхаус → Needs custom handling?
            { 20, 20 }, // Клиника → Промишлен имот (Closest match)
            { 21, 10 }, // Земеделски терени → Земеделска земя
            { 22, 28 }, // Сграда → Търговски помещения (Closest match)
            { 23, 28 }, // Сграда → Duplicate, possible manual handling needed
            { 24, 27 }, // Промишлено помещение/Склад → Промишлен имот
        };

    public static IDictionary<int, int> UesOperationTypeIdToOperationTypeId =>
        new Dictionary<int, int>
        {
            { 1, 1 },
            { 2, 4 },
        };
    
    public static IDictionary<int, int> UesEstateKindIdToCrmEstateCategoryId =>
        new Dictionary<int, int>
        {
            { 1, 1 }, // Жилищен имот -> Жилищен
            { 2, 2 }, // Парцел -> Парцели и терени
            { 4, 4 }  // Бизнес имот -> Бизнес
        };

    public static IDictionary<int, int> UesEstateTypeIdToCrmEstateCategoryId =>
        new Dictionary<int, int>
        {
            { 1, 8 }, // Къща -> Къща/Вила
            { 2, 6 }, // Апартамент -> Апартамент
            { 3, 8 }, // Имения -> Къща/Вила
            { 4, 8 }, // Вила -> Къща/Вила
            { 5, 9 }, // Парцел -> Парцел/Терен
            { 6, 12 }, // Офис -> Офис
            { 7, 13 }, // Търговски обект -> Магазин
            { 8, 16 }, // Заведение -> Заведение/Ресторант 
            { 10, 17 }, // Курорт - изтрито е все едно къде го слагаме
            { 11, 6 }, // Мезонет -> Апартамент
            { 12, 13 }, // Магазин -> Магазин
            { 13, 16 }, // Заведение -> Заведение/Ресторант 
            { 14, 8 }, // Резиденция -> Къща/Вила
            { 15, 15 }, // Хотел / Apartment house -> Хотел/Мотел
            { 16, 16 }, // Офис сграда / Сграда -> Офис Сграда/Търговски център
            { 17, 17 }, // СПА -(неактивно все едно къде - хотел или друго)
            { 18, 17 }, // СПА Център - (неактивно все едно къде - хотел или друго)
            { 19, 6 }, // Пентхаус -> Апартамент
            { 20, 13 }, // Клиника -> Магазин
            { 21, 10 }, // Земеделски терени -> Земеделска земя
            { 22, 20 }, // Сграда -> Жилищна сграда
            { 23, 20 }, // Сграда -> Жилищна сграда
            { 24, 19 } // Промишлено помещение / Склад -> Цех/Склад
        };

    public static IDictionary<int, int> UesFurnitureIdToCrmFurnitureId =>
        new Dictionary<int, int>
        {
            { 1, 1 }, // с обзавеждане -> Пълно обзавеждане
            { 2, 2 }, // частично обзавеждане -> Частично обзавеждане
            { 3, 3 }, // без обзавеждане -> Без обзавеждане
            { 9, 4 }, // до ключ -> До ключ
            { 10, 5 }, // на шпакловка и замазка -> На шпакловка и замазка
            { 6, 6 }, // с оборудване -> Пълно оборудване
            { 7, 7 }, // частично оборудване -> Частично оборудване
            { 8, 8 } // без оборудване -> Без оборудване
        };
}