// codacy-disable
namespace RealtoCrm.DataImporter.Services.Mappings;

using System.Collections.Generic;

public static class OfferAsiImporterMappings
{
    public static IDictionary<int, int> AsiEstateTypeIdToEstateTypeId =>
        new Dictionary<int, int>
        {
            { 1, 14 },
            { 2, 5 },
            { 3, 3 },
            { 4, 2 },
            { 5, 1 },
            { 6, 9 },
            { 8, 13 },
            { 9, 7 },
            { 10, 10 },
            { 11, 12 },
            { 12, 3 },
            { 13, 14 },
            { 14, 19 },
            { 15, 8 },
            { 16, 14 },
            { 17, 6 },
            { 18, 6 },
            { 19, 14 },
            { 20, 17 },
            { 21, 16 },
            { 22, 16 },
            { 23, 11 },
            { 25, 15 },
            { 26, 14 },
            { 27, 8 },
            { 28, 20 },
            
        };

    public static IDictionary<int, int> AsiOperationTypeIdToOperationTypeId =>
        new Dictionary<int, int>
        {
            { 1, 1 },
            { 2, 4 },
        };

    public static IDictionary<string, int> AsiContractTypeIdToContractTypeId =>
        new Dictionary<string, int>
        {
            { "commission", 1 },
            { "executive", 2 },
        };

    public static IDictionary<int, string> AsiConstructionTypeIdToAsiConstructionTypeName =>
        new Dictionary<int, string>
        {
            { 1, "Тухла" },
            { 2, "ЕПК" },
            { 3, "ПК" },
            { 8, "Панел" },
            { 9, "Друго" },
        };
    
    public static IDictionary<int, string> AsiConditionIdToAsiConditionName =>
        new Dictionary<int, string>
        {
            { 1, "За ремонт" },
            { 2, "Добро" },
            { 3, "Много добро" },
            { 4, "Отлично" },
            { 5, "След ремонт" },
            { 6, "В строеж" },
        };
    
    public static IDictionary<int, string> AsiCompletenessTypeIdToAsiCompletenessName =>
        new Dictionary<int, string>
        {
            { 2, "Акт 15" },
            { 3, "Акт 16" },
            { 4, "Разрешение за строеж" },
            { 5, "В строеж" },
        };
    
    public static IDictionary<int, string> AsiFurnitureIdToAsiFurnitureName =>
        new Dictionary<int, string>
        {
            { 1, "Частично обзаведен" },
            { 2, "Напълно обзаведен" },
            { 3, "Необзаведен" },
            { 5, "Шпакловка и замазка" },
            { 5, "До ключ" },
        };
    
    public static IDictionary<int, string> AsiDealMotiveIdToAsiDealMotiveName =>
        new Dictionary<int, string>
        {
            { 1, "излиза от инвестицията" },
            { 2, "разделяне на имота" },
            { 3, "необходимост от пари" },
            { 4, "погасяване на кредит" },
            { 5, "продава, за да купи друг имот" },
            { 6, "преместване" },
            { 7, "реализация на продукт" },
            { 8, "друго" },
            { 9, "за инвестиция" },
            { 10, "за бизнес" },
            { 11, "живее под наем" },
            { 12, "купува за живеене" },
            { 13, "местене в друг град" },
            { 14, "сменя жилището" },
            { 15, "друго" },
        };
}