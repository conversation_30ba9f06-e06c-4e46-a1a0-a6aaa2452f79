namespace RealtoCrm.DataImporter.Services.Models;

using System.Collections.Generic;
using AutoMapper;
using Newtonsoft.Json;
using RealtoCrm.Addresses;
using RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Addresses;
using RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Companies;
using RealtoCrm.Mapping;
using Services.Mappings.DataResolvers;

public class EstateForOfferUesImportModel : IMapTo<Address>, IMapExplicitly
{
    [JsonProperty("id")]
    public int Id { get; set; }

    [JsonProperty("estate_type_id")]
    public int? EstateTypeId { get; set; }

    [JsonProperty("estate_kind_id")]
    public int? EstateKindId { get; set; }

    [JsonProperty("bedrooms")]
    public int? BedroomsCount { get; set; }

    [JsonProperty("bathrooms")]
    public int? BathroomsCount { get; set; }

    [JsonProperty("rooms")]
    public int? RoomsCount { get; set; }

    [JsonProperty("project_id")]
    public object ProjectId { get; set; } = default!;

    [JsonProperty("country_id")]
    public int? CountryId { get; set; }

    [JsonProperty("region_id")]
    public int? RegionId { get; set; }

    [JsonProperty("municipality_id")]
    public int? MunicipalityId { get; set; } = default!;

    [JsonProperty("location_id")]
    public int? LocationId { get; set; }

    [JsonProperty("quarter_id")]
    public int? QuarterId { get; set; }

    [JsonProperty("upi")]
    public int? Upi { get; set; }

    [JsonProperty("upi_id")]
    public object UpiId { get; set; } = default!;

    [JsonProperty("upi_number")]
    public object UpiNumber { get; set; } = default!;

    [JsonProperty("upi_quarter")]
    public object UpiQuarter { get; set; } = default!;

    [JsonProperty("upi_identifier")]
    public object UpiIdentifier { get; set; } = default!;

    [JsonProperty("street_id")]
    public object StreetId { get; set; } = default!;

    [JsonProperty("street_number")]
    public string? StreetNumber { get; set; }

    [JsonProperty("block")]
    public string? Block { get; set; }

    [JsonProperty("entrance")]
    public string? Entrance { get; set; }

    [JsonProperty("floor")]
    public string? Floor { get; set; }

    [JsonProperty("floors")]
    public int? Floors { get; set; }

    [JsonProperty("number_type")]
    public object NumberType { get; set; } = default!;

    [JsonProperty("apartment")]
    public string? Apartment { get; set; }

    [JsonProperty("square")]
    public double? Square { get; set; }

    [JsonProperty("square_yard")]
    public float? SquareYard { get; set; }

    [JsonProperty("square_buildup")]
    public object SquareBuildup { get; set; } = default!;

    [JsonProperty("square_from")]
    public object SquareFrom { get; set; } = default!;

    [JsonProperty("square_to")]
    public object SquareTo { get; set; } = default!;

    [JsonProperty("square_total")]
    public object SquareTotal { get; set; } = default!;

    [JsonProperty("building_year")]
    public int? BuildingYear { get; set; }

    [JsonProperty("construction_id")]
    public int? ConstructionId { get; set; }

    [JsonProperty("latitude")]
    public string? Latitude { get; set; }

    [JsonProperty("longitude")]
    public string? Longitude { get; set; }

    [JsonProperty("clients")]
    public List<UesOfferClient> Clients { get; set; } = default!;

    public void RegisterMappings(IProfileExpression mapper)
        => mapper.CreateMap<EstateForOfferUesImportModel, Address>()
            .ForMember(
                dest => dest.Id,
                opt => opt.Ignore())
            .ForMember(
                dest => dest.Country,
                opt => opt.MapFrom<CountryValueResolver>())
            .ForMember(
                dest => dest.PopulatedPlaceId,
                opt => opt.Ignore())
            .ForMember(
                dest => dest.PopulatedPlace,
                opt => opt.MapFrom<PopulatedPlaceValueResolver>())
            .ForMember(
                dest => dest.District,
                opt => opt.MapFrom<DistrictValueResolver>())
            .ForMember(
                dest => dest.DistrictId,
                opt => opt.Ignore())
            .ForMember(
                dest => dest.Municipality,
                opt => opt.MapFrom<MunicipalityValueResolver>())
            .ForMember(
                dest => dest.MunicipalityId,
                opt => opt.Ignore())
            .ForMember(
                dest => dest.Province,
                opt => opt.MapFrom<ProvinceValueResolver>())
            .ForMember(
                dest => dest.Street,
                opt => opt.MapFrom<StreetValueResolver>())
            .ForMember(
                dest => dest.StreetId,
                opt => opt.Ignore()
            )
            .ForMember(dest => dest.BlockNumber, opt =>
            {
                opt.PreCondition(src => string.IsNullOrEmpty(src.Block) || src.Block.Length <= 10);
                opt.MapFrom(src => src.Block);
            })
            .ForMember(dest => dest.StreetNumber, opt =>
            {
                opt.PreCondition(src => string.IsNullOrEmpty(src.StreetNumber) || src.StreetNumber.Length <= 10);
                opt.MapFrom(src => src.StreetNumber);
            })
            .ForMember(dest => dest.EntranceNumber, opt =>
            {
                opt.PreCondition(src => string.IsNullOrEmpty(src.Entrance) || src.Entrance.Length <= 10);
                opt.MapFrom(src => src.Entrance);
            })
            .ForMember(dest => dest.FloorNumber, opt =>
            {
                opt.PreCondition(src => string.IsNullOrEmpty(src.Floor) || src.Floor.Length <= 10);
                opt.MapFrom(src => src.Floor);
            })
            .ForMember(dest => dest.ApartmentNumber, opt =>
            {
                opt.PreCondition(src => string.IsNullOrEmpty(src.Apartment) || src.Apartment.Length <= 10);
                opt.MapFrom(src => src.Apartment);
            })
            .ForMember(
                dest => dest.Tenant,
                opt => opt.MapFrom<TenantValueResolver<EstateForOfferUesImportModel, Address>>());
}