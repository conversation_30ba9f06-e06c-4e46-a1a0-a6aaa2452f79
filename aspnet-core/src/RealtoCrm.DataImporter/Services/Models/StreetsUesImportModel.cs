using System;
using AutoMapper;
using Newtonsoft.Json;
using RealtoCrm.Mapping;
using RealtoCrm.Nomenclatures;

namespace RealtoCrm.DataImporter.Services.Models;

public class StreetsUesImportModel : IMapFrom<Street>, IMapExplicitly
{
    public int Id { get; set; }

    public string? Name { get; set; }
    
    [JsonProperty("location_id")]
    public int? LocationId { get; set; }
    
    [JsonProperty("is_visible")]
    public int? IsVisible { get; set; }
    
    public int? Deleted { get; set; }
    
    [JsonProperty("not_verified")]
    public int? NotVerified { get; set; }
    
    public string? CreatedAt { get; set; }

    public string? UpdatedAt { get; set; }
    
    public void RegisterMappings(IProfileExpression mapper)
        => mapper
            .CreateMap<StreetsUesImportModel, Street>()
            .ForMember(dest => dest.Id, cfg => cfg
                .Ignore())
            .ForMember(dest => dest.Name, cfg => cfg
                .MapFrom(src => src.Name))
            .ForMember(dest => dest.IsDeleted, cfg => cfg
                .MapFrom(src => src.Deleted == 1))
            .ForMember(dest => dest.IsActive, cfg => cfg
                .MapFrom(src => src.IsVisible == 1))
            .ForMember(dest => dest.IsVerified, cfg => cfg
                .MapFrom(src => src.NotVerified != 1))
            .ForMember(dest => dest.Name, cfg => cfg
                .MapFrom(src => src.Name))
            .ForMember(dest => dest.CreationTime, cfg => cfg
                .MapFrom(src =>
                    string.IsNullOrEmpty(src.CreatedAt) ? DateTime.MinValue : DateTime.Parse(src.CreatedAt)))
            .ForMember(dest => dest.LastModificationTime, cfg => cfg
                .MapFrom(src => string.IsNullOrEmpty(src.UpdatedAt) ? (DateTime?)null : DateTime.Parse(src.UpdatedAt)))
            .ForMember(dest => dest.IsActive, cfg => cfg
                .MapFrom(src => true))
            .ForMember(dest => dest.PopulatedPlaceId, cfg => cfg
                .MapFrom(src => src.LocationId));
}