namespace RealtoCrm.DataImporter.Services.Models;

using System;
using System.Linq;
using AutoMapper;
using Newtonsoft.Json;
using Companies;
using RealtoCrm.ContactDetails.Models;
using Mapping;
using static Constants.ImporterConstants;

public class DepartmentsAsiImportModel : IMapFrom<Department>, IMapExplicitly
{
    public int Id { get; set; }

    public string? Name { get; set; }

    [JsonProperty("parent_id")]
    public int? ParentId { get; set; }

    [JsonProperty("country_id")]
    public int? CountryId { get; set; }

    [JsonProperty("region_id")]
    public int? RegionId { get; set; }

    [JsonProperty("municipality_id")]
    public int? MunicipalityId { get; set; }

    [JsonProperty("location_id")]
    public int? LocationId { get; set; }

    [JsonProperty("quarter_id")]
    public int? QuarterId { get; set; }

    [JsonProperty("street_id")]
    public int? StreetId { get; set; }

    public int? Visible { get; set; }

    public void RegisterMappings(IProfileExpression mapper)
        => mapper
            .CreateMap<DepartmentsAsiImportModel, Department>()
            .ForMember(dest => dest.Name, cfg => cfg
                .MapFrom(src => src.Name ?? string.Empty))
            .ForMember(dest => dest.IsActive, cfg => cfg
                .MapFrom(src => src.Visible != 0))
            .ForMember(dest => dest.CompanyId, cfg => cfg
                .MapFrom(src => AddressCompanyId))
            .ForMember(dest => dest.TenantId, cfg => cfg
                .MapFrom(src => AddressTenantId))
            .ForMember(dest => dest.ContactDetails, cfg => cfg
                .MapFrom(src => Enum.GetValues<ContactDetailId>()
                    .Select(cd => new DepartmentContactDetail
                    {
                        ContactDetailId = (int)cd,
                        Value = string.Empty
                    })))
            .ForMember(dest => dest.Id, cfg => cfg
                .Ignore())
            .ForMember(dest => dest.Teams, cfg => cfg
                .Ignore());
}