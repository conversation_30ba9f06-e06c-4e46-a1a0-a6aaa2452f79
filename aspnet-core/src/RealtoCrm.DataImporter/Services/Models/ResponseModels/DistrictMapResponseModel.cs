using RealtoCrm.DataImporter.Services.Models.ResponseModels.Interfaces;
using RealtoCrm.Mapping;
using RealtoCrm.Nomenclatures;

namespace RealtoCrm.DataImporter.Services.Models.ResponseModels;

public class DistrictMapResponseModel : IHaveAsiId, IMapFrom<DistrictMapping>
{
    public int AsiId { get; set; }
    
    public int? AdminId { get; set; }
    
    public int? EaId { get; set; }

    public int DistrictId { get; set; }
}