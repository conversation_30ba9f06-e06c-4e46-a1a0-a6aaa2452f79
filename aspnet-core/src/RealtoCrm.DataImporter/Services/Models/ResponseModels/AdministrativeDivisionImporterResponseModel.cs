using RealtoCrm.Mapping;
using RealtoCrm.Nomenclatures;

namespace RealtoCrm.DataImporter.Services.Models.ResponseModels;

public class AdministrativeDivisionImporterResponseModel : ImporterResponseModel, IMapFrom<Province>,
    IMapFrom<Municipality>, IMapFrom<PopulatedPlace>
{
    public string Code { get; set; } = default!;

    public string Ekatte { get; set; } = default!;
    
    public int? ProvinceId { get; set; }
}