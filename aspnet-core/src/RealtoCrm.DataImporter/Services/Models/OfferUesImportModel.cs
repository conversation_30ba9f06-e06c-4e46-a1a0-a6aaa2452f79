namespace RealtoCrm.DataImporter.Services.Models;

using System.Collections.Generic;
using System.Linq;
using System.Net;
using AutoMapper;
using Estates;
using Mapping;
using Newtonsoft.Json;
using Offers;
using Services.Mappings.DataResolvers;
using Services.Mappings.DataResolvers.Archive;
using Services.Mappings.DataResolvers.Companies;
using Services.Mappings.DataResolvers.Estates;
using Services.Mappings.DataResolvers.Offers;

public class OfferUesImportModel : IMapTo<Offer>, IMapTo<Estate>, IMapExplicitly
{
    [JsonProperty("id")]
    public int OfferUesId { get; set; }

    [JsonProperty("estate_id")]
    public int? EstateId { get; set; }

    [JsonProperty("user_id")]
    public int? UserId { get; set; }

    public int Brand { get; set; }

    [JsonProperty("agency_id")]
    public long? AgencyId { get; set; }

    [JsonProperty("agency_consultant_id")]
    public object AgencyConsultantId { get; set; } = default!;

    [JsonProperty("office_id")]
    public int? OfficeId { get; set; }

    [JsonProperty("operation_id")]
    public int? OperationId { get; set; }

    [JsonProperty("archive_reason_id")]
    public int? ArchiveReasonUesId { get; set; }
    
    [JsonProperty("lifestyle")]
    public ICollection<UesLifeStyle>? LifeStyles { get; set; }

    [JsonProperty("archive_by")]
    public int? ArchiveBy { get; set; }

    [JsonProperty("date_to_call")]
    public string? DateToCall { get; set; }

    [JsonProperty("archive_price")]
    public object ArchivePrice { get; set; } = default!;

    [JsonProperty("archive_price_unknown")]
    public int ArchivePriceUnknown { get; set; }

    [JsonProperty("archive_description")]
    public object ArchiveDescription { get; set; } = default!;

    [JsonProperty("archive_date_to")]
    public string? ArchiveDateTo { get; set; }

    [JsonProperty("status")]
    public int? Status { get; set; }

    [JsonProperty("price")]
    public decimal? Price { get; set; }

    [JsonProperty("currency_id")]
    public int? CurrencyId { get; set; }

    [JsonProperty("price_spa")]
    public decimal? PriceSpa { get; set; }

    [JsonProperty("currency_spa_id")]
    public int? CurrencySpaId { get; set; }

    [JsonProperty("price_square")]
    public decimal? PriceSquare { get; set; }

    [JsonProperty("currency_square_id")]
    public int? CurrencySquareId { get; set; }

    [JsonProperty("without_vat")]
    public int? WithoutVat { get; set; }

    [JsonProperty("price_hidden")]
    public int PriceHidden { get; set; }

    [JsonProperty("price_previous")]
    public object PricePrevious { get; set; } = default!;

    [JsonProperty("price_previous_date")]
    public object PricePreviousDate { get; set; } = default!;

    [JsonProperty("source_id")]
    public object SourceId { get; set; } = default!;

    [JsonProperty("source_detail_id")]
    public int? SourceDetailId { get; set; }

    [JsonProperty("source_name")]
    public string? SourceName { get; set; }

    [JsonProperty("source_detail")]
    public string? SourceDetailName { get; set; }

    [JsonProperty("source_detail_description")]
    public string SourceDetailDescription { get; set; } = default!;

    [JsonProperty("internet_site_id")]
    public object InternetSiteId { get; set; } = default!;

    [JsonProperty("person_internet_site_id")]
    public object PersonInternetSiteId { get; set; } = default!;

    [JsonProperty("visited_office_id")]
    public object VisitedOfficeId { get; set; } = default!;

    [JsonProperty("recommended_by")]
    public int? RecommendedBy { get; set; }

    [JsonProperty("recommended_by_user_id")]
    public object RecommendedByUserId { get; set; } = default!;

    [JsonProperty("bedrooms")]
    public int? Bedrooms { get; set; }

    [JsonProperty("bathrooms")]
    public int? Bathrooms { get; set; }

    [JsonProperty("rooms")]
    public int? Rooms { get; set; }

    [JsonProperty("terraces")]
    public int? Terraces { get; set; }

    [JsonProperty("garages")]
    public object Garages { get; set; } = default!;

    [JsonProperty("furniture_id")]
    public int? FurnitureId { get; set; }

    [JsonProperty("condition_id")]
    public int? ConditionId { get; set; }

    [JsonProperty("completion_id")]
    public int? CompletenessId { get; set; }

    [JsonProperty("deal_motive_id")]
    public int? DealMotiveId { get; set; }

    [JsonProperty("elevator")]
    public int? Elevator { get; set; }

    [JsonProperty("garage")]
    public int? Garage { get; set; }

    [JsonProperty("new_building")]
    public object NewBuilding { get; set; } = default!;

    [JsonProperty("attic")]
    public int? Attic { get; set; }

    [JsonProperty("basement")]
    public int? Basement { get; set; }

    [JsonProperty("equipped")]
    public object Equipped { get; set; } = default!;

    [JsonProperty("south_exposure")]
    public int? SouthExposure { get; set; }

    [JsonProperty("youtube")]
    public string Youtube { get; set; } = default!;

    [JsonProperty("panorama")]
    public object Panorama { get; set; } = default!;

    [JsonProperty("recommended")]
    public int? Recommended { get; set; }

    [JsonProperty("luxury")]
    public int? Luxury { get; set; }

    [JsonProperty("contract_type")]
    public string ContractType { get; set; } = default!;

    [JsonProperty("private")]
    public int? Private { get; set; }

    [JsonProperty("convenient_time")]
    public object ConvenientTime { get; set; } = default!;

    [JsonProperty("sell_why")]
    public object SellWhy { get; set; } = default!;

    [JsonProperty("sell_why_other")]
    public object SellWhyOther { get; set; } = default!;

    [JsonProperty("sell_since_date")]
    public object SellSinceDate { get; set; } = default!;

    [JsonProperty("sell_since_date_extend")]
    public object SellSinceDateExtend { get; set; } = default!;

    [JsonProperty("buy_after_sell")]
    public object BuyAfterSell { get; set; } = default!;

    [JsonProperty("exit_code")]
    public object ExitCode { get; set; } = default!;

    [JsonProperty("shade")]
    public int Shade { get; set; }

    [JsonProperty("in_website")]
    public int InWebsite { get; set; }

    [JsonProperty("in_website_manager")]
    public int InWebsiteManager { get; set; }

    [JsonProperty("not_in_export")]
    public int NotInExport { get; set; }

    [JsonProperty("client_report")]
    public int ClientReport { get; set; }

    [JsonProperty("description")]
    public string Description { get; set; } = default!;

    [JsonProperty("location_info")]
    public object LocationInfo { get; set; } = default!;

    [JsonProperty("building_info")]
    public object BuildingInfo { get; set; } = default!;

    [JsonProperty("advantages_info")]
    public object AdvantagesInfo { get; set; } = default!;

    [JsonProperty("created_at")]
    public string CreatedAt { get; set; } = default!;

    [JsonProperty("updated_at")]
    public string UpdatedAt { get; set; } = default!;

    [JsonProperty("created_by")]
    public int? CreatedBy { get; set; }

    [JsonProperty("updated_by")]
    public int? UpdatedBy { get; set; }

    [JsonProperty("estate")]
    public EstateForOfferUesImportModel? Estate { get; set; }

    [JsonProperty("estate_marketing_types")]
    public List<object> EstateMarketingTypes { get; set; } = default!;

    [JsonProperty("heating_types")]
    public List<object> HeatingTypes { get; set; } = default!;

    [JsonProperty("comments")]
    public List<object> Comments { get; set; } = default!;

    [JsonProperty("contract")]
    public ContractForOfferImportModel? Contract { get; set; }

    [JsonProperty("meta")]
    public Meta? Meta { get; set; }

    [JsonProperty("offer_key")]
    public int? HasKey { get; set; }

    [JsonProperty("exposureIds")]
    public int[] ExposureIds { get; set; } = default!;

    [JsonProperty("heatingIds")]
    public int[] HeatingIds { get; set; } = default!;

    [JsonProperty("parking_id")]
    public int? ParkingId { get; set; }

    [JsonProperty("state_id")]
    public int? StateId { get; set; }

    [JsonProperty("deleted")]
    public int? IsDeletedImportEntry { get; set; }

    [JsonProperty("rental_term_id")]
    public int? RentalTermId { get; set; }

    [JsonProperty("plot_purpose")]
    public int? PlotPurposeId { get; set; }
    
    [JsonProperty("house_type")]
    public int? HouseTypeAdminId { get; set; }

    public void RegisterMappings(IProfileExpression mapper)
    {
        this.RegisterToOfferMappings(mapper);
        RegisterToEstateMappings(mapper);
        RegisterToOfferDetail(mapper);
        RegisterToEstateDetail(mapper);
    }

    // Cosher: Offer -> Estate -> Address
    // Ues: Offer -> Estate containing the Address

    private void RegisterToOfferMappings(IProfileExpression mapper)
    {
        mapper.CreateMap<OfferUesImportModel, Offer>()
            .ForMember(dest => dest.Name, opt =>
                opt.MapFrom(src => src.Meta.Name))
            .ForMember(dest => dest.EstateDescription, opt =>
                opt.MapFrom(src => WebUtility.HtmlDecode(src.Meta.Description)))
            .ForMember(dest => dest.DistributionDescription, opt =>
                opt.MapFrom(src => WebUtility.HtmlDecode(src.Meta.Distribution)))
            .ForMember(dest => dest.AdvantagesDescription, opt =>
                opt.MapFrom(src => src.Meta.OfferEstateAdvantages != null
                    ? string.Join("\n", src.Meta.OfferEstateAdvantages
                        .Where(a => !string.IsNullOrWhiteSpace(a.Advantage))
                        .Select(a => a.Advantage))
                    : null))
            .ForMember(dest => dest.DirectionDescription, opt =>
                opt.MapFrom(src => src.Meta.Landmark))
            .ForMember(dest => dest.CreationTime, opt =>
                opt.MapFrom(new DateValueResolver<OfferUesImportModel, Offer>(
                    nameof(CreatedAt)
                )))
            .ForMember(dest => dest.LastModificationTime, opt =>
                opt.MapFrom(new DateValueResolver<OfferUesImportModel, Offer>(
                    nameof(UpdatedAt)
                )))
            .ForMember(dest => dest.Attic, opt =>
                opt.Ignore())
            .ForMember(dest => dest.PriceOnRequest, opt =>
                opt.MapFrom(src => src.PriceHidden != 0))
            .ForMember(dest => dest.Furniture, opt =>
                opt.MapFrom<FurnitureValueResolver>())
            .ForMember(dest => dest.Vat, opt =>
                opt.MapFrom<VatValueResolver>())
            .ForMember(dest => dest.Price, opt =>
                opt.MapFrom(new MoneyValueResolver<OfferUesImportModel, Offer>(
                    nameof(Price),
                    nameof(CurrencyId),
                    false
                )))
            .ForMember(dest => dest.PriceSpa, opt =>
                opt.MapFrom(new NullableMoneyValueResolver<OfferUesImportModel, Offer>(
                    nameof(PriceSpa),
                    nameof(CurrencySpaId),
                    false
                )))
            .ForMember(dest => dest.SquareMetrePrice, opt =>
                opt.MapFrom(new NullableMoneyValueResolver<OfferUesImportModel, Offer>(
                    nameof(PriceSquare),
                    nameof(CurrencySquareId),
                    false
                )))
            .ForMember(
                dest => dest.Estate,
                opt =>
                    opt.MapFrom(src => src))
            .ForMember(dest => dest.DealMotive, opt => opt.MapFrom<DealMotiveValueResolver>())
            .ForMember(dest => dest.OperationType, opt =>
                opt.MapFrom<OperationTypeValueResolver>())
            .ForMember(
                dest => dest.ContractType,
                opt => opt.MapFrom<ContractTypeValueResolver>())
            .ForMember(
                dest => dest.ContractTypeId,
                opt => opt.Ignore())
            .ForMember(c => c.OfferMapping, opt =>
                opt.MapFrom(cm => new OfferMapping
                {
                    AdminId = cm.OfferUesId
                }))
            .ForMember(
                dest => dest.Tenant,
                opt => opt.MapFrom<TenantValueResolver<OfferUesImportModel, Offer>>())
            .ForMember(
                dest => dest.SourceCategory,
                opt => opt.MapFrom<SourceCategoryValueResolver>())
            .ForMember(
                dest => dest.Client,
                opt => opt.MapFrom<ClientValueResolver>())
            .ForMember(
                dest => dest.Employee,
                opt => opt.MapFrom<EmployeeValueResolver>())
            .ForMember(
                dest => dest.ExternalAgency,
                opt => opt.MapFrom<ExternalAgencyValueResolver>())
            .ForMember(c => c.OfferMapping, opt => opt.MapFrom(cm => new OfferMapping
            {
                AdminId = cm.OfferUesId
            }))
            .ForMember(
                dest => dest.OfferDetail, opt =>
                    opt.MapFrom(src => src))
            .ForMember(dest => dest.OfferStatus, opt =>
                opt.MapFrom<OfferStatusValueResolver>())
            .ForMember(dest => dest.ArchiveReason, opt =>
                opt.MapFrom<ArchiveReasonValueResolver>())
            .ForMember(dest => dest.ArchiveDate, opt =>
                opt.MapFrom<ArchivedDateValueResolver>())
            .ForMember(dest => dest.DateToCall, opt =>
                opt.MapFrom<DateToCallValueResolver>())
            .ForMember(dest => dest.ArchivedBy, opt =>
                opt.MapFrom<ArchivedByValueResolver>())
            .ForMember(dest => dest.HasKey, opt =>
                opt.MapFrom<NullableBooleanValueResolver<OfferUesImportModel, Offer>, int?>(src => src.HasKey))
            .ForMember(
                dest => dest.Garages, opt =>
                    opt.MapFrom<GaragesValueResolver>())
            .ForMember(dest => dest.OfferLifestyles, opt =>
                opt.MapFrom<LifeStylesValueResolver>())
            .ForMember(
                dest => dest.SourceDetail,
                opt =>
                    opt.MapFrom<SourceDetailOfferValueResolver>());
    }

    private static void RegisterToEstateMappings(IProfileExpression mapper)
    {
        mapper.CreateMap<OfferUesImportModel, Estate>()
            .ForMember(
                dest => dest.Type,
                opt => opt.MapFrom<EstateTypeValueResolver>())
            .ForMember(
                dest => dest.Category,
                opt => opt.MapFrom<EstateCategoryValueResolver>())
            .ForMember(
                dest => dest.BedroomsCount,
                opt =>
                    opt.MapFrom(src => src.Bedrooms))
            .ForMember(
                dest => dest.RoomsCount,
                opt =>
                    opt.MapFrom(src => src.Rooms))
            .ForMember(
                dest => dest.BathroomsCount,
                opt =>
                    opt.MapFrom(src => src.Bathrooms))
            .ForMember(
                dest => dest.TerracesCount,
                opt =>
                    opt.MapFrom(src => src.Terraces))
            .ForMember(
                dest => dest.Area,
                opt =>
                    opt.MapFrom(src => src.Estate.Square))
            .ForMember(
                dest => dest.BuildingYear,
                opt =>
                    opt.MapFrom(src => src.Estate.BuildingYear))
            .ForMember(
                dest => dest.Floors,
                opt =>
                    opt.MapFrom(src => src.Estate.Floors))
            .ForMember(
                dest => dest.FacingDirections,
                opt =>
                    opt.MapFrom<FacingDirectionValueResolver>())
            .ForMember(
                dest => dest.HeatingSystems,
                opt =>
                    opt.MapFrom<HeatingSystemsValueResolver>())
            .ForMember(
                dest => dest.Latitude,
                opt =>
                    opt.MapFrom<NullableDoubleValueResolver<OfferUesImportModel, Estate>, string>(
                        src => (src.Estate == null ? null : src.Estate.Latitude) ?? string.Empty))
            .ForMember(
                dest => dest.Longitude,
                opt =>
                    opt.MapFrom<NullableDoubleValueResolver<OfferUesImportModel, Estate>, string>(
                        src => (src.Estate == null ? null : src.Estate.Longitude) ?? string.Empty))
            .ForMember(
                dest => dest.Elevator,
                opt =>
                    opt.MapFrom(src => src.Elevator.HasValue && src.Elevator.Value == 1))
            .ForMember(
                dest => dest.Condition,
                opt =>
                    opt.MapFrom<ConditionValueResolver>())
            .ForMember(
                dest => dest.ConstructionType,
                opt =>
                    opt.MapFrom<ConstructionTypeValueResolver>())
            .ForMember(
                dest => dest.CompletionLevel,
                opt =>
                    opt.MapFrom<CompletionLevelValueResolver>())
            .ForMember(
                dest => dest.Tenant,
                opt => opt.MapFrom<TenantValueResolver<OfferUesImportModel, Estate>>())
            .ForMember(
                dest => dest.Address,
                opt =>
                    opt.MapFrom(
                        src => src.Estate))
            .ForMember(
                dest => dest.EstateDetail, opt =>
                    opt.MapFrom(src => src));
    }

    private static void RegisterToOfferDetail(IProfileExpression mapper)
    {
        mapper.CreateMap<OfferUesImportModel, OfferDetail>()
            .ForMember(dest => dest.LeaseTerm, opt =>
                opt.MapFrom<LeaseTermValueResolver>())
            .ForMember(dest => dest.HasAttic, opt =>
                opt.MapFrom<NullableBooleanValueResolver<OfferUesImportModel, OfferDetail>, int?>(src => src.Attic))
            .ForMember(dest => dest.HasBasement, opt =>
                opt.MapFrom<NullableBooleanValueResolver<OfferUesImportModel, OfferDetail>, int?>(src => src.Basement))
            .ForMember(dest => dest.HasElevator, opt =>
                opt.MapFrom<NullableBooleanValueResolver<OfferUesImportModel, OfferDetail>, int?>(src => src.Elevator))
            .ForMember(dest => dest.IsGatedComplex,
                opt => opt.MapFrom(new EstateAdvantagesResolver(nameof(OfferDetail.IsGatedComplex))))
            .ForMember(dest => dest.HasFitness,
                opt => opt.MapFrom(new EstateAdvantagesResolver(nameof(OfferDetail.HasFitness))))
            .ForMember(dest => dest.HasPool,
                opt => opt.MapFrom(new EstateAdvantagesResolver(nameof(OfferDetail.HasPool))))
            .ForMember(dest => dest.HasView,
                opt => opt.MapFrom(new EstateAdvantagesResolver(nameof(OfferDetail.HasView))))
            .ForMember(dest => dest.IsPetFriendly,
                opt => opt.MapFrom(new EstateAdvantagesResolver(nameof(OfferDetail.IsPetFriendly))))
            .ForMember(dest => dest.HasGarden,
                opt => opt.MapFrom(new EstateAdvantagesResolver(nameof(OfferDetail.HasGarden))))
            .ForMember(dest => dest.HasPublicTransportNearby,
                opt => opt.MapFrom(new EstateAdvantagesResolver(nameof(OfferDetail.HasPublicTransportNearby))));
    }

    private static void RegisterToEstateDetail(IProfileExpression mapper)
    {
        mapper.CreateMap<OfferUesImportModel, EstateDetail>()
            .ForMember(dest => dest.ConstructionPurposeId, opt =>
                opt.MapFrom(src => src.PlotPurposeId))
            .ForMember(dest => dest.Estate, opt => opt.Ignore())
            .ForMember(dest => dest.HouseType, opt =>
                    opt.MapFrom<HouseTypeValueResolver>());
    }
}

public class UesOfferClient
{
    [JsonProperty("id")]
    public int ClientId { get; set; }

    [JsonProperty("pivot")]
    public Pivot Pivot { get; set; } = default!;
}

public class UesPivot
{
    [JsonProperty("estate_id")]
    public int EstateId { get; set; }

    [JsonProperty("client_id")]
    public int ClientId { get; set; }

    [JsonProperty("id")]
    public int Id { get; set; }
}

public class Meta
{
    [JsonProperty("name")]
    public string? Name { get; set; }

    [JsonProperty("description")]
    public string? Description { get; set; }

    [JsonProperty("distribution")]
    public string? Distribution { get; set; }

    [JsonProperty("estateAdvantages")]
    public List<EstateAdvantage>? EstateAdvantages { get; set; }

    [JsonProperty("offerEstateAdvantages")]
    public List<OfferEstateAdvantage>? OfferEstateAdvantages { get; set; }

    [JsonProperty("landmark")]
    public string? Landmark { get; set; }
}

public class OfferEstateAdvantage
{
    [JsonProperty("advantage")]
    public string? Advantage { get; set; }
}

public class EstateAdvantage
{
    [JsonProperty("id")]
    public int? Id { get; set; }

    [JsonProperty("name")]
    public string? Name { get; set; }
}

public class ContractForOfferImportModel
{
    [JsonProperty("type_id")]
    public int? TypeId { get; set; }

    [JsonProperty("co_exclusive")]
    public int? IsCoexclusive { get; set; }
}

public class UesLifeStyle
{
    [JsonProperty("id")]
    public int Id { get; set; }
}