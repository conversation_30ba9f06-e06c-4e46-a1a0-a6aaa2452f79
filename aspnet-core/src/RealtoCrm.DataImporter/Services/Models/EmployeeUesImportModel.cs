namespace RealtoCrm.DataImporter.Services.Models;

using System.Collections.Generic;
using Abp.Extensions;
using AutoMapper;
using Employees.Models;
using Helpers;
using Mapping;
using MultiTenancy.Models;
using Newtonsoft.Json;

public class EmployeeUesImportModel : IMapTo<UserWithEmployeeCreateRequestModel>, IMapExplicitly
{
    private const string DefaultPassword = "#LG&jFYGoifZhxfXH52a*S";
    private const int MinWorkPositionLength = 2;
    private const int MaxWorkPositionLength = 100;

    [JsonProperty("id")]
    public int AdminId { get; set; }

    [JsonProperty("name_bg")]
    public string? NameBg { get; init; }

    public string? Surname { get; init; }

    public string? Name { get; init; }

    public string Username { get; init; } = default!;

    public string Email { get; init; } = default!;

    [JsonProperty("mobile_phone")]
    public string? MobilePhone { get; init; }

    [JsonProperty("work_position_bg")]
    public string? WorkPosition { get; init; }

    public string? Sim { get; init; }
    
    [JsonProperty("is_visible")]
    public int IsVisible { get; set; }

    [JsonProperty("roleIds")]
    public List<int> RoleIds { get; set; }

    public void RegisterMappings(IProfileExpression mapper)
        => mapper
            .CreateMap<EmployeeUesImportModel, UserWithEmployeeCreateRequestModel>()
            .ForMember(m => m.User, cfg => cfg
                .MapFrom(m => new UserCreateRequestModel
                {
                    UserName = m.Username,
                    EmailAddress = m.Email,
                    Password = DefaultPassword,
                    ShouldChangePasswordOnNextLogin = true,
                    IsActive = m.IsVisible != 0 ? true : false,
                    RoleIds = m.RoleIds,
                }))
            
            .ForMember(m => m.Employee, cfg => cfg
                .MapFrom(m => new EmployeeRequestModel
                {
                    FirstName = !string.IsNullOrWhiteSpace(m.NameBg) ? NameHelper.GetFirstName(m.NameBg) : null,
                    LastName = !string.IsNullOrWhiteSpace(m.NameBg) && m.NameBg.Contains(' ')
                        ? NameHelper.GetLastName(m.NameBg)
                        : null,
                    MiddleName = string.IsNullOrWhiteSpace(m.Surname) ? null : m.Surname,
                    DisplayName = string.IsNullOrWhiteSpace(m.Name) ? null : m.NameBg,
                    PhoneNumber = m.MobilePhone.IsNullOrWhiteSpace() ? null : m.MobilePhone!.Replace(" ", ""),
                    SimCardNumber = m.Sim.IsNullOrWhiteSpace() ? null : m.Sim,
                    IsDeleted = m.IsVisible == 0 ? true : false,
                    WorkPosition = m.WorkPosition != null && m.WorkPosition.Length >= MinWorkPositionLength &&
                                   m.WorkPosition.Length <= MaxWorkPositionLength
                        ? m.WorkPosition
                        : null
                })
            );
}