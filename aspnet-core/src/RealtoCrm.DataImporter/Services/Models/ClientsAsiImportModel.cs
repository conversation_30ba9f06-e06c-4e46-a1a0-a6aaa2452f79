namespace RealtoCrm.DataImporter.Services.Models;

using System;
using System.Collections.Generic;
using Clients;
using Mapping;
using Newtonsoft.Json;
using System.Linq;
using AutoMapper;
using Constants;
using RealtoCrm.DataImporter.Services.Mappings;
using RealtoCrm.Comments;
using Services.Mappings.DataResolvers.Clients;

public class ClientsAsiImportModel : IMapFrom<Client>, IMapExplicitly
{
    [JsonProperty("id")]
    public int ClientAsiId { get; set; }

    [JsonProperty("type_id")]
    public string? TypeId { get; set; }

    public string Firstname { get; set; } = default!;

    public string Surname { get; set; } = default!;

    public string Lastname { get; set; } = default!;

    public string? Egn { get; set; }

    [JsonProperty("card_number")]
    public string? CardNumber { get; set; }

    [JsonProperty("card_region_id")]
    public int? CardRegionId { get; set; }

    [JsonProperty("card_date")]
    public DateTime? CardDate { get; set; }

    public int? Gender { get; set; }

    [JsonProperty("marital_status")]
    public int? MaritalStatus { get; set; }

    public DateTime? Birthdate { get; set; }

    public string? Company { get; set; }
    
    [JsonProperty("company_address")]
    public string? CompanyAddress { get; set; }
    
    [JsonProperty("company_imoteka")]
    public string? CompanyImoteka { get; set; }

    [JsonProperty("job_position")]
    public string? JobPosition { get; set; }

    public int? SourceId { get; set; }
    
    [JsonProperty("source_name")]
    public string? SourceName { get; set; }

    public int? SourceDetailId { get; set; }
    
    [JsonProperty("source_detail_name")]
    public string? SourceDetailName { get; set; }

    public string? SourceDetailDescription { get; set; }

    public int? InternetSiteId { get; set; }

    public int? PersonInternetSiteId { get; set; }

    public int? VisitedOfficeId { get; set; }

    public int? RecommendedBy { get; set; }

    public int? RecommendedByUserId { get; set; }

    [JsonProperty("branch_id")]
    public int? BranchId { get; set; }

    public int? CountryId { get; set; }

    public int? RegionId { get; set; }

    public int? MunicipalityId { get; set; }

    public int? LocationId { get; set; }

    public int? QuarterId { get; set; }

    public int? StreetId { get; set; }

    public string? StreetNumber { get; set; }

    public string? Block { get; set; }

    public string? Entrance { get; set; }

    public string? Floor { get; set; }

    public string? Apartment { get; set; }

    public string? CompanyName { get; set; }

    public string? Bulstat { get; set; }

    public int? ForeignerIdNumber { get; set; }

    public int? IsForeignCompany { get; set; }

    public string? ExternalAgent { get; set; }

    public int? ExternalAgentCertificate { get; set; }

    public string? ExternalAgentCertificateDate { get; set; }

    public int? AddressId { get; set; }

    public int? ImotekaId { get; set; }

    public string? AddressRobobrokerSubscribedAt { get; set; }

    public DateTime? AddressRobobrokerUnsubscribedAt { get; set; }

    public string? ImotekaRobobrokerSubscribedAt { get; set; }

    public DateTime? ImotekaRobobrokerUnsubscribedAt { get; set; }

    public DateTime? CreatedAt { get; set; }

    public string? UpdatedAt { get; set; }
    
    [JsonProperty("created_by")]

    public int? CreatedBy { get; set; }

    public int? UpdatedBy { get; set; }

    public List<CommentAsiImportModel>? Comments { get; set; }

    public List<Communication>? Communications { get; set; }

    [JsonProperty("client_relations")]
    public List<ClientRelation>? ClientRelations { get; set; }

    public ICollection<int> Employees { get; set; } = new List<int>();

    public void RegisterMappings(IProfileExpression mapper)
        => mapper
            .CreateMap<ClientsAsiImportModel, Client>()
            .ForMember(c => c.NationalityId, cfg => cfg
                .MapFrom(c => c.CountryId))
            .ForMember(c => c.TypeId, cfg =>
                cfg.MapFrom(src => src.TypeId))
            .ForMember(c => c.ClientMapping, cfg => cfg
                .MapFrom(c =>
               new ClientMapping {
                    AsiId = c.ClientAsiId,
                }))
            .ForMember(c => c.PersonalData, cfg => cfg
                .MapFrom(c => new ClientPersonalData
                {
                    BirthDate = c.Birthdate,
                    GenderId = c.Gender,
                    IdentificationNumber = c.Egn,
                    MaritalStatusId = c.MaritalStatus,
                    DocumentIssueDate = c.CardDate,
                    DocumentNumber = c.CardNumber,
                    FirstName = c.Firstname,
                    MiddleName = c.Surname,
                    LastName = c.Lastname,
                }))
              .ForMember(m => m.ClientsComments, cfg => cfg
                  .MapFrom(m => (m.Comments ?? new List<CommentAsiImportModel>())
                      .Select(c => new ClientComment
                      {
                          Comment = new Comment
                          {
                              Text = c.Comment ?? string.Empty,
                              CreationTime = c.CreatedAt ?? DateTime.MinValue,
                              CreatorUserId = c.UpdatedBy,
                              LastModificationTime = c.UpdatedAt,
                          },
                          CreationTime = c.CreatedAt ?? DateTime.MinValue,
                          CreatorUserId = c.UpdatedBy,
                          LastModificationTime = c.UpdatedAt,
                      })
                      .ToList()))
            .ForMember(c => c.ClientsSourceCategories, cfg => cfg.MapFrom<ClientSourceCategoriesValueResolver>())
            .ForMember(c => c.ClientsWorkplaces, cfg => cfg.Ignore())
            .ForMember(c => c.SourceDetails, cfg => cfg.Ignore())
            .ForMember(c => c.SearchViewings, cfg => cfg.Ignore())
            .ForMember(c => c.OfferViewings, cfg => cfg.Ignore())
            .ForMember(c => c.Offers, cfg => cfg.Ignore())
            .ForMember(c => c.ContactDetails, cfg => cfg.MapFrom(src =>
                src.Communications!.Any()
                    ? src.Communications!
                        .Where(c => c.TypeId.HasValue &&
                                    ClientAsiImporterMappings.AsiContactDetailIdToContactDetailId.ContainsKey(
                                        c.TypeId.Value))
                        .Select(c => new ClientContactDetail
                        {
                            ContactDetailId =
                                ClientAsiImporterMappings.AsiContactDetailIdToContactDetailId[c.TypeId!.Value],
                            Value = c.Name!,
                            IsDefault = c.IsDefault.HasValue && c.IsDefault.Value == 1
                        })
                        .ToList()
                    : new List<ClientContactDetail>()))
            .ForMember(c => c.ClientMapping, opt => opt.MapFrom(cm => new ClientMapping
            {
                AsiId = cm.ClientAsiId
            }))
            .ForMember(dest => dest.TenantId, cfg => cfg.MapFrom(src => 
                src.CompanyImoteka == "1" 
                    ? ImporterConstants.ImotekaTenantId 
                    : src.CompanyAddress == "1" 
                        ? ImporterConstants.AddressTenantId 
                        : (int?)null))
            .ForMember(c => c.ClientsTags, cfg => cfg.Ignore())
            .ForMember(c => c.ClientsCardTypes, cfg => cfg.Ignore())
            .ForMember(c => c.ProjectsClients, cfg => cfg.Ignore())
            .ForMember(c => c.OwnedProjects, cfg => cfg.Ignore())
            .ForMember(c => c.LegalEntity, cfg => cfg.Ignore())
            .ForMember(c => c.IsDeleted, cfg => cfg.Ignore())
            .ForMember(c => c.DeleterUserId, cfg => cfg.Ignore())
            .ForMember(c => c.DeletionTime, cfg => cfg.Ignore());
}

public class ClientRelation
{
    public int? Id { get; set; }

    [JsonProperty("client_id")]
    public int? ClientId { get; set; }

    [JsonProperty("client_relation_id")]
    public int? ClientRelationId { get; set; }

    public int? OfferId { get; set; }
    

    public int? ProjectId { get; set; }

    [JsonProperty("type_id")]
    public int? TypeId { get; set; }

    public DateTime? CreatedAt { get; set; }

    public DateTime? UpdatedAt { get; set; }

    public int? CreatedBy { get; set; }

    public int? UpdatedBy { get; set; }
}

public class CommentAsiImportModel
{
    public int? Id { get; set; }

    [JsonProperty("client_id")]
    public int ClientId { get; set; }

    public string? Comment { get; set; }

    [JsonProperty("created_at")]
    public DateTime? CreatedAt { get; set; }

    [JsonProperty("updated_at")]
    public DateTime? UpdatedAt { get; set; }

    [JsonProperty("created_by")]
    public int? CreatedBy { get; set; }

    [JsonProperty("updated_by")]
    public int? UpdatedBy { get; set; }
}

public class Communication
{
    public int? Id { get; set; }

    [JsonProperty("client_id")]
    public int? ClientId { get; set; }

    [JsonProperty("type_id")]
    public int? TypeId { get; set; }

    public string? Name { get; set; }

    public int? IsDefault { get; set; }

    public int? Archive { get; set; }

    public string? CreatedAt { get; set; }

    public string? UpdatedAt { get; set; }

    public int? CreatedBy { get; set; }

    public int? UpdatedBy { get; set; }
}