using AutoMapper;
using RealtoCrm.ExternalPortalModels.ImotBg;
using RealtoCrm.Mapping;

namespace RealtoCrm.DataImporter.Services.Models;

public class ImotBgCraionImportModel : ImotBgValueImportModel, IMapExplicitly
{
    public void RegisterMappings(IProfileExpression profile)
    {
        profile.CreateMap<ImotBgCraionImportModel, Craion>()
            .ForMember(dest => dest.ImotBgId, opt => opt.MapFrom(src => src.Optval))
            .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name));
    }
}