namespace RealtoCrm.DataImporter.Services.Models;

using System;
using Mapping;
using Newtonsoft.Json;
using Offers;

public class OfferImageUesImportModel : IMapTo<OfferImage>
{
    [JsonProperty("offer_id")]
    public int OfferId { get; init; }

    public string Url { get; init; } = default!;

    public bool? Sketch { get; init; }

    public int? Visible { get; init; }

    public int Position { get; init; }
    
    [JsonProperty("created_by")]
    public int? CreatedBy { get; init; }
    
    [JsonProperty("image_id")]
    public int ImageId { get; init; }
}