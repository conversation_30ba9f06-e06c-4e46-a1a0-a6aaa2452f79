namespace RealtoCrm.DataImporter.Services.Models;

using System;
using AutoMapper;
using Companies;
using Mapping;
using Newtonsoft.Json;
using Services.Mappings.DataResolvers.Companies;

public class TeamsUesImportModel : IMapTo<Team>, IMapExplicitly
{
    [JsonProperty("team_name")]
    public string? TeamName { get; set; }

    [JsonProperty("team_leader_id")]
    public int TeamLeaderId { get; set; }

    [JsonProperty("users")]
    public int[] Users { get; set; }


    public void RegisterMappings(IProfileExpression mapper)
        => mapper
            .CreateMap<TeamsUesImportModel, Team>()
            .ForMember(dest => dest.Name, cfg => cfg
                .MapFrom(src => src.TeamName ?? String.Empty))
            .ForMember(dest => dest.IsActive, cfg => cfg
                .MapFrom(src => true))
            .ForMember(
                dest => dest.Tenant,
                opt => opt
                    .MapFrom<TenantValueResolver<TeamsUesImportModel, Team>>())
            .ForMember(
                dest => dest.Company,
                opt => opt
                    .MapFrom<CompanyValueResolver<TeamsUesImportModel, Team>>())
            .ForMember(
                dest => dest.Department,
                opt => opt
                    .MapFrom<DepartmentValueResolver>());
}