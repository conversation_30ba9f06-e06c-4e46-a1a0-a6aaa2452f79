using System;
using AutoMapper;
using RealtoCrm.Mapping;
using RealtoCrm.Nomenclatures;

namespace RealtoCrm.DataImporter.Services.Models;

public class ProvinceAsiImportModel : IMapFrom<Province>, IMapExplicitly
{
    public int Id { get; set; }

    public int? CountryId { get; set; }

    public string Name { get; set; } = default!;

    public string? CreatedAt { get; set; }

    public string? UpdatedAt { get; set; }

    public void RegisterMappings(IProfileExpression mapper)
        => mapper
            .CreateMap<ProvinceAsiImportModel, Province>()
            .ForMember(dest => dest.Name, cfg => cfg
                .MapFrom(src => src.Name ?? string.Empty))
            .ForMember(dest => dest.CreationTime, cfg => cfg
                .MapFrom(src =>
                    string.IsNullOrEmpty(src.CreatedAt) ? DateTime.MinValue : DateTime.Parse(src.CreatedAt)))
            .ForMember(dest => dest.LastModificationTime, cfg => cfg
                .MapFrom(src => string.IsNullOrEmpty(src.UpdatedAt) ? (DateTime?)null : DateTime.Parse(src.UpdatedAt)))
            .ForMember(dest => dest.IsActive, cfg => cfg
                .MapFrom(src => true))
            .ForMember(dest => dest.Id, cfg => cfg
                .Ignore());
}