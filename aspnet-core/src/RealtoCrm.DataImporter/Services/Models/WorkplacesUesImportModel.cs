using AutoMapper;
using Newtonsoft.Json;
using RealtoCrm.Mapping;
using RealtoCrm.Nomenclatures;

namespace RealtoCrm.DataImporter.Services.Models;

public class WorkplacesUesImportModel: IMapTo<Workplace>, IMapFrom<Workplace>, IMapExplicitly
{
    [JsonProperty("id")]
    public int AdminId { get; set; }

    public string? Name { get; set; }

    public void RegisterMappings(IProfileExpression mapper)
        => mapper
            .CreateMap<WorkplacesUesImportModel, Workplace>()
            .ForMember(dest => dest.Name, cfg => cfg
                .MapFrom(src => src.Name ?? string.Empty))
            .ForMember(dest => dest.Description, cfg => cfg
                .MapFrom(src => "Default Description"))
            .ForMember(dest => dest.IsActive, cfg => cfg
                .MapFrom(src => true))
            .ForMember(dest => dest.ClientsWorkplaces, cfg => cfg
                .Ignore());
}