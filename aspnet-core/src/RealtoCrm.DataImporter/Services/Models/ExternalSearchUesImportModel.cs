using AutoMapper;
using Newtonsoft.Json;
using RealtoCrm.DataImporter.Services.Mappings.DataResolvers.ExternalAgencies;
using RealtoCrm.ExternalSearches;
using RealtoCrm.Mapping;

namespace RealtoCrm.DataImporter.Services.Models;

public class ExternalSearchUesImportModel : IMapTo<ExternalSearch>, IMapExplicitly
{
    public int Id { get; set; }

    [JsonProperty("agency_id")]
    public int? ExternalAgencyId { get; set; }

    public void RegisterMappings(IProfileExpression mapper)
        => mapper.CreateMap<ExternalSearchUesImportModel, ExternalSearch>()
            .ForMember(x => x.ExternalAgencyId, opt
                => opt.MapFrom<ExternalAgenciesValueResolver>())
            .ForMember(x => x.ExternalSearchesMapping, opt => opt.MapFrom(src => new ExternalSearchesMapping
            {
                AdminId = src.Id
            }))
            .ForMember(x => x.Id, opt => opt.Ignore())
            .ForMember(x => x.Name, opt => opt.MapFrom(src => string.Empty));
}