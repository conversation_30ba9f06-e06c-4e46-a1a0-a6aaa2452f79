namespace RealtoCrm.DataImporter.Services;

using Abp.Domain.Uow;
using Abp.EntityFrameworkCore;
using AutoMapper;
using Microsoft.Extensions.Configuration;

public abstract class BaseImotBgImporterService<TEntity, TModel>(
    Log log,
    IMapper mapper,
    IConfiguration configuration,
    IDbContextResolver dbContextResolver,
    IConnectionStringResolver connectionStringResolver,
    IJsonSerializerService<TModel> jsonSerializerService,
    IImportDocumentFetchService importDocumentFetchService,
    IAddressMappingService addressMappingService,
    IMapMoneyService mapMoneyService,
    IMapSourceCategoryService mapSourceCategoryService)
    : BaseImporterService<TEntity, TModel>(
        log,
        mapper,
        configuration,
        dbContextResolver,
        connectionStringResolver,
        jsonSerializerService,
        importDocumentFetchService,
        addressMappingService,
        mapMoneyService,
        mapSourceCategoryService)
    where TEntity : class
    where TModel : class
{
    protected string DefaultUrl => "https://api.imot.bg/import_api/dictionary";
    
    protected override string ApiKeyConfigurationKey => "Unused";
}