using Abp.Application.Services.Dto;
using Abp.DynamicEntityProperties;
using RealtoCrm.Mapping;

namespace RealtoCrm.DynamicEntityProperties.Dto;

public class DynamicEntityPropertyDto
    : EntityDto,
        IMapFrom<DynamicEntityProperty>
{
    public string EntityFullName { get; set; }

    public string DynamicPropertyName { get; set; }

    public int DynamicPropertyId { get; set; }

    public int? TenantId { get; set; }
}