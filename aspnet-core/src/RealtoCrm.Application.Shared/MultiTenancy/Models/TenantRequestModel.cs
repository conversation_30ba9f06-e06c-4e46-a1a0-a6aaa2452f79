namespace RealtoCrm.MultiTenancy.Models;

using AutoMapper;
using Companies;
using Mapping;

public class TenantRequestModel : IMapTo<Tenant>, IMapTo<Company>, IMapExplicitly
{
    public string Name { get; init; } = default!;

    public string? Bulstat { get; init; }

    public string? MaterialResponsiblePerson { get; init; }

    public bool IsActive { get; init; } = true;

    public virtual void RegisterMappings(IProfileExpression mapper)
        => mapper
            .CreateMap<TenantRequestModel, Tenant>()
            .ForMember(m => m.TenancyName, cfg => cfg
                .MapFrom(m => m.Name));
}