using System;
using Abp.Application.Services.Dto;
using Abp.Auditing;
using Abp.Domain.Entities;
using Abp.Domain.Entities.Auditing;
using RealtoCrm.Mapping;

namespace RealtoCrm.MultiTenancy.Dto;

public class TenantListDto
    : EntityDto,
        IPassivable,
        IHasCreationTime,
        IMapFrom<Tenant>
{
    public string TenancyName { get; set; }

    public string Name { get; set; }

    public string EditionDisplayName { get; set; }

    [DisableAuditing] public string ConnectionString { get; set; }

    public bool IsActive { get; set; }

    public DateTime CreationTime { get; set; }

    public DateTime? SubscriptionEndDateUtc { get; set; }

    public int? EditionId { get; set; }

    public bool IsInTrialPeriod { get; set; }
}