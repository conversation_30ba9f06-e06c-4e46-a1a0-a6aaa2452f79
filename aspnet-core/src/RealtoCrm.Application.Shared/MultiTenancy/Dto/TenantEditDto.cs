namespace RealtoCrm.MultiTenancy.Dto;

using System;
using System.ComponentModel.DataAnnotations;
using Abp.Application.Services.Dto;
using Abp.MultiTenancy;
using Mapping;

public class TenantEditDto
    : EntityDto,
        IReverseMap<Tenant>
{
    [Required]
    [StringLength(AbpTenantBase.MaxTenancyNameLength)]
    public string TenancyName { get; set; } = default!;

    [Required]
    [StringLength(TenantConsts.MaxNameLength)]
    public string Name { get; set; } = default!;

    public int? EditionId { get; set; }

    public bool IsActive { get; set; }

    public DateTime? SubscriptionEndDateUtc { get; set; }

    public bool IsInTrialPeriod { get; set; }
}