using System;
using Abp;
using Abp.Runtime.Validation;

namespace RealtoCrm.Configuration.Host.Dto;

public class UserLockOutSettingsEditDto: ICustomValidate
{
    public bool IsEnabled { get; set; }

    public int? MaxFailedAccessAttemptsBeforeLockout { get; set; }

    public int? DefaultAccountLockoutSeconds { get; set; }
        
    public void AddValidationErrors(CustomValidationContext context)
    {
        if (!this.IsEnabled)
        {
            return;
        }

        if (!this.MaxFailedAccessAttemptsBeforeLockout.HasValue)
        {
            throw new ArgumentNullException(nameof(this.MaxFailedAccessAttemptsBeforeLockout));
        }
            
        if (!this.DefaultAccountLockoutSeconds.HasValue)
        {
            throw new ArgumentNullException(nameof(this.DefaultAccountLockoutSeconds));
        }
    }
}