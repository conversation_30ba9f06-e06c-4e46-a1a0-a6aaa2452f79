namespace RealtoCrm.Configuration.Tenants.Dto;

public class ClientSettingsModel
{
    public bool ShowDocumentsTab { get; init; }

    public int RegisterOfferSellDaysSingleOffer { get; init; }

    public int RegisterOfferSellDaysOfferInProject { get; init; }

    public int RegisterOfferRentDays { get; init; }

    public int ReleaseOfferDaysForInfluenceSphereRecommendationExternalBrokerSource { get; init; }

    public int ReleaseOfferDaysForConsultingActivityFiledPotentialsCorporateClientsSource { get; init; }

    public int ReleaseOfferDaysForAdvertisementSource { get; init; }

    public int ReleaseOfferDaysForCommissionContract { get; init; }

    public int ReleaseOfferDaysForExclusiveContract { get; init; }

    public int ReleaseOfferDaysForProject { get; init; }

    public int ReleaseOfferDaysForUnfilledOfferWithContract { get; init; }
}