namespace RealtoCrm.Authorization.Accounts.Dto;

public class IsTenantAvailableOutput
{
    public TenantAvailabilityState State { get; set; }

    public int? TenantId { get; set; }

    public string ServerRootAddress { get; set; }

    public IsTenantAvailableOutput()
    {
            
    }

    public IsTenantAvailableOutput(TenantAvailabilityState state, int? tenantId = null)
    {
        this.State = state;
        this.TenantId = tenantId;
    }

    public IsTenantAvailableOutput(TenantAvailabilityState state, int? tenantId, string serverRootAddress)
    {
        this.State = state;
        this.TenantId = tenantId;
        this.ServerRootAddress = serverRootAddress;
    }
}