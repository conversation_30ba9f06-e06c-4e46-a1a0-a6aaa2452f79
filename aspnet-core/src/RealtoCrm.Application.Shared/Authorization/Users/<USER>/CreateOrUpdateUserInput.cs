namespace RealtoCrm.Authorization.Users.Dto;

using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

public class CreateOrUpdateUserInput
{
    [Required]
    public UserEditDto User { get; set; } = default!;

    [Required]
    public string[] AssignedRoleNames { get; set; } = default!;

    public bool SendActivationEmail { get; set; }

    public bool SetRandomPassword { get; set; }

    public List<long> OrganizationUnits { get; set; } = [];
}