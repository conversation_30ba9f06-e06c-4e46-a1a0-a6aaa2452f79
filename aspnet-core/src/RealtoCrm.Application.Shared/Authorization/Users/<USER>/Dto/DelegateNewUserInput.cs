using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using RealtoCrm.Authorization.Delegation;
using RealtoCrm.Mapping;

namespace RealtoCrm.Authorization.Users.Delegation.Dto;

public class CreateUserDelegationDto
    : IValidatableObject,
        IMapTo<UserDelegation>
{
    [Required] [Range(1, long.MaxValue)] public long TargetUserId { get; set; }

    [Required] public DateTime StartTime { get; set; }

    [Required] public DateTime EndTime { get; set; }

    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        if (this.StartTime > this.EndTime)
        {
            yield return new ValidationResult("StartTime of a user delegation operation can't be bigger than EndTime!");
        }
    }
}