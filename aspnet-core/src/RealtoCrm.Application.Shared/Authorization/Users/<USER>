namespace RealtoCrm.Authorization.Users;

using System.Collections.Generic;
using System.Threading.Tasks;
using Abp.Application.Services;
using Abp.Application.Services.Dto;
using Dto;

public interface IUserLinkAppService : IApplicationService
{
    Task LinkToUser(LinkToUserInput linkToUserInput);

    Task LinkToUsersAsync(LinkToUsersRequestModel request);

    Task<IEnumerable<LinkedUserResponseModel>> GetLinkedUsersAsync(long userId);

    Task<PagedResultDto<LinkedUserDto>> GetLinkedUsers(GetLinkedUsersInput input);

    Task<ListResultDto<LinkedUserDto>> GetRecentlyUsedLinkedUsers();

    Task UnlinkUser(UnlinkUserInput input);
}