namespace RealtoCrm.Authorization.Users.Dto;

using System.ComponentModel.DataAnnotations;
using Abp.Auditing;
using Abp.Authorization.Users;
using Abp.Domain.Entities;
using AutoMapper;
using Mapping;

public class UserEditDto
    : IPassivable,
        IReverseMap<User>,
        IMapExplicitly
{
    /// <summary>
    /// Set null to create a new user. Set user's Id to update a user
    /// </summary>
    public long? Id { get; set; }

    [Required]
    [StringLength(AbpUserBase.MaxUserNameLength)]
    public string UserName { get; set; } = default!;

    [Required]
    [EmailAddress]
    [StringLength(AbpUserBase.MaxEmailAddressLength)]
    public string EmailAddress { get; set; } = default!;

    // Not used "Required" attribute since empty value is used to 'not change password'
    [StringLength(AbpUserBase.MaxPlainPasswordLength)]
    [DisableAuditing]
    public string? Password { get; set; }

    public bool IsActive { get; set; }

    public bool ShouldChangePasswordOnNextLogin { get; set; }

    public virtual bool IsTwoFactorEnabled { get; set; }

    public virtual bool IsLockoutEnabled { get; set; }

    public void RegisterMappings(IProfileExpression mapper)
        => mapper
            .CreateMap<User, UserEditDto>()
            .ForMember(
                dto => dto.Password,
                options => options.Ignore())
            .ReverseMap()
            .ForMember(
                user => user.Password,
                options => options.Ignore());
}