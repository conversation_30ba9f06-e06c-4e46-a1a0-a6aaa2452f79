using System;
using Abp.Authorization;
using Abp.Runtime.Validation;
using RealtoCrm.Dto;

namespace RealtoCrm.Authorization.Users.Dto;

public class GetLoginAttemptsInput : PagedAndSortedInputDto, IGetLoginAttemptsInput, IShouldNormalize
{
    public string? Filter { get; set; }

    public DateTime? StartDate { get; set; }

    public DateTime? EndDate { get; set; }

    public AbpLoginResultType? Result { get; set; }

    public void Normalize()
    {
        if (string.IsNullOrEmpty(this.Sorting))
        {
            this.Sorting = "CreationTime DESC";
        }

        this.Filter = this.Filter?.Trim();
    }
}