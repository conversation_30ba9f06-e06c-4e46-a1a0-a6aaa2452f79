using Abp.Application.Services.Dto;

namespace RealtoCrm.Localization.Dto;

using Abp.Localization;
using RealtoCrm.Mapping;

public class ApplicationLanguageListDto
    : FullAuditedEntityDto,
        IMapFrom<ApplicationLanguage>
{
    public virtual int? TenantId { get; set; }
        
    public virtual string Name { get; set; }

    public virtual string DisplayName { get; set; }

    public virtual string Icon { get; set; }

    public bool IsDisabled { get; set; }
}