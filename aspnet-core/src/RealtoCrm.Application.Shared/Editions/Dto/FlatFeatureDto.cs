namespace RealtoCrm.Editions.Dto;

using Abp.Application.Features;
using RealtoCrm.Mapping;

public class FlatFeatureDto
    : IMapFrom<Feature>
{
    public string ParentName { get; set; }

    public string Name { get; set; }

    public string DisplayName { get; set; }

    public string Description { get; set; }

    public string DefaultValue { get; set; }

    public FeatureInputTypeDto InputType { get; set; }
}