using System.Collections.Generic;
using Abp.Runtime.Validation;

namespace RealtoCrm.Editions.Dto;

using Abp.UI.Inputs;
using RealtoCrm.Mapping;

//Mapped in CustomDtoMapper
public class FeatureInputTypeDto
    : IMapFrom<CheckboxInputType>,
        IMapFrom<SingleLineStringInputType>,
        IMapFrom<ComboboxInputType>
{
    public string Name { get; set; }

    public IDictionary<string, object> Attributes { get; set; }

    public IValueValidator Validator { get; set; }

    public LocalizableComboboxItemSourceDto ItemSource { get; set; }
}