using Abp.UI.Inputs;

namespace RealtoCrm.Editions.Dto;

using Abp.Application.Features;
using RealtoCrm.Mapping;

public class FlatFeatureSelectDto
    : IMapFrom<Feature>,
        IMapTo<Feature>
{
    public string ParentName { get; set; }

    public string Name { get; set; }

    public string DisplayName { get; set; }

    public string Description { get; set; }

    public string DefaultValue { get; set; }

    public IInputType InputType { get; set; }

    public string TextHtmlColor { get; set; }
}