using System;
using Abp.Application.Services.Dto;
using Abp.Events.Bus.Entities;
using Abp.EntityHistory;
using RealtoCrm.Mapping;

namespace RealtoCrm.Auditing.Dto;

public class EntityChangeListDto
    : EntityDto<long>,
        IMapFrom<EntityChange>
{
    public long? UserId { get; set; }

    public string UserName { get; set; }

    public DateTime ChangeTime { get; set; }

    public string EntityTypeFullName { get; set; }

    public EntityChangeType ChangeType { get; set; }

    public string ChangeTypeName => this.ChangeType.ToString();

    public long EntityChangeSetId { get; set; }
}