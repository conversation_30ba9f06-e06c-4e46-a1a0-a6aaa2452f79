namespace RealtoCrm.Expressions;

using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using Abp.Dependency;
using Abp.Domain.Entities;
using DataCrudModels;

public interface IExpressionsBuilder : ISingletonDependency
{
    Expression<Func<TEntity, bool>> BuildDataCrudFiltersExpression<TEntity>(
        ParameterExpression parameter,
        IEnumerable<DataFilter>? filters,
        Dictionary<string, FilterExpression> customFilterExpressions);

    IEnumerable<SortByExpressionDefinition<TEntity>> BuildDataCrudSortByExpression<TEntity, TPrimaryKey>(
        IEnumerable<DataSorter> sortBys)
        where TEntity : Entity<TPrimaryKey>;

    SortByExpressionDefinition<TEntity> BuildDistanceSortByExpression<TEntity, TValue>(
        Expression<Func<TEntity, TValue?>> valueSelector,
        TValue? referenceValue)
        where TValue : struct, IComparable<TValue>;

    FilterExpression BuildForCompanyName<TEntity>(ParameterExpression parameter);

}