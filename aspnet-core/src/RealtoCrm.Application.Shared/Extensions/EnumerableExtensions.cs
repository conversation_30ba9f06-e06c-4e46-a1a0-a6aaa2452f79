namespace RealtoCrm.Extensions;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

public static class EnumerableExtensions
{
    public static void ForEach<T>(this IEnumerable<T> enumerable, Action<T> action)
    {
        foreach (var item in enumerable)
        {
            action(item);
        }
    }

    public static void ForEach<T>(this IEnumerable<T> enumerable, Action<T, int> action)
    {
        var index = 0;
        foreach (var item in enumerable)
        {
            action(item, index);
            index++;
        }
    }

    public static async Task ForEachAsync<T>(this IEnumerable<T> source, Func<T, Task> action)
    {
        foreach (var item in source)
        {
            await action(item);
        }
    }

    public static async Task ForEachAsync<T>(this IEnumerable<T> source, Func<T, int, Task> action)
    {
        var index = 0;
        foreach (var item in source)
        {
            await action(item, index);
            index++;
        }
    }

    public static async Task ForEachAsync<T>(this IEnumerable<T> source, Action<T> action)
    {
        foreach (var item in source)
        {
            action(item);
        }
    }

    public static async Task ForEachAsync<T>(this IEnumerable<T> source, Action<T, int> action)
    {
        var index = 0;
        foreach (var item in source)
        {
            action(item, index);
            index++;
        }
    }

    public static async Task<IEnumerable<TResult>> SelectManyAsync<TSource, TResult>(this IEnumerable<Task<TSource>> source, Func<TSource, IEnumerable<TResult>> selector)
        => (await Task.WhenAll(source))
            .SelectMany(selector);

    public static async Task<IEnumerable<TResult>> SelectAsync<TSource, TResult>(this Task<IEnumerable<TSource>> source, Func<TSource, TResult> selector)
        => (await source)
            .Select(selector);

    public static async Task<List<T>> ToListAsync<T>(this Task<IEnumerable<T>> source)
        => (await source).ToList();

    public static async Task<ISet<T>> ToSetAsync<T>(this Task<IEnumerable<T>> source)
        => (await source).ToHashSet();

    public static async Task<IEnumerable<T>> WhereAsync<T>(this IEnumerable<Task<T>> source, Func<T, bool> predicate)
        => (await Task.WhenAll(source))
            .Where(predicate);

    public static async Task<IEnumerable<T>> WhenAllSequential<T>(this IEnumerable<Task<T>> source)
    {
        var result = new List<T>();
        foreach (var task in source)
        {
            result.Add(await task);
        }

        return result;
    }
}