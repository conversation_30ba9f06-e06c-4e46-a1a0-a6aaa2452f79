namespace RealtoCrm.Extensions;

using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;

public static class FormFileExtensions
{
    public static async Task<byte[]> ToBytesAsync(this IFormFile? file)
    {
        if (file is null || file.Length == 0)
        {
            return Array.Empty<byte>();
        }

        await using var fileStream = file.OpenReadStream();

        return await fileStream.ToBytesAsync();
    }
}