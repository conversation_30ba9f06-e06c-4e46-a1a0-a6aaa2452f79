namespace RealtoCrm.Extensions;

using RealtoCrm.Money;

public static class MoneyExtensions
{
    private const decimal EuroToLevBnbCourse = 0.511292M;
    private const decimal EuroToUsdBnbCourse = 0.92659087M;

    public static decimal ToCurrency(this decimal amount, Currency currency)
    {
        if (amount == 0)
        {
            return 0;
        }

        return currency switch
        {
            Currency.EUR => amount * EuroToLevBnbCourse,
            Currency.USD => amount * EuroToUsdBnbCourse,
            Currency.BGN => amount,
        };
    }
}