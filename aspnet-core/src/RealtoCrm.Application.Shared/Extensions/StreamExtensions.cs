namespace RealtoCrm.Extensions;

using System.IO;
using System.Threading.Tasks;

public static class StreamExtensions
{
    public static async Task<byte[]> ToBytesAsync(this Stream stream)
    {
        using var memoryStream = new MemoryStream();

        await stream.CopyToAsync(memoryStream);

        return memoryStream.ToArray();
    }

    public static MemoryStream ToMemoryStream(this byte[] bytes) => new(bytes);
}