using System.Threading.Tasks;
using Abp.Application.Services;
using Abp.Application.Services.Dto;
using RealtoCrm.Caching.Dto;

namespace RealtoCrm.Caching;

public interface ICachingAppService : IApplicationService
{
    ListResultDto<CacheDto> GetAllCaches();

    Task ClearCache(EntityDto<string> input);

    Task ClearPermissionsCache();

    Task ClearAllCaches();

    bool CanClearAllCaches();
}