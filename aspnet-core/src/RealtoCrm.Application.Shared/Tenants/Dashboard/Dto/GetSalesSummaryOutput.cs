using System.Collections.Generic;

namespace RealtoCrm.Tenants.Dashboard.Dto;

public class GetSalesSummaryOutput
{
    public GetSalesSummaryOutput(List<SalesSummaryData> salesSummary)
    {
        this.SalesSummary = salesSummary;
    }


    public int TotalSales { get; set; }

    public int Revenue { get; set; }

    public int Expenses { get; set; }

    public int Growth { get; set; }

    public List<SalesSummaryData> SalesSummary { get; set; }

}