namespace RealtoCrm.DataCrudModels;

public enum DataFilterOperator
{
    None = 1,
    Equals = 2,
    NotEquals = 3,
    Contains = 4,
    NotContains = 5,
    StartsWith = 6,
    EndsWith = 7,
    //GreaterThan
    Gt = 8,
    //GreaterThanOrEqual
    Gte = 9,
    //LessThan
    Lt = 10,
    //LessThanOrEqual
    Lte = 11,
    DateIs = 12,
    DateIsNot = 13,
    DateBefore = 14,
    DateAfter = 15,
}

public class DataFilter
{
    public string? Name { get; set; } = default!;

    public string? NameFixed => this.Name?[..1].ToUpper() + this.Name?[1..];
    
    public string? Value { get; set; } = default!;

    public DataFilterOperator? Operator { get; set; } = DataFilterOperator.None;
}
